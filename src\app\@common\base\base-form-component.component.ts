import { Directive, inject } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { Params } from '@angular/router';
import { ToastrService } from '@common/services/toastr/toastr.service';

@Directive()
export abstract class BaseFormComponent {
  model!: FormGroup;
  loadingRequest = false;

  constructor() {}

  abstract submit(params?: Params): void;

  onSubmit(params?: Params) {
    if (this.model.valid) {
      this.submit(params);
    } else {
      this.validateForm(this.model);
    }
  }

  validateForm(formGroup: FormGroup | FormArray) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field);
      control?.markAsDirty();
      control?.markAsTouched();
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.validateForm(control);
      }
    });
  }

  onClearForm() {
    this.model.reset();
  }
}
