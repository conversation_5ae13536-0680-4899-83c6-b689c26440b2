import { ScrollingModule } from '@angular/cdk/scrolling'
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { EqpDesignToolsModule } from '@design-tools/eqp-design-tools.module'
import { NbButtonModule, NbDialogModule } from '@nebular/theme'
import { MoneyPipe } from '@pipes/money/money.pipe'
import { SanitizePipe } from '@pipes/sanitize/sanitize.pipe'
import { TextMaskModule } from 'angular2-text-mask'

import { ConfirmationComponent } from './dialogs/confirmation/confirmation.component'
import { InfoComponent } from './dialogs/info/info.component'
import { ListaSelecaoComponent } from './dialogs/lista-selecao/lista-selecao.component'
import { DecreeSearchComponent } from './dialogs/decree-search/decree-search.component'
import { DecreeSearchAnnualGoalsComponent } from './dialogs/decree-search-annual-goals/decree-search-annual-goals.component'
import { DecreeSearchAnnualGoals2020Component } from './dialogs/decree-search-annual-goals-2020/decree-search-annual-goals-2020.component'
import { AccountPlanSearchComponent } from './dialogs/account-plan-search/account-plan-search.component'
import { EntitySearchComponent } from './dialogs/entity-search/entity-search.component'
import { PersonSearchComponent } from './dialogs/person-search/person-search.component'
import { ViewPdfComponent } from './dialogs/view-pdf/view-pdf.component'
import { CpfCnpjPipe } from './pipes/cpf-cnpj/cpf-cnpj.pipe'
import { SafeHtmlPipe } from './pipes/safe-html/safe-html.pipe'
import { TelefonePipe } from './pipes/telefone-pipe/telefone.pipe'
import { ModalConfirmarComponent } from '@dialogs/modal-confirmar/modal-confirmar.component'
import { EditorDocComponent } from './dialogs/editor-doc/editor-doc.component'
import { CustomDecreeSearchComponent } from './dialogs/custom-decree-search/custom-decree-search.component'
import { ViewReportDialogComponent } from './dialogs/view-report-dialog/view-report-dialog.component'

@NgModule({
  declarations: [
    InfoComponent,
    ConfirmationComponent,
    ListaSelecaoComponent,
    ViewPdfComponent,
    CpfCnpjPipe,
    TelefonePipe,
    SafeHtmlPipe,
    SanitizePipe,
    MoneyPipe,
    PersonSearchComponent,
    ModalConfirmarComponent,
    DecreeSearchComponent,
    DecreeSearchAnnualGoalsComponent,
    DecreeSearchAnnualGoals2020Component,
    EditorDocComponent,
    AccountPlanSearchComponent,
    EntitySearchComponent,
    CustomDecreeSearchComponent,
    ViewReportDialogComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NbButtonModule,
    ReactiveFormsModule,
    EqpDesignToolsModule,
    TextMaskModule,
    NbDialogModule.forRoot(),
    ScrollingModule,
  ],
  providers: [CpfCnpjPipe, TelefonePipe, SafeHtmlPipe, SanitizePipe, MoneyPipe],
  exports: [
    FormsModule,
    ReactiveFormsModule,
    NbButtonModule,
    EqpDesignToolsModule,
    TextMaskModule,
    InfoComponent,
    ConfirmationComponent,
    ListaSelecaoComponent,
    ModalConfirmarComponent,
    ViewPdfComponent,
    SafeHtmlPipe,
    CpfCnpjPipe,
    SanitizePipe,
    MoneyPipe,
  ],
})
export class CommonToolsModule {}
