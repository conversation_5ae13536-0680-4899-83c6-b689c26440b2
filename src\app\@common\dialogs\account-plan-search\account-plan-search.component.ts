import { Component, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef } from '@nebular/theme'
import { DxDataGridComponent } from 'devextreme-angular'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'

import { AccountPlanSearchService } from './account-plan-search.service'

@Component({
  selector: 'eqp-account-plan-search',
  templateUrl: './account-plan-search.component.html',
  styleUrls: ['./account-plan-search.component.scss'],
})
export class AccountPlanSearchComponent implements OnInit, OnDestroy {
  public loading: boolean = false

  @Input() tipoFluxoTransferenciaFinanceiraData: string

  @Input()
  public dialogTitle: string = 'Plano Contábil | Busca'
  public gridData: any
  public columnsTemplate: DxColumnInterface[] = []
  public selected: any[] = []

  private subscription: Subscription

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private service: AccountPlanSearchService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
    this.columnsTemplate = this.getColumnsTemplate()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private fetchGrid(): void {
    const isIngresso = this.tipoFluxoTransferenciaFinanceiraData === 'Ingresso'
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltroComposto(
        'uuid',
        `plano_contabil/paginado/analitica?isAnalytical=true`,
        10,
        `["codigo","startswith","${isIngresso ? '45' : '35'}"]`,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: '',
        dataField: 'uuid',
        width: 70,
        cellTemplate: 'checkedTemplate',
      },
      {
        caption: 'Conta Contábil',
        dataField: 'codigoReduzido',
        width: 100,
      },
      {
        caption: 'Código',
        dataField: 'codigo',
      },
      {
        caption: 'Nome',
        dataField: 'nome',
      },
      {
        caption: 'Analitica',
        dataField: 'analitica',
      },
    ]
    return template
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid) return true
    } else false
  }

  public confirm(): void {
    const decree = this.grid.instance.getSelectedRowsData()[0]
    this.dialogRef.close(decree)
  }

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
