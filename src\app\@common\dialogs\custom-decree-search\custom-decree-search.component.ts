import { Component, Input, OnInit, ViewChild } from '@angular/core'
import { DecreeSearchService } from '@common/dialogs/decree-search/decree-search.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef } from '@nebular/theme'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'

@Component({
  selector: 'eqp-custom-decree-search',
  templateUrl: './custom-decree-search.component.html',
  styleUrls: ['./custom-decree-search.component.scss'],
})
export class CustomDecreeSearchComponent implements OnInit {
  public loading: boolean = false

  @Input() public dialogTitle: string = 'Leis/Atos | Busca'
  @Input() public codigoEscopo: number[] = []
  @Input() public url: any

  public gridData: any
  public columnsTemplate: DxColumnInterface[] = []
  public selected: any[] = []

  private subscription: Subscription

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private service: DecreeSearchService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
    this.columnsTemplate = this.getColumnsTemplate()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private fetchGrid(): void {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      this.url
        ? this.url
        : 'estimativa_compensacao_renuncia_receita/scope_year',
      10,
    )
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: '',
        dataField: 'uuid',
        width: 70,
        cellTemplate: 'checkedTemplate',
      },
      {
        caption: 'Código',
        dataField: 'codigo',
        width: 100,
      },
      {
        caption: 'Tipo de Documento',
        dataField: 'tipoDocumento.tipoDocumentoTce.nome',
      },
      {
        caption: 'Número',
        dataField: 'numero',
      },
      {
        caption: 'Ano',
        dataField: 'ano',
      },
      {
        caption: 'Descrição',
        dataField: 'descricaoTipoDocumento',
      },
      {
        caption: 'Data',
        dataField: 'data',
      },
      {
        caption: 'Ano Inicial',
        dataField: 'anoInicialAplicacao',
      },
    ]
    return template
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid) return true
    } else false
  }

  public confirm(): void {
    const decree = this.grid.instance.getSelectedRowsData()[0]
    this.dialogRef.close(decree)
  }

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
