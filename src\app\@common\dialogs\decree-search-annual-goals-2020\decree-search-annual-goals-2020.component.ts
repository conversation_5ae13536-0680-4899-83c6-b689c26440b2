import { Component, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef } from '@nebular/theme'
import { AnnualGoals2020Service } from '@pages/planning/budget-guidelines-law/annual-goals-2020/annual-goals-2020.service'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'
import { finalize } from 'rxjs/operators'

import { DecreeSearchAnnualGoals2020Service } from './decree-search-annual-goals-2020.service'

@Component({
  selector: 'eqp-decree-search',
  templateUrl: './decree-search-annual-goals-2020.component.html',
  styleUrls: ['./decree-search-annual-goals-2020.component.scss'],
})
export class DecreeSearchAnnualGoals2020Component implements OnInit, OnDestroy {
  public loading: boolean = false

  @Input()
  public dialogTitle: string = 'Leis/Atos | Busca'
  public gridData: any
  public columnsTemplate: DxColumnInterface[] = []
  public selected: any[] = []
  public exerciseAnnualData: any

  @Input() public exercise: any

  private subscription: Subscription

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private service: DecreeSearchAnnualGoals2020Service,
  ) { }

  public ngOnInit(): void {
    this.fetchGrid()
    this.columnsTemplate = this.getColumnsTemplate()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private fetchGrid(): void {
    const exercicio = this.exercise;

    this.gridData = {
      store: this.service.getDataSourceFiltroComposto('uuid', 'lei/paginado', 10, `["anoInicialAplicacao","=","${exercicio}"],"and",["escopoCodigo","=","2"]`),
      paginate: true,
      pageSize: 10,
    }
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: '',
        dataField: 'uuid',
        width: 70,
        cellTemplate: 'checkedTemplate',
      },
      {
        caption: 'Código',
        dataField: 'codigo',
        width: 100,
      },
      {
        caption: 'Tipo de Documento',
        dataField: 'tipoDocumento.tipoDocumentoTce.nome',
      },
      {
        caption: 'Número',
        dataField: 'numero',
      },
      {
        caption: 'Ano',
        dataField: 'ano',
      },
      {
        caption: 'Descrição',
        dataField: 'descricaoTipoDocumento',
      },
      {
        caption: 'Data',
        dataField: 'data',
      },
      {
        caption: 'Ano Inicial',
        dataField: 'anoInicialAplicacao',
      },
    ]
    return template
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid) return true
    } else false
  }

  public confirm(): void {
    const decree = this.grid.instance.getSelectedRowsData()[0]
    this.dialogRef.close(decree)
  }

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
