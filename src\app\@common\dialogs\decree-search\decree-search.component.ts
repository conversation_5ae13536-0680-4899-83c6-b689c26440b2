import { Component, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef } from '@nebular/theme'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'

import { DecreeSearchService } from './decree-search.service'

@Component({
  selector: 'eqp-decree-search',
  templateUrl: './decree-search.component.html',
  styleUrls: ['./decree-search.component.scss'],
})
export class DecreeSearchComponent implements OnInit, OnDestroy {
  public loading: boolean = false

  @Input() public dialogTitle: string = 'Leis/Atos | Busca'
  @Input() public codigoEscopo: number[] = []
  @Input() public url: any
  @Input() public filter: string

  public gridData: any
  public columnsTemplate: DxColumnInterface[] = []
  public selected: any[] = []

  private subscription: Subscription

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private service: DecreeSearchService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
    this.columnsTemplate = this.getColumnsTemplate()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private fetchGrid(): void {
    let escopoFilter: string = ''

    for (let i = 0; i < this.codigoEscopo.length; i++) {
      escopoFilter += `["escopoCodigo","=","${this.codigoEscopo[i]}"]`

      if (i < this.codigoEscopo.length - 1) escopoFilter += ',"or",'
    }

    this.gridData = this.service.getDataSourceFiltroComposto(
      'uuid',
      this.url ? this.url : 'lei/paginado',
      10,
      escopoFilter || this.filter,
    )
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: '',
        dataField: 'uuid',
        width: 70,
        cellTemplate: 'checkedTemplate',
      },
      {
        caption: 'Código',
        dataField: 'codigo',
        width: 100,
      },
      {
        caption: 'Tipo de Documento',
        dataField: 'tipoDocumento.tipoDocumentoTce.nome',
      },
      {
        caption: 'Número',
        dataField: 'numero',
      },
      {
        caption: 'Ano',
        dataField: 'ano',
      },
      {
        caption: 'Descrição',
        dataField: 'descricaoTipoDocumento',
      },
      {
        caption: 'Data',
        dataType: 'date',
        dataField: 'data',
      },
      {
        caption: 'Ano Inicial',
        dataField: 'anoInicialAplicacao',
      },
    ]
    return template
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid) return true
    } else false
  }

  public confirm(): void {
    const decree = this.grid.instance.getSelectedRowsData()[0]
    this.dialogRef.close(decree)
  }

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
