import { ToastrService } from './../../services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { FormGroup, FormBuilder } from '@angular/forms'
import { Component, Input, OnInit } from '@angular/core'

@Component({
  selector: 'eqp-editor-doc',
  templateUrl: './editor-doc.component.html',
  styleUrls: ['./editor-doc.component.scss'],
})
export class EditorDocComponent implements OnInit {
  public loading: boolean = false
  public formulario: FormGroup

  @Input() public pageTitle: string = 'Riscos Fiscais'
  @Input() public dados: string

  constructor(
    private formBuilder: FormBuilder,
    public ref: NbDialogRef<EditorDocComponent>,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    if(this.dados){
      let amount = this.dados.split("&lt;")
      let cleanText = this.dados
      amount.forEach(x => {
        cleanText = cleanText.replace("&lt;", "<");
      })
      this.formulario.get('texto').patchValue(cleanText)
    }
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      texto: [this.dados],
    })
  }

  public cancelar(): void {
    this.ref.close(this.dados)
  }

  public gravar(): void {
    this.ref.close(this.formulario.get('texto').value)
  }
}
