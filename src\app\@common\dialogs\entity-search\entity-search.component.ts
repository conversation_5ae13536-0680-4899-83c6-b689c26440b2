import { Component, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef } from '@nebular/theme'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'
import { finalize } from 'rxjs/operators'

import { EntitySearchService } from './entity-search.service'

@Component({
  selector: 'eqp-entity-search',
  templateUrl: './entity-search.component.html',
  styleUrls: ['./entity-search.component.scss'],
})
export class EntitySearchComponent implements OnInit, OnDestroy {
  public loading: boolean = false

  @Input()
  public dialogTitle: string = 'Entidade | Busca'
  public gridData: any
  public columnsTemplate: DxColumnInterface[] = []
  public selected: any[] = []

  private subscription: Subscription

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private service: EntitySearchService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
    this.columnsTemplate = this.getColumnsTemplate()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private fetchGrid(): void {
    this.gridData = {
      store: this.service.getDataSourceFiltro('uuid', 'entidade/paginado'),
      paginate: true,
      pageSize: 10,
    }
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: '',
        dataField: 'uuid',
        width: 70,
        cellTemplate: 'checkedTemplate',
      },
      {
        caption: 'Código',
        dataField: 'codigo',
      },
      {
        caption: 'Nome',
        dataField: 'nome',
      },
      {
        caption: 'Uf',
        dataField: 'uf',
      },
    ]
    return template
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid) return true
    } else false
  }

  public confirm(): void {
    const decree = this.grid.instance.getSelectedRowsData()[0]
    this.dialogRef.close(decree)
  }

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
