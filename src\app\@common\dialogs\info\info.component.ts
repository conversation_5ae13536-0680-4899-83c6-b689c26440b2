import { Component, Input, OnInit } from '@angular/core'
import { NbDialogRef } from '@nebular/theme'

@Component({
  selector: 'eqp-info-dialog',
  templateUrl: './info.component.html',
  styleUrls: ['./info.component.scss'],
})
export class InfoComponent implements OnInit {
  @Input() public dialogTitle: string = 'Informativo'

  @Input() public dialogContent: any

  @Input() public gridData: any = []
  @Input() public gridColumns: any = []
  @Input() public dialogSize: 'small' | 'medium' | 'large' | 'extra-large' | 'full' = 'large'

  public loading: boolean = false

  constructor(protected dialogRef: NbDialogRef<any>) {}

  public ngOnInit(): void {}

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
