<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ dialogTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <ul *ngIf="list && list.length > 0">
      <cdk-virtual-scroll-viewport itemSize="1">
        <ng-container *cdkVirtualFor="let item of list">
          <li class="mb-1">
            <ng-container *ngIf="this.config.type === 'MULTI'">
              <div class="checkbox">
                <input
                  type="checkbox"
                  [(ngModel)]="item.$checked"
                  [attr.value]="item.$checked"
                />

                <i
                  [ngClass]="{
                    'fas fa-check-square': item.$checked,
                    'far fa-square': !item.$checked
                  }"
                ></i>
              </div>
            </ng-container>

            {{ item.text }}
          </li>
        </ng-container>
      </cdk-virtual-scroll-viewport>
    </ul>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="dispose()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
        >
          Selecionar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
