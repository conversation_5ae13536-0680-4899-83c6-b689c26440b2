import { Component, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogRef } from '@nebular/theme'
import { PreLoadService } from '@pages/pre-load.service'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'

import {
  ClassificacaoPessoa,
  TipoPessoa,
} from '@dialogs/person-search/person-search.model'
import DataSource from 'devextreme/data/data_source'
import { PersonSearchService } from './person-search.service'

@Component({
  selector: 'eqp-person-search',
  templateUrl: './person-search.component.html',
  styleUrls: ['./person-search.component.scss'],
})
export class PersonSearchComponent implements OnInit, OnD<PERSON>roy {
  public loading: boolean = false
  // TODO ADD PERSON INTERFACE AND RETURN INTERFACE
  @Input()
  public dialogTitle: string = 'Pessoa | Busca'
  public gridData: any
  public personTypeDataSource: any
  public columns: DxColumnInterface[] = []
  public selected: any[] = []

  private subscription: Subscription

  // FILTERS
  @Input() tipoPessoa?: TipoPessoa
  @Input() classificacao?: ClassificacaoPessoa

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private service: PersonSearchService,
    private preLoadService: PreLoadService,
    private toastr: ToastrService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
    this.fetchFilter()
    this.columns = this.getColumns()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private fetchGrid(): void {
    const filter = []
    if (this.tipoPessoa) {
      filter.push('tipoPessoaId', '=', this.tipoPessoa)
    }

    const url =
      this.classificacao === ClassificacaoPessoa.Servidor
        ? 'servidor'
        : 'pessoa'

    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        url,
        10,
        'tipoPessoaId',
        this.tipoPessoa,
      ),
      paginate: true,
    })
  }

  private fetchFilter(): void {
    this.personTypeDataSource = this.service.getDataSourceFiltro(
      'uuid',
      'pessoa/tipo_pessoa',
      10,
    )
  }

  private getColumns(): DxColumnInterface[] {
    if (this.classificacao === ClassificacaoPessoa.Servidor) {
      return [
        {
          caption: '',
          dataField: 'uuid',
          width: 70,
          cellTemplate: 'checkedTemplate',
        },
        {
          caption: 'Nome',
          dataField: 'nomePessoa',
        },
        {
          caption: 'Matrícula',
          dataField: 'matricula',
          width: 120,
        },
      ]
    }

    return [
      {
        caption: '',
        dataField: 'uuid',
        width: 70,
        cellTemplate: 'checkedTemplate',
      },
      {
        caption: 'Código',
        dataField: 'codigo',
        alignment: 'left',
      },
      {
        caption: 'Tipo',
        dataField: 'tipoPessoaId',
        lookup: {
          dataSource: this.personTypeDataSource,
          valueExpr: 'id',
          displayExpr: 'nome',
        },
      },
      {
        caption: 'Nome',
        dataField: 'nome',
      },
    ] as DxColumnInterface[]
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid) return true
    } else false
  }

  public confirm(): void {
    const person = this.grid.instance.getSelectedRowsData()[0]
    this.dialogRef.close(person)
  }

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
