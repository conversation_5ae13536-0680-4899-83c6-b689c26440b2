<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ dialogTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div *ngIf="content" [innerHTML]="content | safeHtml"></div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="dispose()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
