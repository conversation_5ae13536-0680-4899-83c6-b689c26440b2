<div class="row" [ngClass]="'medium'">
  <div class="col-md-12">
    <nb-card [ngStyle]="{ 'max-height': '90vh' }">
      <nb-card-header>
        <div class="row">
          <div class="col-md-8">
            <h5>Relatório</h5>
          </div>
          <div class="col-md-4">
            <button
              [appearance]="'outline'"
              [status]="'primary'"
              [shape]="'small'"
              [size]="'small'"
              [disabled]="false"
              id="'download-report-dialog-button'"
              class="float-right ml-2"
              nbButton
              (click)="dispose()"
            >
              <i class="fas fa-undo-alt"></i>
            </button>
          </div>
        </div>
      </nb-card-header>
      <eqp-loading
        *ngIf="loading"
        class="loading"
        textLoading="Gerando relatório..."
      ></eqp-loading>
      <nb-card-body class="nb-card-body">
        <div class="content-loading">
          <div class="content-report-view">
            <ng-container *ngIf="pdfPreview">
              <iframe
                class="iframe-preview"
                [src]="pdfPreview"
                type="application/pdf"
                allowfullscreen
              ></iframe>
            </ng-container>
          </div>
        </div>
      </nb-card-body>
      <nb-card-footer>
        <div class="row">
          <div class="d-flex col-sm-12 col-md-6 col-lg-4" style="gap: 0.5rem">
            <i class="mt-2 fas fa-file-alt"></i>
            <eqp-nebular-select
              [formControl]="downloadType"
              [dataSource]="reportTypeList"
              placeholder="Tipo de arquivo"
              [displayExpr]="'valor'"
              [valueExpr]="'chave'"
              [hasFullWidth]="true"
              [disabled]="true"
            ></eqp-nebular-select>
          </div>
          <div class="col align-self-center">
            <button
              [appearance]="'filled'"
              [status]="'success'"
              [shape]="'reactangle'"
              [size]="'small'"
              [disabled]="disableButton"
              id="download-report-dialog-button"
              title="Baixar"
              class="float-right ml-2"
              nbButton
              (click)="confirm()"
            >
              <i class="mr-1 fas fa-download"></i>
              {{ 'Baixar em ' + this.downloadType.value }}
            </button>
          </div>
        </div>
      </nb-card-footer>
    </nb-card>
  </div>
</div>
