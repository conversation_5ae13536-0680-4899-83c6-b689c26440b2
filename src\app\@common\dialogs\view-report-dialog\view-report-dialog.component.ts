import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { CrudService } from '@common/services/crud.service';
import { NbDialogRef } from '@nebular/theme';
import DataSource from 'devextreme/data/data_source';
import { Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { ViewReportService } from './view-report.service';

@Component({
  selector: 'eqp-view-report-dialog',
  templateUrl: './view-report-dialog.component.html',
  styleUrls: ['./view-report-dialog.component.scss'],
})
export class ViewReportDialogComponent implements OnInit {
  @Input() downloadName: string;
  @Input() data: string;

  downloadType = new FormControl('PDF');

  loading: boolean = false;
  disableButton: boolean = true;
  pdfPreview?: SafeResourceUrl;
  reportData?: any;

  reportTypeList: DataSource;

  private unsub$ = new Subject<null>();
  public item = '';

  constructor(
    private sanitizer: DomSanitizer,
    private crudService: CrudService,
    private reportService: ViewReportService,
    private dialogRef: NbDialogRef<ViewReportDialogComponent>,
  ) {}

  ngOnInit(): void {
    this.getTypes();
    //this.getReport();
    this.getReportPreview(this.data);
  }

  ngOnDestroy(): void {
    this.unsub$.next();
    this.unsub$.complete();
  }

  private getTypes() {
    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe((res) => {
        this.reportTypeList = new DataSource({ store: res.dados });
      });
  }

  notWorking() {
    setTimeout(() => {
      this.dialogRef.close();
    }, 1000);
  }

  private getReportPreview(data) {
    this.pdfPreview = this.sanitizer.bypassSecurityTrustResourceUrl(
      'data:application/pdf;base64,' + data,
    );
    this.disableButton = false;
    this.loading = false;
  }

  confirm() {
    this.reportService.donwloadReport(this.data, this.downloadName);
  }

  dispose() {
    this.dialogRef.close();
  }
}
