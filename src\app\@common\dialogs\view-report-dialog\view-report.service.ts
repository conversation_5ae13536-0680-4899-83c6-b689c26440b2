import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';

@Injectable({
  providedIn: 'root',
})
export class ViewReportService extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, `livro_diario_contabil/relatorio`);
  }

  public b64toBlob(b64Data, contentType) {
    contentType = contentType || '';
    let sliceSize = 512;

    var byteCharacters = atob(b64Data);
    var byteArrays = [];

    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);

      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      var byteArray = new Uint8Array(byteNumbers);

      byteArrays.push(byteArray);
    }

    var blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

  donwloadReport(dados: string, fileName: string) {
    var blob = this.b64toBlob(dados, 'application/pdf');
    let a = document.createElement('a');
    document.body.appendChild(a);
    var url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = String(`${fileName}.pdf`);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }

  // donwloadReport(documentDonwload: any, nameReportDownload: string) {
  //   const downloadLink = document.createElement('a');
  //   downloadLink.href = window.URL.createObjectURL(
  //     this.dataURItoBlob(
  //       documentDonwload.documento,
  //       documentDonwload.tipo.toLowerCase(),
  //     ),
  //   );
  //   if (nameReportDownload)
  //     downloadLink.setAttribute(
  //       'download',
  //       `${nameReportDownload}.${documentDonwload.tipo.toLocaleLowerCase()}`,
  //     );
  //   document.body.appendChild(downloadLink);
  //   downloadLink.click();
  // }

  // dataURItoBlob(dataURI: string, dataType: string) {
  //   const byteString = window.atob(dataURI);
  //   const arrayBuffer = new ArrayBuffer(byteString.length);
  //   const int8Array = new Uint8Array(arrayBuffer);
  //   for (let i = 0; i < byteString.length; i++) {
  //     int8Array[i] = byteString.charCodeAt(i);
  //   }
  //   const blob = new Blob([int8Array], { type: dataType });
  //   return blob;
  // }
}
