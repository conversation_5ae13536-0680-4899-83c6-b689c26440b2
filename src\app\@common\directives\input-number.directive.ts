import { Directive, HostListener, ElementRef, Input } from '@angular/core';

@Directive({
  selector: '[inputNumberDirective]',
})
export class InputNumberDirective {
  @Input() validarInputNumerico: boolean = false;

  private regex: RegExp = new RegExp(/^[0-9]*$/);
  private specialKeys: Array<string> = [
    'Backspace',
    'Tab',
    'End',
    'Home',
    'ArrowLeft',
    'ArrowRight',
    'Control',
    'Insert',
    'Delete',
  ];

  constructor(private el: ElementRef) {}

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    if (!this.validarInputNumerico) {
      return;
    }

    if (
      this.specialKeys.indexOf(event.key) !== -1 ||
      (event.ctrlKey && (event.key === 'v' || event.key === 'c')) ||
      (event.shiftKey && event.key === 'Insert')
    ) {
      return;
    }

    let current: string = this.el.nativeElement.value;
    let next: string = current.concat(event.key);
    if (next && !next.match(this.regex)) {
      event.preventDefault();
    }
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent) {
    if (!this.validarInputNumerico) {
      return;
    }

    let clipboardData = event.clipboardData;
    let pastedInput = clipboardData.getData('text');

    if (pastedInput && !pastedInput.match(this.regex)) {
      event.preventDefault();
    }
  }
}
