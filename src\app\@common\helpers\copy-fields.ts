import { Params } from "@angular/router";

export function copyOnlyFields<T>(obj:any, fieldsCopy: any[]): T{
  return fieldsCopy.reduce((acc, field: string) => {
      if (Object.prototype.hasOwnProperty.call(obj, field)) {
        acc[field] = obj[field];
      }
      return acc;
    }, new Object());
}

export function fromEntries(entries: [string, any][]): object {
  const result: any = {};

  for (const [key, value] of entries) {
    result[key] = value;
  }

  return result;
}
export function removeEmptyProperties(obj: object){
  return fromEntries(Object.entries(obj).filter(([_, v]) => v || v === false)) as Params;
}

export function copyOnlyFieldsNew<T>(obj: object, fieldsCopy: string[]): Partial<T> {
  return fromEntries(Object.entries(obj).filter(([key, _]) => fieldsCopy.includes(key))) as Partial<T>;
}