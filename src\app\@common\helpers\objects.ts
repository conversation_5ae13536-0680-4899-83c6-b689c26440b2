export const htmlEditorObject = {
  toolbar: {
    items: [
      'undo',
      'redo',
      'separator',
      {
        name: 'size',
        acceptedValues: ['8pt', '10pt', '12pt', '14pt', '18pt', '24pt', '36pt'],
      },
      {
        name: 'font',
        acceptedValues: [
          'Aria<PERSON>',
          'Courier New',
          'Georgia',
          'Impact',
          '<PERSON><PERSON>',
          'Tahoma',
          'Times New Roman',
          'Verdana',
        ],
      },
      'separator',
      'bold',
      'italic',
      'strike',
      'underline',
      'separator',
      'alignLeft',
      'alignCenter',
      'alignRight',
      'alignJustify',
      'separator',
      {
        name: 'header',
        acceptedValues: [false, 1, 2, 3, 4, 5],
      },
      'separator',
      'separator',
      'orderedList',
      'bulletList',
      'separator',
      'color',
      'background',
      'separator',
      'link',
      'image',
      'separator',
      'clear',
      'codeBlock',
      'blockquote',
    ],
  },
  mediaResizing: {
    enabled: true,
  },
}
