import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpParams,
  HttpRequest,
} from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Router } from '@angular/router'
import { environment } from '@environments/environment'
import { UserDataService } from '@guards/services/user-data.service'
import { LoadingService } from '@services/loading/loading.service'
import { ToastrMessageInterface } from '@services/toastr/interfaces/toastr-message'
import { ToastrService } from '@services/toastr/toastr.service'
import deepCleaner from 'deep-cleaner'
import { Observable, throwError } from 'rxjs'
import { catchError, tap } from 'rxjs/operators'

@Injectable()
export class APIInterceptor implements HttpInterceptor {
  public BASE_URL: string

  constructor(
    private loadingService: LoadingService,
    private userService: UserDataService,
    private toastr: ToastrService,
    private router: Router,
  ) {
    this.BASE_URL = environment.url
  }

  public getBaseURL(): string {
    return this.BASE_URL
  }

  public getRequest(req: HttpRequest<any>): HttpRequest<any> {
    const userData = this.userService.getUserData()

    if (!navigator.onLine) {
      this.toastr.bulkSend([
        {
          error: true,
          message:
            'Detectamos um problema de conexão. Por favor, verifique e tente novamente. Caso persista, entre em contato com o suporte.',
        },
      ])
    }

    let headers: any = {
      'x-encryption': 'false',
      'x-validate': 'false',
      'x-permission': 'false',
    }
    if (userData.token) {
      headers = {
        ...headers,
        authorization: userData.token,
      }
    }

    if (userData?.tokenJwt) {
      headers = {
        ...headers,
        'authorization-jwt': userData?.tokenJwt,
      }
    }

    if (
      userData.clienteUuid &&
      userData.municipioClienteUuid &&
      userData.exercicioUuid &&
      userData.entidadeUuid &&
      userData.exercicio
    ) {
      headers = {
        ...headers,
        'x-client-uuid': userData.clienteUuid,
        'x-county-client-uuid': userData.municipioClienteUuid,
        'x-exercise-uuid': userData.exercicioUuid,
        'x-entity-uuid': userData.entidadeUuid,
        'x-exercise': userData.exercicio.toString().substring(0, 4),
      }
    }

    if (req.body) {
      deepCleaner(req.body)
      deepCleaner(req.body)
    }

    return req.clone({
      url: `${this.BASE_URL}/${req.url}`,
      withCredentials: true,
      setHeaders: headers,
      params: this.handleParams(req.params),
    })
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler,
  ): Observable<HttpEvent<any>> {
    const apiReq = this.getRequest(req)

    this.loadingService.getLoadingSubject().next(true)

    return next.handle(apiReq).pipe(
      catchError(e => this.handleError(e)),
      tap(data => this.handleResponse(data)),
    )
  }

  private handleParams(params: HttpParams): HttpParams {
    params.keys().forEach(key => {
      const value = params.get(key)
      if (!(value !== '' && value !== null && typeof value !== 'undefined')) {
        params = params.delete(key)
      }
    })

    return params
  }

  private handleResponse(data: any): void {
    if (!data || !data.body) return

    if (data.error != null && data.error.causa != null) {
      this.toastr.send({
        error: true,
        message: data.error.causa.descricao,
      })
    }
    if (data.mensagemErro != null) {
      this.toastr.bulkSend({
        error: true,
        message: data.mensagemErro,
      })
    }
    if (data.descricao != null) {
      this.toastr.bulkSend({
        error: true,
        message: data.descricao,
      })
    }
  }

  private handleError(err: HttpErrorResponse): Observable<any> {
    const unreachable = 0
    const badRequest = 400 // https://httpstatuses.com/400
    const unauthorized = 401 // https://httpstatuses.com/401
    const forbidden = 403 // https://httpstatuses.com/403

    const esMensagens: ToastrMessageInterface[] = []

    let msg
    if (err.error != null && err.error.causa != null) {
      if (typeof err.error.causa === 'string') msg = err.error.causa
      else msg = err.error.causa.descricao
      this.toastr.send({
        error: true,
        message: msg,
      })
    }

    if (err.status === unreachable) {
      esMensagens.push({
        error: true,
        message:
          'Detectamos um problema de conexão. Por favor, verifique e tente novamente. Caso persista, entre em contato com o suporte.',
      })
    }
    if (msg === 'jwt expired') {
      localStorage.removeItem('jwtToken')
      localStorage.removeItem('userData')
      this.userService.crossClientDel('userData')
      this.userService.crossClientDel('jwtToken')
    }

    if (
      err.status === unreachable ||
      err.status === badRequest ||
      err.status === unauthorized ||
      err.status === forbidden
    ) {
      this.toastr.bulkSend(esMensagens)
      return throwError(err)
    }

    return throwError(err)
  }
}
