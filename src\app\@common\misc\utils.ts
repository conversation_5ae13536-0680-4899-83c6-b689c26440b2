import { AbstractControl, FormArray, FormGroup } from '@angular/forms'

export function markFormAsDirty(form: FormGroup) {
  Object.keys(form.controls).forEach(key => {
    markControlAsDirty(form.controls[key])
  })
}

export function markControlAsDirty(control: AbstractControl) {
  control.markAsDirty()

  if (control instanceof FormGroup) {
    markFormAsDirty(control)
  } else if (control instanceof FormArray) {
    control.controls.forEach(element => markControlAsDirty(element))
  }
}

export function parseApiBoolean(value: 'S' | 'N') {
  return value === 'S'
}

export function formatApiBoolean(value: boolean) {
  return value ? 'S' : 'N'
}
