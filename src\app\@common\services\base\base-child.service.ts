import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class BaseChildService<T> {
  constructor(
    protected http: HttpClient,
    public parentUri: string,
    public uri: string,
  ) {}

  public get(parentUuid: string, filters?: any) {
    const headers = new HttpHeaders()
    let params = new HttpParams()
    if (filters) {
      Object.keys(filters).forEach(p => (params = params.append(p, filters[p])))
    }
    return this.http.get<any>(
      `${this.parentUri}/${parentUuid}/${this.uri}`,
      {
        headers,
        params,
      },
    )
  }

  public getIndividual(
    parentUuid: string,
    uuid: string,
  ): Observable<ResponseDto<T>> {
    const headers = new HttpHeaders()
    return this.http.get<ResponseDto<T>>(
      `${this.parentUri}/${parentUuid}/${this.uri}/${uuid}`,
      {
        headers,
      },
    )
  }

  public put(parentUuid: string, dto: T, uuid: string): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.put<ResponseDto<T>>(
      `${this.parentUri}/${parentUuid}/${this.uri}/${uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(parentUuid: string, dto: T): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.post<ResponseDto<T>>(
      `${this.parentUri}/${parentUuid}/${this.uri}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(parentUuid: string, uuid: string): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.delete<any>(
      `${this.parentUri}/${parentUuid}/${this.uri}/${uuid}`,
      {
        headers,
      },
    )
  }
}
