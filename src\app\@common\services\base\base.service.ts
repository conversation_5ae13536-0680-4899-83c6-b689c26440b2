import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'

import { CrudService } from './../crud.service'
import { pluck } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class BaseService<RT, T extends { uuid?: string }> extends CrudService {
  constructor(protected http: HttpClient, private routeUrl: string) {
    super(http)
  }
  public get(filters?: any): Observable<RT> {
    const headers = new HttpHeaders()
    let params = new HttpParams()

    if (filters) {
      Object.keys(filters).forEach(p => (params = params.append(p, filters[p])))
    }

    return this.http.get<RT>(this.routeUrl, {
      headers,
      params,
    })
  }

  public getOne(uuid: string): Observable<T> {
    return this.http
      .get<{ dados: T; sucesso: boolean }>(`${this.routeUrl}/${uuid}`)
      .pipe(pluck('dados'))
  }

  public put(dto: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<T>(`${this.routeUrl}/${dto.uuid}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<T>(this.routeUrl, dto, {
      headers,
      observe: 'response',
    })
  }

  public save(dto: any): Observable<T> {
    return dto.uuid
      ? this.http.put<T>(`${this.routeUrl}/${dto.uuid}`, dto)
      : this.http.post<T>(this.routeUrl, dto)
  }

  public postBatch(batch: any[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<T[]>(`${this.routeUrl}/lote`, batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: string | number): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`${this.routeUrl}/${uuid}`, {
      headers,
    })
  }

  //TODO: Verificar
  public getIndividual(
    uuid: string,
  ): Observable<{ dados: T; sucesso: boolean }> {
    const headers = new HttpHeaders()

    return this.http.get<{ dados: T; sucesso: boolean }>(
      `${this.routeUrl}/${uuid}`,
      {
        headers,
      },
    )
  }
}
