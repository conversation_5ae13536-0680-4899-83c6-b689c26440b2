import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@environments/environment'
import { ApiResponse } from '@pages/planning/shared/dtos/api-response'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import * as AspNetData from 'devextreme-aspnet-data-nojquery'
import CustomStore from 'devextreme/data/custom_store'

@Injectable({
  providedIn: 'root',
})
export class CrudService {
  public urlFull: string

  constructor(protected httpClient: HttpClient) {
    this.urlFull = environment.url
  }

  public getDataSourceFiltro(
    idkey: string,
    complementoUrl: string = '',
    take: number = 0,
    coluna: string = '',
    valor: string = '',
  ): CustomStore {
    const userData = JSON.parse(localStorage.getItem('userData'))
    const jwt = userData.token
    return AspNetData.createStore({
      key: idkey,
      loadUrl: this.urlFull + (complementoUrl ? '/' + complementoUrl : ''),
      onBeforeSend: function (method, ajaxOptions) {
        if (coluna && valor) {
          if (ajaxOptions.data.filter) {
            if (ajaxOptions.data.filter.startsWith('[[')) {
              ajaxOptions.data.filter = ajaxOptions.data.filter.replace(
                '[[',
                `[["${coluna}","=","${valor}"],"and",[`,
              )
            } else {
              ajaxOptions.data.filter =
                `[["${coluna}","=","${valor}"],"and",[` +
                ajaxOptions.data.filter +
                ']'
            }
          } else {
            ajaxOptions.data.filter = `["${coluna}","=","${valor}"]`
          }
        }
        ajaxOptions.data.take = take
        ajaxOptions.headers = {
          authorization: jwt,
          'x-encryption': 'false',
          'x-permission': 'false',
          'x-validate': 'false',
          'x-entity-uuid': userData?.entidadeUuid,
          'x-exercise-uuid': userData?.exercicioUuid,
          'x-exercise': userData.exercicio.toString().substring(0, 4),
          'x-county-client-uuid': userData?.municipioClienteUuid,
          'x-client-uuid': userData?.clienteUuid,
        }
      },
      onAjaxError(e) {
        const erro = JSON.parse(e.xhr.response)
        e.error =
          erro.descricao +
          ': ' +
          erro.mensagem +
          '. Descrição: ' +
          erro.causa.message
      },
    })
  }

  public getDataSourceFiltroComposto(
    idkey: string,
    complementoUrl: string = '',
    take: number = 0,
    filtro: string = '',
  ): CustomStore {
    const userData = JSON.parse(localStorage.getItem('userData'))
    const jwt = userData.token
    return AspNetData.createStore({
      key: idkey,
      loadUrl: this.urlFull + (complementoUrl ? '/' + complementoUrl : ''),
      onBeforeSend: function (method, ajaxOptions) {
        if (filtro) {
          if (ajaxOptions.data.filter) {
            if (ajaxOptions.data.filter.startsWith('[[')) {
              ajaxOptions.data.filter = ajaxOptions.data.filter.replace(
                '[[',
                `[${filtro},"and",[`,
              )
            } else {
              ajaxOptions.data.filter =
                `[${filtro},"and",` + ajaxOptions.data.filter + ']'
            }
          } else {
            ajaxOptions.data.filter = `[${filtro}]`
          }
        }
        ajaxOptions.data.take = take
        ajaxOptions.headers = {
          authorization: jwt,
          'x-encryption': 'false',
          'x-permission': 'false',
          'x-validate': 'false',
          'x-entity-uuid': userData?.entidadeUuid,
          'x-exercise-uuid': userData?.exercicioUuid,
          'x-county-client-uuid': userData?.municipioClienteUuid,
          'x-client-uuid': userData?.clienteUuid,
        }
      },
      onAjaxError(e) {
        const erro = JSON.parse(e.xhr.response)
        e.error =
          erro.descricao +
          ': ' +
          erro.mensagem +
          '. Descrição: ' +
          erro.causa.message
      },
    })
  }

  customGetSingleData<T>(url: string, params?: Object) {
    let httpParams: HttpParams = new HttpParams()
    params &&
      Object.keys(params).forEach(key => {
        httpParams = httpParams.append(key, params[key])
      })
    return this.httpClient.get<ApiResponse<T>>(`${url}`, {
      params: httpParams,
    })
  }

  getSingleData<T>(url: string) {
    return this.httpClient.get<ResponseDto<T>>(`${url}`)
  }

  public getDataSource(
    idkey: string,
    complementoUrl: string = '',
    take: number = 0,
  ): CustomStore {
    const headers = this.getHeaders();
    return AspNetData.createStore({
      key: idkey,
      loadUrl: this.urlFull + (complementoUrl ? '/' + complementoUrl : ''),
      onBeforeSend: function (method, ajaxOptions) {
        if (ajaxOptions.data.filter)
          ajaxOptions.data.filter = ajaxOptions.data.filter.replace(
            'contains',
            '=',
          );

        ajaxOptions.data.take = take;
        ajaxOptions.headers = headers;
      },
      onAjaxError(e) {
        const erro = JSON.parse(e.xhr.response);
        e.error =
          erro.descricao +
          ': ' +
          erro.mensagem +
          '. Descrição: ' +
          erro.causa.message;
      },
    });
  }

  getSingleObject<T>(url: string, params?: Object) {
    let httpParams: HttpParams = new HttpParams();
    params &&
      Object.keys(params).forEach((key) => {
        httpParams = httpParams.append(key, params[key]);
      });
    return this.httpClient.get<ResponseDto<T>>(`${url}`, {
      params: httpParams,
    });
  }

  public getHeaders() {
    const userData = JSON.parse(localStorage.getItem('userData'));
    const jwt = userData.token;
    return {
      authorization: jwt,
      'x-encryption': 'false',
      'x-permission': 'false',
      'x-validate': 'false',
      'x-entity-uuid': userData?.entidadeUuid,
      'x-exercise-uuid': userData?.exercicioUuid,
      'x-county-client-uuid': userData?.municipioClienteUuid,
      'x-client-uuid': userData?.clienteUuid,
    };
  }

}
