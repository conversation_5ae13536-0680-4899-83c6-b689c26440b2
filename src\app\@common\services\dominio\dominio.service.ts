import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { DominioInterface } from './interfaces/dominio';


@Injectable({
  providedIn: 'root'
})
export class DominioService {

  constructor(private _httpClient: HttpClient) { }

  checkDomain(domain: string){
    return this._httpClient.get<ResponseDto<any>>(`transparencia/dominio/check/${domain}`)
  }

  getDomain(){
    return this._httpClient.get<ResponseDto<DominioInterface>>(`transparencia/dominio`)
  }

  updateDomain(domain: string){
    return this._httpClient.put<ResponseDto<DominioInterface>>(`transparencia/dominio`, {
      domainName: domain
    })
  }

  deleteDomain(){
    return this._httpClient.delete(`transparencia/dominio`)
  }

  createDomain(domain: string){
    return this._httpClient.post<ResponseDto<DominioInterface>>(`transparencia/dominio/`, {
      domainName: domain
    })
  }
}
