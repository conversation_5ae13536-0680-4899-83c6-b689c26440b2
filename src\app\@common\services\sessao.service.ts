import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'

import { first } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class SessaoService {
  constructor(private http: HttpClient) {}

  public atualizarToken(tokenRequest: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<any>('auth/token', tokenRequest, {
      headers,
    })
  }

  public logout(): void {
    const idToken = JSON.parse(localStorage.getItem('userData'))?.idToken
    localStorage.removeItem('userData')
    if (idToken)
      this.removerSessao(idToken)
        .pipe(first())
        .subscribe(data => {})
  }

  public atualizaSessao(sessao: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<any>(`auth/sessao/${sessao.idToken}`, sessao, {
      headers,
    })
  }

  public recuperaSessao(idToken: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`auth/sessao/${idToken}`, {
      headers,
    })
  }

  public removerSessao(idToken: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`auth/sessao/${idToken}`, {
      headers,
    })
  }
}
