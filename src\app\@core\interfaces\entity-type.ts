export interface EntityTypeInterface {
  codigo: number;
  uuid?: string;
  nome: string;
}

export const ENTITIES_TYPE = {
  Outros: {
    codigo: 0,
    nome: 'Outros',
  },
  Prefeitura: {
    codigo: 1,
    nome: 'Prefeitura',
  },
  camara: {
    codigo: 2,
    nome: 'Câmara',
  },
  previdencia: {
    codigo: 3,
    nome: 'Fundo de previdência',
  },
  educacao: {
    codigo: 4,
    nome: 'Fundo de educação',
  },
  saude: {
    codigo: 5,
    nome: 'Fundo de saúde',
  },
  mista: {
    codigo: 6,
    nome: 'Economia mista',
  },
  empresaPublica: {
    codigo: 7,
    nome: 'Empresa pública',
  },
  Autarquia: {
    codigo: 8,
    nome: 'Autarquia',
  },
  consorcio: {
    codigo: 9,
    nome: 'Consórcio',
  },
};
