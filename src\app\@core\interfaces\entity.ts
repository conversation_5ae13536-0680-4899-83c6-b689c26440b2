import { AttachmentInterface } from './attachment';
import { EntityTypeInterface } from './entity-type';
import { FooterInterface } from './rodape';
import { ThemeInterface } from './tema';
import { PensionTypeInterface } from './tipoPrevidencia';

export interface EntityDataInterface{
  valido: boolean;
  dominio: string;
  municipioUuid: string;
  clienteUuid: string;
  entidade: EntityInterface;
  acessos: number;
  tema: ThemeInterface;
  rodape: FooterInterface;
  linkContato: string | null;
}
export interface EntityInterface {
  endereco: string;
  brasao: AttachmentInterface;
  dominio: string;
  uuid: string;
  clienteUuid: string;
  nome: string;
  uf: string;
  tipoEntidade: EntityTypeInterface;
  tipoPrevidencia: PensionTypeInterface;
}
