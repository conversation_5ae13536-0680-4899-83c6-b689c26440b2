export interface MenuInterface {
    title: string;
    icon?: IconInterface;
    link: string;
    ordem: number;
    status: "ACTIVE" | "DISABLED";
    code: string;
    children?: MenuInterface[];
    description?: string;
}

interface IconInterface {
    icon: string;
    pack: string;
}

export interface MenuResponseInterface {
    uuid: string;
    nome: string;
    codigoPai: number;
    codigo: number;
    ordem: number;
    descricao: string;
    termoPesquisa: string;
    classeFontAwesome: string;
    singleMenu: boolean;
    personalizavel: string;
    visivel:string;
    nivel: number;
    link: string;
    filhos: MenuResponseInterface[];
    criadoEm: string;
    atualizadoEm: string;
  }