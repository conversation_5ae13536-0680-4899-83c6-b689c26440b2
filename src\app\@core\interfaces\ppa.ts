export interface MultiYearPlanInterface {
  uuid: string;
  exercicioBase: number;
  exercicioInicial: number;
  exercicioFinal: number;
  descricao: string;
}
export interface PPAVersionInterface {
  uuid: string;
  leiUuidPms: string;
  leiUuidPpa: string;
  numeroVersao: number;
  ppaEscopoDocumentoId: PPADocumentScopeInterface;
  situacaoVersaoId: PPAVersionStatusInterface;
  ppaId: MultiYearPlanInterface;
}
export interface PPADocumentScopeInterface {
  id: number;
  escopoDocumentoTceUuid: string;
  flagVersao: string;
  uuid: string;
  nome: string;
}
export interface PPAVersionStatusInterface {
  id: number;
  uuid: string;
  nome: string;
  versao: number;
}

export interface PPAProgramInterface {
  uuid: string;
  codigo: string;
  nome: string;
  ppaVersaoId: PPAVersionInterface;
  programaMovimentoUuid: string;
}

export interface LawAct {
  uuid: string;
  code: string;
  actCode: string;
  documentType: string;
  initialYear: number;
  scopeType: string;
  year: number;
}
