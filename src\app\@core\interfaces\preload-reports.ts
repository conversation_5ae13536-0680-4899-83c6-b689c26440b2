import { AttachmentInterface } from './attachment';
import { EntityTypeInterface } from './entity-type';
export interface ReportPreloadEntityInterface {
  chave: string;
  tipo: EntityTypeInterface;
  valor: string;
}

export interface ReportPreloadFileTypeInterface {
  chave: string;
  valor: string;
}

export interface ReportPreloadExerciseInterface {
  chave: string;
  valor: string;
}

export interface ReportPreloadLrfInterface {
  opcoesBimestre: ReportPreloadDefaultInterface[];
  opcoesQuadrimestre: ReportPreloadDefaultInterface[];
  opcoesSemestre: ReportPreloadDefaultInterface[];
  opcoesTpPeriodicidade: ReportPreloadDefaultInterface[];
}

export interface reportPreloadActionTCEInterface {
  codigo: number;
  flagtipoacao: string;
  nome: string;
  uf: string;
  uuid: string;
}
export interface reportPreloadGoodNatureTypeTCEInterface {
  uuid: string;
  codigo: number;
  nome: string;
  uf: string;
}
export interface ReportPreloadDefaultInterface {
  chave: string;
  valor: string;
}

export interface reportPreloadRHInterface {
  opcoesMes: ReportPreloadDefaultInterface[];
  opcoesTipoCargo: ReportPreloadDefaultInterface[];
}

export interface ReportPreloadClassificationInterface {
  brasao: AttachmentInterface;
  classificacao: ClassificationInterface[];
  nome: string;
  uf: string;
  uuid: string;
}
export interface ClassificationInterface {
  nome: string;
  uuid: string;
}

export interface ReportChartConfigInterface {
  headers: {label: string; key: string}[];
  columnsKeys: string[][];
}
