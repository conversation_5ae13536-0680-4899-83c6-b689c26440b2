import {
  HttpClient,
  HttpHeaders,
  HttpParams
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Params } from '@angular/router';
import { ReturnDefaultInterface } from '@common/interfaces/return';
import { PortalReportInterface } from '@core/interfaces/document-body';
import { Pageable } from '@core/interfaces/pageable';
import { UserDataService } from '@guards/services/user-data.service';
import { NbDialogService } from '@nebular/theme';
import { ReportService } from '@pages/planning/shared/reports/services/report.service';
import { Observable, throwError } from 'rxjs';
import { catchError, take, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class PortalReportService extends ReportService {
  constructor(
    private userDataService: UserDataService,
    protected http: HttpClient,
    protected dialog: NbDialogService,
  ) {
    super(http, dialog);
  }

  loadGridReport<T = { [key: string]: unknown }>(dto: Params, urlPath: string) {
    const params = new HttpParams({ fromObject: dto });
    return this.http.get<ReturnDefaultInterface<T>>(`transparencia/${urlPath}`, {
      params,
    });
  }

  singleRequest<T = PortalReportInterface>(
    dto: Params,
    urlPath: string,
  ): Observable<ReturnDefaultInterface<T>> {
    const params = new HttpParams({ fromObject: dto });
    return this.http
      .get<ReturnDefaultInterface<T>>(`transparencia/${urlPath}`, {
        params,
      })
      .pipe(take(1));
  }

  getReport(
    dto: Params,
    urlPath: string,
    downloadReport = false,
    reportName = 'reportName',
    removePageable?: boolean,
  ): Observable<ReturnDefaultInterface<PortalReportInterface>> {
    const dtoRequest = {
      ...dto,
      entidadeUuid: this.userDataService.userData.entidade?.uuid as string,
    };
    let params = new HttpParams({ fromObject: dtoRequest });
    let headers = new HttpHeaders();
    headers = headers.append('x-is-logged', 'true');

    if (!removePageable) {
      params = params.append('size', '0');
    }
    return this.http
      .get<ReturnDefaultInterface<PortalReportInterface>>(`transparencia/${urlPath}`, {
        headers,
        params,
      })
      .pipe(
        tap((element) => {
          if (downloadReport) this.downloadReport(element.dados, reportName);
        }),
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  generateGrid<T>(urlPath: string, dto: Params): Observable<Pageable<T>> {
    const dtoRequest = {
      ...dto,
      entidadeUuid: this.userDataService.userData.entidade?.uuid as string,
    };
    const params = new HttpParams({ fromObject: dtoRequest });
    return this.http
      .get<Pageable<T>>(`transparencia/${urlPath}`, { params })
      .pipe(
        catchError((error) => {
          return throwError(() => error);
        }),
      );
  }

  generateGridDefault<T>(urlPath: string, dto: Params): Observable<T> {
    const dtoRequest = {
      ...dto,
      entidadeUuid: this.userDataService.userData.entidade?.uuid as string,
    };
    const params = new HttpParams({ fromObject: dtoRequest });
    return this.http.get<T>(`transparencia/${urlPath}`, { params }).pipe(
      catchError((error) => {
        return throwError(() => error);
      }),
    );
  }

  downloadReport(
    documentDonwload: PortalReportInterface,
    nameReportDownload: string,
  ) {
    const downloadLink = document.createElement('a');
    downloadLink.href = window.URL.createObjectURL(
      this.dataURItoBlob(
        documentDonwload.documento,
        documentDonwload.tipo.description.toLowerCase(),
      ),
    );
    if (nameReportDownload)
      downloadLink.setAttribute(
        'download',
        `${nameReportDownload}.${documentDonwload.tipo.description.toLocaleLowerCase()}`,
      );
    document.body.appendChild(downloadLink);
    downloadLink.click();
  }

  dataURItoBlob(dataURI: string, dataType: string) {
    const byteString = window.atob(dataURI);
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const int8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteString.length; i++) {
      int8Array[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([int8Array], { type: dataType });
    return blob;
  }
  
}
