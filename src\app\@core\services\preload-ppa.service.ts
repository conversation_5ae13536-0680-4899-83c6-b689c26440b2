import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ReturnDefaultInterface } from '@common/interfaces/return';
import { LawAct, MultiYearPlanInterface, PPADocumentScopeInterface, PPAProgramInterface, PPAVersionInterface } from '@core/interfaces/ppa';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class PreloadPpaService {
  constructor(private _http: HttpClient) {}

  getBasePlan() {
    return this._http.get<ReturnDefaultInterface<MultiYearPlanInterface[]>>(
      'transparencia/pre_carregamento_orcamento/base_plano_plurianual',
    );
  }

  getMultiYearPlan() {
    return this._http.get<ReturnDefaultInterface<MultiYearPlanInterface[]>>(
      'transparencia/pre_carregamento_orcamento/ppa',
    );
  }

  getLawAct(entityUuids: string) {
    let params = new HttpParams();
    params = params.append('entityUuids', entityUuids);
    return this._http.get<ReturnDefaultInterface<LawAct[]>>(
      'transparencia/ppa_demonstrativo_estimativa_receita/law',
      { params },
    );
  }

  getPPAVersion(multyYearPlanUuid: string) {
    let params = new HttpParams();
    params = params.append('ppaUuid', multyYearPlanUuid);
    return this._http.get<ReturnDefaultInterface<PPAVersionInterface[]>>(
      'transparencia/pre_carregamento_orcamento/ppa_versao',
      { params },
    );
  }

  getPPAProgram(ppaUuid?: string, ppaScopeUuid?: string) {
    let params = new HttpParams();
    params = params.append('take', '0');
    params = params.append('skip', '0');

    if (ppaUuid) params = params.append('ppaUuid', ppaUuid);
    if (ppaScopeUuid)
      params = params.append('ppaEscopoDocumentoUuid', ppaScopeUuid);
    return this._http
      .get<ReturnDefaultInterface<PPAProgramInterface[]>>(
        'transparencia/pre_carregamento_orcamento/ppa_programa',
        { params },
      )
      .pipe(
        map((element) => {
          return {
            ...element,
            data: element.data.sort(
              (a, b) => Number(a.codigo) - Number(b.codigo),
            ),
          };
        }),
      );
  }

  getPPAScope() {
    return this._http.get<ReturnDefaultInterface<PPADocumentScopeInterface[]>>(
      'transparencia/pre_carregamento_orcamento/ppa_escopo',
    );
  }
}
