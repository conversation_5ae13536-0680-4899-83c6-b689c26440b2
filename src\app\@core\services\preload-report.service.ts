import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { ReturnDefaultInterface } from '@common/interfaces/return';
import { DefaultResponseListInterface } from '@core/interfaces/default';
import { ReportPreloadEntityInterface, ReportPreloadExerciseInterface, ReportPreloadFileTypeInterface } from '@core/interfaces/preload-reports';

@Injectable({
  providedIn: 'root',
})
export class PreloadReportService {
  
  constructor(private _http: HttpClient) {}

  getAllExercises() {
    return this._http.get<ReturnDefaultInterface<DefaultResponseListInterface[]>>(
      `transparencia/pre_carregamento_relatorio/exercicio`,
    );
  }

  getAllReportType() {
    return this._http.get<ReturnDefaultInterface<DefaultResponseListInterface[]>>(
      `transparencia/pre_carregamento_relatorio/tipo_relatorio`,
    );
  }

  getExercise() {
    return this._http.get<ReturnDefaultInterface<ReportPreloadExerciseInterface[]>>(
      `transparencia/pre_carregamento_relatorio/exercicio`,
    );
  }

  getEntity(exercicieYear?: string) {
    let params = new HttpParams();
    if(exercicieYear) params = params.append('exercicio', exercicieYear);
    return this._http.get<ReturnDefaultInterface<ReportPreloadEntityInterface[]>>(
      `transparencia/pre_carregamento_relatorio/entidade_por_exercicio`,
      { params },
    );
  }

  getEntityFilters() {
    return this._http.get<ReturnDefaultInterface<ReportPreloadEntityInterface[]>>(
      `transparencia/pre_carregamento_relatorio/entidade_por_filtros`);
  }

  reportsType() {
    return this._http.get<ReturnDefaultInterface<ReportPreloadFileTypeInterface[]>>(
      `transparencia/pre_carregamento_relatorio/tipo_relatorio`,
    );
  }
}
