import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { ReturnDefaultInterface, ReturnInterface } from '@common/interfaces/return';
import {
  ReportPreloadClassificationInterface,
  reportPreloadActionTCEInterface,
  reportPreloadGoodNatureTypeTCEInterface,
} from '@core/interfaces/preload-reports';
import { take } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class PreloadTceService {

  constructor(private _http: HttpClient) { }

  getListTCEAction() {
    return this._http.get<ReturnDefaultInterface<reportPreloadActionTCEInterface[]>>(
      'transparencia/pre_carregamento_orcamento/tipo_acao_tce',
    );
  }

  getGoodNatureTypeTCE() {
    return this._http.get<
      ReturnDefaultInterface<reportPreloadGoodNatureTypeTCEInterface[]>
    >('transparencia/bens_patrimoniais/tipo_natureza_bem_tce');
  }

  getCommitmentClassification(entidadeUuid: string) {
    const params = new HttpParams({ fromObject: { entidadeUuid } });
    return this._http
      .get<ReturnDefaultInterface<ReportPreloadClassificationInterface>>(
        'transparencia/pre_carregamento_prestacao_conta/classification/effort',
        { params },
      )
      .pipe(take(1));
  }
}
