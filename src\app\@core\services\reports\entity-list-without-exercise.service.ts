import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { ReturnDefaultInterface } from '@common/interfaces/return';
import { EntityInterface } from '@core/interfaces/entity';

@Injectable({
  providedIn: 'root'
})
export class EntityListWithoutExerciseService {
  private _http = inject(HttpClient);

  getEntities(){
    return this._http.get<ReturnDefaultInterface<EntityInterface[]>>('transparencia/pre_carregamento_relatorio/entidade_por_filtros')
  }
}
