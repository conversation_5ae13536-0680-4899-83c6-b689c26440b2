import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { MenuInterface, MenuResponseInterface } from '@core/interfaces/menu';
import { MenuService } from '@pages/menu.service';
import { BehaviorSubject, forkJoin } from 'rxjs';
import { take } from 'rxjs/operators';

export type ActionDTO = 'orcamento' | 'prestacao-de-contas';

type MenuStorageDTO = Record<ActionDTO, {
  allMenu: MenuResponseInterface[];
  singleMenu: MenuInterface[];
  loading: boolean;
}>;

interface MenuStorageInterface extends MenuStorageDTO {
  allMenu: MenuResponseInterface[];
}

@Injectable({
  providedIn: 'root',
})
export class MenuStorageService {

  constructor(
    private _menuService: MenuService,
    private _router: Router,
  ) {
    this._initStorage();
  }

  private _store$ = new BehaviorSubject<MenuStorageInterface>({
    allMenu: [],
    orcamento: {
      allMenu: [],
      singleMenu: [],
      loading: true,
    },
    'prestacao-de-contas': {
      allMenu: [],
      singleMenu: [],
      loading: true,
    }
  });

  public get() {
    return this._store$.asObservable();
  }

  public set(data: MenuStorageInterface) {
    this._store$.next(data);
  }

  public get value() {
    return this._store$.getValue();
  }

  private _initStorage() {
    forkJoin({
      resMenusBudget: this._menuService.getFullMenusBudget(),
      resMenusAccountability: this._menuService.getFullMenusAccountability(),
    }).pipe(take(1))
      .subscribe(({ resMenusBudget, resMenusAccountability }) => {
        const allMenu = [...resMenusBudget, ...resMenusAccountability];
        this.set({
          allMenu,
          orcamento: this._setMenuData(resMenusBudget),
          'prestacao-de-contas': this._setMenuData(resMenusAccountability),
        });
      });
  }

  private _setMenuData(data: MenuResponseInterface[]) {
    return {
      allMenu: data,
      singleMenu: this._prepareMenuData(data),
      loading: false,
    }
  }

  private _prepareMenuData(data: MenuResponseInterface[]): MenuInterface[] {
    return data.filter((parent) => this._isParent(parent.nivel) && this._isVisable(parent.visivel))
      .map((parent) => {
        return {
          ...this._convertMenuLink(parent),
          children: data.filter((childElement) => 
            childElement.codigoPai === parent.codigo && this._isGrandsonElement(childElement.nivel) && this._isVisable(childElement.visivel))
          .map((grandsonElement) => {
            return {
              ...this._convertMenuLink(grandsonElement),
              children: data.filter((element) => element.codigoPai === grandsonElement.codigo && this._isGreatGrandsonElement(element.nivel) && this._isVisable(element.visivel))
              .map((greatGrandsonElement) => {
                return {
                  ...this._convertMenuLink(greatGrandsonElement),
                }
              })
            }
          })
        };
      });
  }

  private _isVisable(value: string) {
    return value === "1";
  }

  private _isParent(value: number) {
    return value === 1;
  }

  private _isGrandsonElement(value: number) {
    return value === 2;
  }

  private _isGreatGrandsonElement(value: number) {
    return value === 3;
  }

  private _convertMenuLink(data: MenuResponseInterface): MenuInterface {

    const dto: MenuInterface = {
      title: data.nome,
      link: '/relatorio/'+data.link,
      ordem: data.ordem,
      status: "ACTIVE",
      description: data.descricao,
      code: data.codigo.toString(),
    }

    if (!data.classeFontAwesome) {
      delete dto.icon;
    }

    return dto;
  }

  public getMenuCurrentAction(): MenuResponseInterface | null {

    const nameAction = this._preparingUrlCompareAction();
    const reportLevel = 3;
    const allMenuMenuData = this._store$.getValue().allMenu;

    const filter = allMenuMenuData
      .filter(data =>
        data.link &&
        data.link.includes(nameAction) &&
        data.nivel === reportLevel
    );

    if (filter && filter[0]) {
      return filter[0];
    }

    return null;
  }

  private _preparingUrlCompareAction(): string {
    const url = this._router.url;
    const clearParams = url.split('?')[0];
    const paths = clearParams.split('/').filter(value => value);
    
    return `${paths[1]}/${paths[2]}/${paths[3]}`;
  }
  
}
