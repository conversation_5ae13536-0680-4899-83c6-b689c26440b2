import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Params } from '@angular/router';
import { ReturnDefaultInterface } from '@common/interfaces/return';
import { OrganInterface } from '@pages/planning/organs-unities/interfaces/organ';
import { UnityInterface } from '@pages/planning/organs-unities/interfaces/unity';
import { take, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class OrganUnitsService {
  constructor(private _http: HttpClient) {}

  getOrgansByEntity(
    entityUuid: string,
    exercise?: number,
    versionUuid?: string,
  ) {
    let params = new HttpParams();
    params = params.append('entityUuid', entityUuid);
    if (exercise) {
      params = params.append('exercicio', exercise.toString());
    }
    if (versionUuid) {
      params = params.append('versaoUuid', versionUuid);
    }
    return this._http.get<ReturnDefaultInterface<OrganInterface[]>>(
      `transparencia/pre_carregamento_orcamento/orgao/paginado`,
      {
        params,
      },
    );
  }

  getUnitsByOrgan(entityUuid: string, exercise: number, organCode: string) {
    let params = new HttpParams();
    params = params.append('entityUuid', entityUuid);
    params = params.append('exercicio', exercise.toString());
    params = params.append('codigoOrgan', organCode);

    return this._http.get<ReturnDefaultInterface<UnityInterface[]>>(
      `transparencia/pre_carregamento_orcamento/unidade/paginado`,
      {
        params,
      },
    );
  }

  getOrgans(queryParams?: Params) {
    const params = new HttpParams({ fromObject: queryParams });

    return this._http
      .get<ReturnDefaultInterface<OrganInterface[]>>(
        `transparencia/pre_carregamento_prestacao_conta/orgao/paginado`,
        {
          params,
        },
      )
      .pipe(
        take(1),
        tap((res) => {
          res.data.forEach(
            (organ) => (organ['codigoNome'] = `${organ.codigo} - ${organ.nome}`),
          );
        }),
      );
  }

  getUnits(queryParams?: Params) {
    const params = new HttpParams({ fromObject: queryParams });

    return this._http
      .get<ReturnDefaultInterface<UnityInterface[]>>(
        `transparencia/pre_carregamento_prestacao_conta/unidade/paginado`,
        {
          params,
        },
      )
      .pipe(
        take(1),
        tap((res) => {
          res.data.forEach(
            (unity) => (unity['codigoNome']  = `${unity.codigo} - ${unity.nome}`),
          );
        }),
      );
  }
}
