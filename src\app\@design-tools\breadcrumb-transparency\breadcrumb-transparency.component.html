<nav aria-label="breadcrumb">
  <ng-container *ngIf="loading; else breadcrumbTemplate">
      <p class="container">
      <ng-container *ngFor="let item of [1, 2, 3]; let last = last; let first = first">
        <ng-container *ngIf="!first">
          &nbsp;&nbsp;<span class="arrow">❯</span>&nbsp;&nbsp;
        </ng-container>
        <span [ngClass]="{'active': last }" >
          <eqp-skeleton height="20px" maxHeight="20px"></eqp-skeleton>
        </span>
      </ng-container>
    </p>
  </ng-container>
</nav>

<ng-template #breadcrumbTemplate>
  <p class="mb-0">
    <ng-container *ngIf="showInitLink">
      <span (click)="redirectToInit()">Início</span>
    </ng-container>

    <ng-container *ngFor="let item of breadcrumbs; let i = index; let last = last">
      <ng-container *ngIf="(showInitLink && i === 0) || i > 0">
        <span class="arrow">❯</span>
      </ng-container>

      <span [style.font-size.px]="fontSize" [ngClass]="{ 'active': last }">
        {{ item.title }}
      </span>
    </ng-container>
  </p>
</ng-template>