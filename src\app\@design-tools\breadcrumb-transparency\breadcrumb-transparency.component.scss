:host {
    display: block;
}

.container {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    list-style: none;
    margin-bottom: 0;
    align-items: center;
}

span {
  color: var(--menu-text-color);         
  font-size: 11px;
  font-weight: 600;
  opacity: 0.6;                          

  &.active {
    color: var(--menu-item-active-text-color); 
    opacity: 1;                                 
  }
}

span:not(.active):hover {
  color: var(--text-primary-hover-color); 
  opacity: 1;                            
}

.arrow {
    color: #6c757d;
    padding-right: 0.2rem;
    padding-left: 0.2rem;
    font-weight: 600;
}
