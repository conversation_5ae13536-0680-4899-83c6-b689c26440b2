import { Component, Input, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { EqpNbMenuItem } from '@common/interfaces/dtos/menu'
import { MenuInterface } from '@core/interfaces/menu'
import {
  ActionDTO,
  MenuStorageService as MenuStorageTransparencyService,
} from '@core/services/reports/menu-storage.service'
import { MenuStorageService } from '@core/utils/menu-storage.service'
import { BreadCrumpInterface } from '@design-tools/interfaces/breadcrumb'
import { combineLatest, Subject } from 'rxjs'
import { filter } from 'rxjs/operators'

@Component({
  selector: 'eqp-breadcrumb-transparency',
  templateUrl: './breadcrumb-transparency.component.html',
  styleUrls: ['./breadcrumb-transparency.component.scss'],
})
export class BreadcrumbTransparencyComponent implements OnInit {
  @Input() fontSize: string
  @Input() showInitLink: boolean = true
  @Input() menuAction: ActionDTO

  public breadcrumbs: BreadCrumpInterface[] = []
  public loading: boolean = true

  private _destroy$: Subject<void> = new Subject<void>()

  constructor(
    private _menuStorageTransparencyService: MenuStorageTransparencyService,
    private _menuStorageService: MenuStorageService,
    private _router: Router,
    private _route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.menuAction = this._route.snapshot.queryParams['action']
    this._getStorage()
  }

  ngOnDestroy(): void {
    this._destroy$.next()
    this._destroy$.complete()
  }

  private _getStorage(): void {
    combineLatest([
      this._menuStorageTransparencyService.get(),
      this._menuStorageService.store.selectAll(),
    ])
      .pipe(
        filter(
          ([transparency]) =>
            transparency[this.menuAction].singleMenu.length > 0,
        ),
      )
      .subscribe(([transparency, currentModule]) => {
        const transparencyMenus = transparency[this.menuAction].singleMenu[0]
        const currentModuleMenus = currentModule.allMenu

        const transparencyPath =
          this._generateBreadcrumbTransparency(transparencyMenus)
        const currentModulePath = this._generateBreadcrumb(currentModuleMenus)

        this.breadcrumbs = [...currentModulePath, ...transparencyPath]

        this.loading = false
      })
  }

  private _generateBreadcrumbTransparency(
    transparencyMenus: MenuInterface,
  ): BreadCrumpInterface[] {
    const reportPath = this._router.url
    let breadcrumbPath: { title: string; link: string }[] = []

    breadcrumbPath = this._findReportPath(
      transparencyMenus.children,
      reportPath,
      [],
    )

    if (breadcrumbPath.length) {
      const breadcrumbs = breadcrumbPath as unknown as BreadCrumpInterface[]
      return breadcrumbs
    }

    return []
  }

  private _generateBreadcrumb(
    currentModuleMenus: EqpNbMenuItem[],
  ): BreadCrumpInterface[] {
    const segments = ['menu', this.menuAction]

    let breadcrumbPath: EqpNbMenuItem[] = []
    do {
      breadcrumbPath = this._findPath(currentModuleMenus, segments)
      segments.pop()
    } while (segments.length >= 1 && breadcrumbPath.length == 0)

    if (breadcrumbPath.length) {
      const breadcrumbs = breadcrumbPath as unknown as BreadCrumpInterface[]
      return breadcrumbs
    }

    return []
  }

  private toTitleCase(str: string): string {
    const siglas = ['PR', 'TCE', 'LOA', 'LDO', 'PPA', 'RPPS', 'QDD']
    const listaPalavrasLigacao = [
      'de',
      'do',
      'da',
      'dos',
      'das',
      'e',
      'por',
      'com',
      'para',
    ]

    return str
      .toLowerCase()
      .split(' ')
      .map(word => {
        const upper = word.toUpperCase()

        // Extrai apenas letras para verificar se é sigla
        const letrasApenas = word.replace(/[^a-zA-Z]/g, '').toUpperCase()

        if (siglas.includes(upper) || siglas.includes(letrasApenas)) {
          return upper
        }

        if (listaPalavrasLigacao.includes(word)) {
          return word
        }

        return word.charAt(0).toUpperCase() + word.slice(1)
      })
      .join(' ')
  }

  private _findReportPath(
    items: MenuInterface[],
    url: string,
    parentItem: { title: string; link: string }[] = [],
  ): { title: string; link: string }[] {
    const cleanUrl = url.split('?')[0].split('#')[0]

    for (const node of items) {
      const path = [
        ...parentItem,
        { title: this.toTitleCase(node.title), link: node.link },
      ]

      if (node.link === cleanUrl) {
        return path
      }

      if (node?.children?.length) {
        const found = this._findReportPath(node.children, cleanUrl, path)
        if (found.length) {
          return found
        }
      }
    }

    return []
  }

  private _findPath(
    tree: EqpNbMenuItem[],
    segments: string[],
    path: EqpNbMenuItem[] = [],
  ): EqpNbMenuItem[] {
    for (const node of tree) {
      const nodeLink = node.link?.split('/').filter(Boolean) ?? []
      const match = segments.join('/') === nodeLink.join('/')

      const newPath = [...path, node]

      if (match) {
        return newPath
      }

      if (node.children?.length) {
        const result = this._findPath(node.children, segments, newPath)
        if (result.length) {
          return result
        }
      }
    }

    return []
  }

  public redirectToInit(): void {
    this._router.navigate(['/'])
  }
}
