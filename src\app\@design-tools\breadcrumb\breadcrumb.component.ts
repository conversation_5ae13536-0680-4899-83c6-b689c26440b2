import {
  Component,
  Input,
  OnDestroy,
  OnInit
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { EqpNbMenuItem } from '@common/interfaces/dtos/menu';
import { MenuStorageService } from '@core/utils/menu-storage.service';
import { BreadCrumpInterface } from '@design-tools/interfaces/breadcrumb';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss'],
})
export class BreadcrumbComponent implements OnInit, OnDestroy {
  @Input() breadcrumbInfo: {
    manually: boolean, // Indica se o breadcrumb foi configurado manualmente ou se foi gerado automaticamente
    path: string // Caminho do breadcrumb
  } = {
    manually: false,
    path: '',
  };
  @Input() fontSize: string
  @Input() showInitLink: boolean = true;

  public breadcrumbs: BreadCrumpInterface[] = [];
  public loading: boolean = true;

  private _menuList: EqpNbMenuItem[] = [];
  private _destroy$: Subject<void> = new Subject<void>();

  constructor(
    private _menuStorageService: MenuStorageService,
    private _router: Router,
  ) {}

  ngOnInit(): void {
    this._getStorage();
  }

  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  private _getStorage(): void {
    this._menuStorageService.store
      .selectAll()
      .pipe(takeUntil(this._destroy$))
      .subscribe(({ loading, allMenu }) => {
        this.loading = loading;
        this._menuList = allMenu;
        if(this.breadcrumbInfo.manually) {
          this._generateBreadcrumb(this.breadcrumbInfo.path)
        } else {
          this._setBreadcrumbs();
          this._routerNavigationEnd();
        }
      });
  }

  private _routerNavigationEnd() {
    this._router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this._destroy$),
      )
      .subscribe(() => {
        this._setBreadcrumbs();
      });
  }

  private _setBreadcrumbs(): void {
    const path = this._router.url;
    this._generateBreadcrumb(path);
  }

  private _generateBreadcrumb(path: string): void {
    const segments = path.split('?')[0].split('/').filter(Boolean)
    let breadcrumbPath: EqpNbMenuItem[] = []
    do {
      breadcrumbPath = this._findPath(this._menuList, segments)
      segments.pop()
    } while(segments.length >= 1 && breadcrumbPath.length == 0)
    if (breadcrumbPath.length) {
      this.breadcrumbs = breadcrumbPath as unknown as BreadCrumpInterface[];
    }
  }

  private _findPath(
    tree: EqpNbMenuItem[],
    segments: string[],
    path: EqpNbMenuItem[] = []
  ): EqpNbMenuItem[] {
    for (const node of tree) {
      const nodeLink = node.link?.split('/').filter(Boolean) ?? [];
      const match = segments.join('/') === nodeLink.join('/');

      const newPath = [...path, node];

      if (match) {
        return newPath;
      }

      if (node.children?.length) {
        const result = this._findPath(node.children, segments, newPath);
        if (result.length) {
          return result;
        }
      }
    }

    return [];
  }

  public redirectToInit(): void {
    this._router.navigate(['/']);
  }
}
