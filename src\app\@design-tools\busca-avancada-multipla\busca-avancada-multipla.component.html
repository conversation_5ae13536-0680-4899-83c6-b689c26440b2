<label class="label">{{ required ? label + ' *' : label }}</label>
<div class="d-flex w-100" style="gap: 0.5rem" [formGroup]="model">
  <nb-form-field>
    <input
      nbInput
      [fullWidth]="true"
      [fieldSize]="'small'"
      [formControlName]="'codigo'"
      [placeholder]="codeLabel"
      [type]="inputType"
      [readonly]="disabled || disabledCodeInput"
      [tabIndex]="tabIndex"
    />
    <button
      [size]="'small'"
      [title]="'Buscar'"
      nbSuffix
      nbButton
      ghost
      [disabled]="disabled"
      [hidden]="disabled || hideButton"
      (click)="onButtonClick()"
      tabIndex="-1"
    >
      <nb-icon icon="search" pack="eva"></nb-icon>
    </button>
  </nb-form-field>
  <button
    (click)="abrirModalVisualizar()"
    status="primary"
    class="botao-visualizar"
    size="small"
    [disabled]="obterDadosDataSource().length === 0 || disabled"
    nbButton
  >
    <i class="fas fa-eye mr-1"></i> Visualizar registros selecionados
  </button>
</div>
<div
  *ngIf="displayError"
  class="invalid-feedback"
  [ngClass]="{
    'd-block': true
  }"
>
  <div>{{ label + ' é obrigatório' }}</div>
</div>
<div
  *ngIf="
    getAbsControl()?.errors?.customError &&
    (getAbsControl()?.touched || !getAbsControl()?.pristine)
  "
  class="invalid-feedback"
  [ngClass]="{
    'd-block': getAbsControl()?.errors
  }"
>
  <div>{{ getAbsControl()?.errors?.customError }}</div>
</div>
