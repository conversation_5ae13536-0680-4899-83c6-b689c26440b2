import { Component, Input, OnInit, ViewChild } from '@angular/core';
import {
  Control<PERSON><PERSON>r,
  FormBuilder,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { DefaultSearchDialogComponent } from '@pages/planning/shared/searchs/default-search-dialog/default-search-dialog.component';
import { SEARCH_COLUMNS } from '@pages/planning/shared/searchs/default-search-dialog/search-columns';
import { BuscaAvancadaMultiplaVisualizarComponent } from '@pages/shared/components/busca-avancada-multipla-visualizar/busca-avancada-multipla-visualizar.component';
import { DxSelectBoxComponent } from 'devextreme-angular';
import DataSource from 'devextreme/data/data_source';
import { Subject, from } from 'rxjs';
import { debounceTime, filter, take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-busca-avancada-multipla',
  templateUrl: './busca-avancada-multipla.component.html',
  styleUrls: ['./busca-avancada-multipla.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: BuscaAvancadaMultiplaComponent,
      multi: true,
    },
  ],
})
export class BuscaAvancadaMultiplaComponent implements OnInit {
  @Input() tabIndex: number;
  @Input() label: string = 'Texto Padrão';
  @Input() searchColumnsType: string = '';
  @Input() codeLabel: string = 'Código';
  @Input() uri = 'plano_despesa/paginado';
  @Input() filter: any[];
  @Input() dialogTitle = this.label;
  @Input() multipleNames: string[];
  @Input() idKey = 'uuid';
  @Input() hideName = false;
  @Input() returnAllData = false;
  @Input() hideButton = false;
  @Input() disabled = false;
  @Input() disabledCodeInput = false;
  @Input() required = false;
  @Input() displayError = false;
  @Input() codeInputWidth = null;
  @Input() nameLabel = 'Selecionadas';
  @Input() searchPanelVisible = true;
  @Input() dataSourceLookup: any[];
  @Input() objIsData = true;
  @Input() inputType: string = 'text';
  @Input() primaryMask?: string = null;
  @Input() messageNotFound = 'Plano despesa não encontrado.';
  @Input() waitingTime: number = 2000;
  @Input() allowSort = false;
  @Input() formControlName: string;
  @Input() onlyNumberKey: boolean = false;
  @Input() allowPagination: boolean = true;
  @Input() isMultiple = false
  @Input() codeKey = 'codigo';
  @Input() customFilterExpression: Function
  @Input() manipulateDataFn: (dados: any[]) => any[];
  @Input() manipularIdKey: (item: any) => any;
  @Input() mascara: string
  @ViewChild('dxSelectBox') dxSelectBox: DxSelectBoxComponent;
  public planosDespesaSelecionados: DataSource = new DataSource([]);
  private touched = false;
  private unsub$ = new Subject();
  public model: FormGroup;

  constructor(
    private _dialogService: NbDialogService,
    private _builder: FormBuilder,
    private _crudService: CrudService,
    private _controlContainer: ControlContainer,
    private _toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.model = this._builder.group({
      codigo: [],
      descricao: [],
    });
    this.carregarHandlers();
  }

  ngOnDestroy(): void {
    this.unsub$.next();
    this.unsub$.complete();
  }

  public getAbsControl(): any {
    if (!this._controlContainer) return null;
    return this._controlContainer.control.get(this.formControlName);
  }

  onChange = (value: any) => {};

  onTouched = () => {};

  writeValue(obj: any): void {
    if (obj) {
      this.atualizarDataSource(obj);
    } else {
      this.model.reset(undefined, { emitEvent: false });
    }
    this.markAsTouched();
  }

  registerOnChange(fn: (value: any) => void) {
    this.onChange = fn;
  }

  registerOnTouched(touched: any) {
    this.onTouched = touched;
  }

  private carregarHandlers() {
    this.model
      .get('codigo')
      .valueChanges.pipe(debounceTime(this.waitingTime), takeUntil(this.unsub$))
      .subscribe((value) => {
        this.carregarPlanoPorCodigo(value);
        this.markAsTouched();
      });
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  private async carregarPlanoPorCodigo(value: string): Promise<void> {
    if (this.verificarValorVazio(value)) {
      this.limparFormulario();
      return;
    }

    try {
      const dataSource = this.criarDataSource(value);
      const planoDespesa = await dataSource.load();

      if (this.validarValorVazio(planoDespesa)) {
        this.mensagemNaoEncontrado();
        return;
      }

      this.mensagemSucesso();
      this.exibirInformacoes(planoDespesa);
    } catch (error) {
      this.mensagemErro(error);
    }
  }

  private verificarValorVazio(value: string): boolean {
    return !value || value.trim() === '';
  }

  private criarDataSource(value: any) {
    const uri = this.uri;

    if (this.filter) {
      return new DataSource({
        store: this._crudService.getDataSourceFiltro(
          'uuid',
          uri,
          1,
          'codigo',
          value,
        ),
        filter: this.filter,
      });
    }

    return this._crudService.getDataSourceFiltro(
      'uuid',
      uri,
      1,
      'codigo',
      value,
    );
  }

  private validarValorVazio(results: any[]): boolean {
    return results.length === 0;
  }

  private mensagemNaoEncontrado(): void {
    this.mensagemErro(this.messageNotFound || 'Nenhum registro encontrado.');
    this.limparFormulario();
  }

  private limparFormulario(): void {
    this.onChange([]);
    this.model.reset({}, { emitEvent: false });
  }

  private mensagemErro(message: string): void {
    this._toastr.send({
      error: true,
      title: 'Erro',
      message,
    });
  }

  private mensagemSucesso(message: string = 'Registro encontrado com sucesso.'): void {
    this._toastr.send({
      success: true,
      message,
    });
  }

  onButtonClick(): void {
    const dialogRef = this._dialogService.open(DefaultSearchDialogComponent, {
      context: {
        searchData: {
          uri: this.uri,
          dialogTitle: this.dialogTitle,
          columns: SEARCH_COLUMNS[`${this.searchColumnsType}`],
          filter: this.filter,
          isMultiple: true,
        },
        objIsData: this.objIsData,
        allowSort: this.allowSort,
        codeKey: this.codeKey,
        customFilterExpression: this.customFilterExpression,
        manipulateDataFn: this.manipulateDataFn
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    });

    dialogRef.onClose.pipe(filter((res) => res)).subscribe((res: any[]) => {
      this.exibirInformacoes(res);
      this.exibirDadosUltimoRegistroSelecionado();
    });
  }

  public obterDadosDataSource() {
    let dados = [];

    from(this.planosDespesaSelecionados.store().load()).subscribe((res) => {
      dados = res;
    });

    return dados;
  }

  private atualizarFormControl() {
    const dataSource = this.obterDadosDataSource();
    const newData = this.returnAllData 
      ? dataSource 
      : dataSource.map(item => this.manipularIdKey?.(item[this.idKey]) ?? item[this.idKey]);

    this.onChange(newData);
  }

  private exibirInformacoes(novosPlanos: any[]): void {
    const planosAtuais = this.obterDadosDataSource();
    const planosAtualizados = this.obterPlanosNaoDuplicados(
      planosAtuais,
      novosPlanos,
    );

    this.atualizarDataSource(planosAtualizados);
    this.atualizarFormControl();
  }

  public abrirModalVisualizar() {
    const ref = this._dialogService.open(
      BuscaAvancadaMultiplaVisualizarComponent,
      {
        context: {
          dataSource: this.planosDespesaSelecionados,
          columns: SEARCH_COLUMNS[`${this.searchColumnsType}`],
          customFilterExpression: this.customFilterExpression
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      },
    );

    ref.onClose.pipe(take(1)).subscribe((_) => {
      this.atualizarFormControl();
      this.exibirDadosUltimoRegistroSelecionado();
    });
  }

  private obterPlanosNaoDuplicados(
    planosAtuais: any[],
    novosPlanos: any[],
  ): any[] {
    return [
      ...planosAtuais,
      ...novosPlanos.filter(
        (planoNovo) =>
          !planosAtuais.some(
            (planoExistente) => planoExistente.uuid === planoNovo.uuid,
          ),
      ),
    ];
  }

  private atualizarDataSource(dados: any[]): void {
    const dadosManipulados = this.manipulateDataFn ? this.manipulateDataFn(dados) : dados;

    this.planosDespesaSelecionados = new DataSource({
      store: {
        data: dadosManipulados,
        key: 'uuid',
        type: 'array',
      },
      paginate: false,
      pageSize: 10,
    });
  }

  private exibirDadosUltimoRegistroSelecionado() {
    const ultimoRegistroSelecionado = this.obterDadosDataSource().slice(-1)[0];
    this.model.get('codigo').setValue(ultimoRegistroSelecionado?.codigo, { emitEvent: false });
  }
}
