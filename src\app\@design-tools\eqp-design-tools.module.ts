import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { NbMomentDateModule } from '@nebular/moment'
import {
  NbAccordionModule,
  NbActionsModule,
  NbAlertModule,
  NbAutocompleteModule,
  NbButtonModule,
  NbCardModule,
  NbCheckboxModule,
  NbContextMenuModule,
  NbDatepickerModule,
  NbFormFieldComponent,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbListModule,
  NbPopoverModule,
  NbProgressBarModule,
  NbRadioModule,
  NbSelectModule,
  NbSpinnerModule,
  NbTabsetModule,
  NbToggleModule,
  NbTooltipModule,
  NbWindowModule,
} from '@nebular/theme'
import { TextMaskModule } from 'angular2-text-mask'
import {
  DxButtonModule,
  DxChartModule,
  DxCheckBoxModule,
  DxDataGridModule,
  DxDateBoxModule,
  DxDropDownBoxModule,
  DxHtmlEditorModule,
  DxListModule,
  DxPieChartModule,
  DxPivotGridModule,
  DxPopoverModule,
  DxSelectBoxModule,
  DxTabPanelModule,
} from 'devextreme-angular'
import { ImageCropperModule } from 'ngx-image-cropper'

import { DxBarChartComponent } from './dx-bar-chart/dx-bar-chart.component'
import { DxStandardGridComponent } from './dx-standard-grid/dx-standard-grid.component'
import { ChooseFilesComponent } from './file-handling/choose-files/choose-files.component'
import { FileInputComponent } from './file-handling/file-input/file-input.component'
import { FileParser } from './file-handling/file-parser'
import { DragDropDirective } from './image-handling/directives/dragDrop.directive'
import { ImgAdjusterComponent } from './image-handling/img-adjuster/img-adjuster.component'
import { ImgContainerComponent } from './image-handling/img-container/img-container.component'
import { ImageUploadComponent } from './image-upload/image-upload.component'
import { LoadingComponent } from './loading/loading.component'
import { NebularAccordionComponent } from './nebular-accordion/nebular-accordion.component'
import { NebularAutoCompleteComponent } from './nebular-auto-complete/nebular-auto-complete.component'
import { NebularButtonComponent } from './nebular-button/nebular-button.component'
import { NebularCardComponent } from './nebular-card/nebular-card.component'
import { NebularCheckboxComponent } from './nebular-checkbox/nebular-checkbox.component'
import { NebularDialogComponent } from './nebular-dialog/nebular-dialog.component'
import { NebularInputComponent } from './nebular-input/nebular-input.component'
import { NebularSelectComponent } from './nebular-select/nebular-select.component'
import { NebularToggleComponent } from './nebular-toggle/nebular-toggle.component'
import { NebularWindowComponent } from './nebular-window/nebular-window.component'
import { StandardPageComponent } from './standard-page/standard-page.component'
import { SuggestionBoxComponent } from './suggestion-box/suggestion-box.component'
import { ColumnDirective } from './wrapped-dx-grid/directives/column.directive'
import { WrappedDxGridComponent } from './wrapped-dx-grid/wrapped-dx-grid.component'
import { NgxMaskModule } from 'ngx-mask'
import { NgxCurrencyModule } from 'ngx-currency'
import { NebularSearchFieldComponent } from './nebular-search-field/nebular-search-field.component'
import { FieldsetComponent } from './fieldset/fieldset.component'
import { FieldToggleComponent } from './field-toggle/field-toggle.component'
import { SearchFieldComponent } from './search-field/search-field.component'
import { MultipleNebularSelectComponent } from './multiple-nebular-select/multiple-nebular-select.component'
import { PeriodFieldComponent } from './period-field/period-field.component'
import { NebularDateComponent } from './nebular-date/nebular-date.component';
import { MultipleNebularCheckboxComponent } from './multiple-nebular-checkbox/multiple-nebular-checkbox.component'
import { FieldDateComponent } from './field-date/field-date.component'
import { RelatorioTemplateComponent } from './relatorio-template/relatorio-template.component'
import { InputNumberDirective } from '@common/directives/input-number.directive'

@NgModule({
  declarations: [
    StandardPageComponent,
    NebularButtonComponent,
    NebularInputComponent,
    NebularSelectComponent,
    NebularToggleComponent,
    DxStandardGridComponent,
    NebularCardComponent,
    NebularCheckboxComponent,
    NebularDateComponent,
    NebularWindowComponent,
    NebularDialogComponent,
    NebularAutoCompleteComponent,
    NebularAccordionComponent,
    SuggestionBoxComponent,
    WrappedDxGridComponent,
    DxBarChartComponent,
    ColumnDirective,
    FileInputComponent,
    ChooseFilesComponent,
    ImageUploadComponent,
    LoadingComponent,
    ImgContainerComponent,
    ImgAdjusterComponent,
    DragDropDirective,
    NebularSearchFieldComponent,
    FieldsetComponent,
    FieldToggleComponent,
    SearchFieldComponent,
    MultipleNebularSelectComponent,
    PeriodFieldComponent,
    MultipleNebularCheckboxComponent,
    FieldDateComponent,
    RelatorioTemplateComponent,
    InputNumberDirective,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NbCardModule,
    NbButtonModule,
    NbIconModule,
    NbInputModule,
    NbSelectModule,
    NbToggleModule,
    NbCheckboxModule,
    NbTooltipModule,
    NbRadioModule,
    NbProgressBarModule,
    NbDatepickerModule,
    NbMomentDateModule,
    NbSpinnerModule,
    NbAccordionModule,
    NbListModule,
    NbAutocompleteModule,
    NbFormFieldModule,
    NbListModule,
    NbAlertModule,
    NbCardModule,
    NbTabsetModule,
    NbPopoverModule,
    NbAccordionModule,
    NbContextMenuModule,
    NbActionsModule,
    NbWindowModule.forChild(),
    DxDataGridModule,
    DxDateBoxModule,
    DxPopoverModule,
    DxPieChartModule,
    DxChartModule,
    DxSelectBoxModule,
    DxHtmlEditorModule,
    DxTabPanelModule,
    DxCheckBoxModule,
    ImageCropperModule,
    TextMaskModule,
    NgxMaskModule.forRoot(),
    NgxCurrencyModule,
    DxPivotGridModule,
    DxDropDownBoxModule,
    DxListModule,
    DxButtonModule
  ],
  exports: [
    NbListModule,
    NbAlertModule,
    NbCardModule,
    NbTabsetModule,
    NbPopoverModule,
    NbAccordionModule,
    NbRadioModule,
    NbContextMenuModule,
    NbActionsModule,
    NbIconModule,
    NbTooltipModule,
    NbSpinnerModule,
    DxDataGridModule,
    DxDateBoxModule,
    DxPopoverModule,
    DxPieChartModule,
    DxChartModule,
    DxHtmlEditorModule,
    DxTabPanelModule,
    DxCheckBoxModule,
    NbSelectModule,
    NebularButtonComponent,
    NebularInputComponent,
    NebularSelectComponent,
    NebularToggleComponent,
    DxStandardGridComponent,
    NebularCardComponent,
    NebularCheckboxComponent,
    NebularDateComponent,
    NebularWindowComponent,
    NebularDialogComponent,
    NebularAutoCompleteComponent,
    NebularAccordionComponent,
    NebularSearchFieldComponent,
    WrappedDxGridComponent,
    NbFormFieldComponent,
    DxBarChartComponent,
    ColumnDirective,
    StandardPageComponent,
    SuggestionBoxComponent,
    FileInputComponent,
    ChooseFilesComponent,
    ImageUploadComponent,
    ImgContainerComponent,
    ImgAdjusterComponent,
    LoadingComponent,
    DragDropDirective,
    NgxCurrencyModule,
    NbInputModule,
    NbToggleModule,
    DxPivotGridModule,
    FieldsetComponent,
    FieldToggleComponent,
    DxDropDownBoxModule,
    DxListModule,
    SearchFieldComponent,
    MultipleNebularSelectComponent,
    PeriodFieldComponent,
    MultipleNebularCheckboxComponent,
    FieldDateComponent,
    RelatorioTemplateComponent,
    InputNumberDirective,
  ],
  entryComponents: [ChooseFilesComponent],
  providers: [FileParser],
})
export class EqpDesignToolsModule {}
