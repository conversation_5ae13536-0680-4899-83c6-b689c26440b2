<div class="form-control-group">
  <label class="label" *ngIf="label" [attr.aria-label]="label" for="{{ name }}"
    >{{ required ? label + ' *' : label }}
    <nb-icon
      *ngIf="showIcon"
      [nbTooltip]="tooltipText"
      [nbTooltipStatus]="tooltipStatus"
      [icon]="icon"
      pack="eva"
      [status]="iconStatus"
      [options]="{ animation: { type: 'pulse', infinite: true } }"
    ></nb-icon
  ></label>
  <div class="position-relative input-group">
    <input
      #dateInput
      nbInput
      [nbDatepicker]="datepicker"
      fieldSize="small"
      [textMask]="{ mask: configuration.maskRegex }"
      [fullWidth]="true"
      [class]="class"
      [disabled]="disabled"
      [name]="name"
      [(ngModel)]="currentValue"
      [placeholder]="placeholder"
      [readonly]="readonly"
      [required]="required"
      (ngModelChange)="changeInput($event)"
      [tabindex]="tabIndex"
    />
    <nb-icon
      [options]="{ animation: { type: 'pulse' } }"
      icon="calendar"
      status="info"
      class="absolute-right pointer calendar-accessor"
      (click)="datepicker?.isShown ? null : dateInput.click()"
    ></nb-icon>
    <nb-datepicker
      #datepicker
      (dateChange)="changeValue($event)"
      [format]="configuration.displayFormat"
    ></nb-datepicker>
  </div>
</div>
<div
  *ngIf="
    getAbsControl()?.errors?.required &&
    (getAbsControl()?.touched || !getAbsControl()?.pristine)
  "
  class="invalid-feedback"
  [ngClass]="{
    'd-block': getAbsControl()?.errors
  }"
>
  <div>{{ label + ' é obrigatório' }}</div>
</div>
<div
  *ngIf="
    getAbsControl()?.errors?.customError &&
    (getAbsControl()?.touched || !getAbsControl()?.pristine)
  "
  class="invalid-feedback"
  [ngClass]="{
    'd-block': getAbsControl()?.errors
  }"
>
  <div>{{ getAbsControl()?.errors?.customError }}</div>
</div>
