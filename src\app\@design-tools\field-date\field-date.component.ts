import {
  Component,
  EventEmitter,
  Host,
  Input,
  OnInit,
  Optional,
  Output,
  SkipSelf,
} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';

import moment from 'moment';

type DateConfig = {
  inputFormat: string;
  outputFormat: string;
  displayFormat: string;
  maskRegex: any;
};

@Component({
  selector: 'eqp-field-date',
  templateUrl: './field-date.component.html',
  styleUrls: ['./field-date.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: FieldDateComponent,
    },
  ],
})
export class FieldDateComponent implements ControlValueAccessor, OnInit {
  @Input() formControlName: string;
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() required = false;
  @Input() label = '';
  @Input() name = '';
  @Input() class = '';
  @Input() tabIndex: number;
  @Input() placeholder = '00/00/0000';
  @Input() public inputChange?: ($event: any, that: any) => any;
  @Input() public this?: any;
  @Input() public showIcon: boolean = false;
  @Input() public tooltipText: string = 'Informação';
  @Input() public icon: string = 'question-mark-circle-outline';
  @Input() public iconStatus:
    | 'primary'
    | 'warning'
    | 'basic'
    | 'info'
    | 'success'
    | 'danger'
    | 'control' = 'primary';
  @Input() public tooltipStatus:
    | 'primary'
    | 'warning'
    | 'basic'
    | 'info'
    | 'success'
    | 'danger'
    | 'control' = 'primary';

  @Output() onInputChange = new EventEmitter<any>();

  currentValue: string;
  configuration: DateConfig;

  constructor(
    @Optional()
    @Host()
    @SkipSelf()
    private controlContainer: ControlContainer,
  ) {}

  public getAbsControl(): any {
    if (!this.controlContainer) return null;
    return this.controlContainer.control.get(this.formControlName);
  }

  onChanged(value?: any) {}

  onTouched: (value?: any) => {};

  ngOnInit(): void {
    this.configuration = {
      displayFormat: 'DD[/]MM[/]yyyy',
      inputFormat: 'DD/MM/yyyy',
      maskRegex: [
        /[0-3]/,
        /\d/,
        '/',
        /[0-1]/,
        /\d/,
        '/',
        /\d/,
        /\d/,
        /\d/,
        /\d/,
      ],
      outputFormat: 'yyyy-MM-DD',
    };
    if(this.readonly)
      this.tabIndex = -1  
  }

  set value(value: any) {
    this.currentValue = value
      ? moment(value).format(this.configuration.inputFormat)
      : null;
  }

  changeInput(value: string) {
    const field = this.controlContainer.control.get(this.formControlName);
    if (value.includes('_')) {
      field.markAsPending();
    } else {
      const [d, m, y] = value.split('/');
      field.patchValue(`${y}-${m}-${d}`);
      this.onChanged(`${y}-${m}-${d}`);
    }

    this.onInputChange.emit(value);
    if (!value) field.reset();
    this.onTouched();
  }

  changeValue(value: any) {
    const { inputFormat, outputFormat } = this.configuration;
    this.currentValue = value.format(inputFormat);
    const outValue = value.format(outputFormat);
    this.onChanged(outValue);
    this.onTouched();
  }

  writeValue(value: any) {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChanged = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean) {
    this.disabled = isDisabled;
  }

  public modelChanged(event: any): void {
    this.onChanged(event);
    if (this.inputChange) {
      this.inputChange(event, this.this);
    }
  }
}
