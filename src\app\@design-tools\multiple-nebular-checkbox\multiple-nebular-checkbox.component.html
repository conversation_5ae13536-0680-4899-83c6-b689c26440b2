<div *ngIf="erroEstrutura; else estruturaValida" class="invalid-feedback d-block">
  Erro: Estrutura inválida. Verifique as informações passadas para componente
  novamente.
</div>
<ng-template #estruturaValida>
  <div class="form-control-group flex-control d-flex flex-column">
    <nb-checkbox
      *ngFor="let item of checkboxItens"
      [status]="status"
      [disabled]="disabled"
      [indeterminate]="indeterminate"
      [checked]="isChecked(item.chave)"
      [ngClass]="{
        'is-invalid': formControl?.invalid && !formControl?.pristine
      }"
      (checkedChange)="checkboxChange($event, item.chave)"
      (click)="onClick($event)"
    >
      {{ item.valor }}
    </nb-checkbox>
  </div>
</ng-template>
