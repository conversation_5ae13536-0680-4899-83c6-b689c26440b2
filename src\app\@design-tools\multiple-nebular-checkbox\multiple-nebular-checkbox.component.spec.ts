import { ComponentFixture, TestBed } from '../../../../types/@angular/core/testing';

import { MultipleNebularCheckboxComponent } from '../../../../types/@design-tools/multiple-nebular-checkbox/multiple-nebular-checkbox.component';

describe('MultipleNebularCheckboxComponent', () => {
  let component: MultipleNebularCheckboxComponent;
  let fixture: ComponentFixture<MultipleNebularCheckboxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MultipleNebularCheckboxComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MultipleNebularCheckboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
