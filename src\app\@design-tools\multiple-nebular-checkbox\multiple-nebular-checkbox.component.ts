import { Provider, forwardRef, Component, OnInit, Input, Output, EventEmitter } from "@angular/core"
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from "@angular/forms"
import { NbComponentStatus } from "@nebular/theme"

export const CHECKBOX_VALUE_ACCESSOR: Provider[] = [
  {
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => MultipleNebularCheckboxComponent),
    multi: true,
  },
]

export interface CheckBoxEvent {
  currentCheck: unknown
  checked: boolean
  event: unknown
}

@Component({
  selector: 'eqp-multiple-nebular-checkbox',
  templateUrl: './multiple-nebular-checkbox.component.html',
  providers: [CHECKBOX_VALUE_ACCESSOR],
  styleUrls: ['./multiple-nebular-checkbox.component.scss'],
})
export class MultipleNebularCheckboxComponent implements OnInit, ControlValueAccessor {
  @Input() disabled = false
  @Input() status: NbComponentStatus = 'primary'
  @Input() indeterminate = false
  @Input() checkboxItens: { chave: any; valor: string }[] = []
  @Output() changeValue = new EventEmitter<any>()
  private opcoesSelecionadas: any[] = []
  public erroEstrutura = false;

  onChange: (value: unknown) => void = () => {}
  onTouched = () => {}

  ngOnInit(): void {
    this.erroEstrutura = !this.validateInterface();
  }

  validateInterface(): boolean {
    return this.checkboxItens.every(
      (item) => 'chave' in item && typeof item.valor === 'string'
    );
  }

  isChecked(itemChave: any): boolean {
    return (
      Array.isArray(this.opcoesSelecionadas) &&
      this.opcoesSelecionadas.includes(itemChave)
    )
  }

  writeValue(obj: unknown): void {
    if (Array.isArray(obj)) {
      this.opcoesSelecionadas = obj
    } else {
      this.opcoesSelecionadas = []
    }
  }

  registerOnChange(fn: (value: unknown) => void): void {
    this.onChange = fn
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled
  }

  checkboxChange(event: boolean, valor: any): void {
    const array = [...this.opcoesSelecionadas]
    if (event && !this.opcoesSelecionadas.includes(valor)) {
      array.push(valor)
    } else if (!event) {
      const index = array.indexOf(valor)
      array.splice(index, 1)
    }
    this.opcoesSelecionadas = array

    this.onChange(array)
    this.changeValue.emit(array)
  }

  onClick(event: Event): void {
    event.stopPropagation()
    this.onTouched()
  }
}
