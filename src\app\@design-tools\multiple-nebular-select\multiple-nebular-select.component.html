<div>
  <label style="display: block" class="label">
    {{ label }} {{ isRequired ? '*' : '' }}
  </label>

  <nb-select
    multiple
    [fullWidth]="fullWidth"
    [size]="'small'"
    [placeholder]="placeholder"
    [selected]="selectedOptions"
    (selectedChange)="onSelectionChange($event)"
    [disabled]="disabled"
  >
    <nb-select-label>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
        "
      >
        <span
          style="
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
          "
        >
          {{ selectedOptionsPreview }}
        </span>

        <dx-button
          icon="trash"
          stylingMode="text"
          type="danger"
          style="margin-left: 8px"
          (onClick)="clearSelection()"
          hint="Desmarcar seleção"
        ></dx-button>
      </div>
    </nb-select-label>

    <nb-option
      *ngIf="showToggleAll"
      value="TODOS"
      (click)="toggleSelectAll($event)"
    >
      Todos
    </nb-option>
    <nb-option *ngFor="let item of dataSource" [value]="item[value]">
      <ng-container *ngIf="isString(displayExpr)">
        {{ item[displayExpr] }}
      </ng-container>
      <ng-container *ngIf="isFunction(displayExpr)">
        {{ displayExpr(item) }}
      </ng-container>
    </nb-option>
  </nb-select>

  <div *ngIf="labelDisplayError" class="invalid-feedback d-block">
    <div>{{ label + ' é obrigatório' }}</div>
  </div>
</div>
