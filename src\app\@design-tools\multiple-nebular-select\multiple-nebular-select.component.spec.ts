import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MultipleNebularSelectComponent } from './multiple-nebular-select.component';

describe('MultipleNebularSelectComponent', () => {
  let component: MultipleNebularSelectComponent;
  let fixture: ComponentFixture<MultipleNebularSelectComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MultipleNebularSelectComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MultipleNebularSelectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
