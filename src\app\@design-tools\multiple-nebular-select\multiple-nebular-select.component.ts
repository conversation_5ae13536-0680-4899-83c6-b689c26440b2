import { Component, Host, Input, Optional, SkipSelf } from '@angular/core';
import { AbstractControl, ControlContainer, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';


@Component({
  selector: 'eqp-multiple-nebular-select',
  templateUrl: './multiple-nebular-select.component.html',
  styleUrls: ['./multiple-nebular-select.component.scss'],
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: MultipleNebularSelectComponent,
    multi: true
  }]
})
export class MultipleNebularSelectComponent implements ControlValueAccessor {
  @Input() public placeholder = 'Selecionar dados';
  @Input() public dataSource: unknown[] = [];
  @Input() public fullWidth = true;
  @Input() public label = '';
  @Input() public value = 'uuid';
  @Input() public displayExpr: string | Function  = 'nome';
  @Input() public selectedPreviewDisplay: string | Function  = 'nome';
  @Input() public formControlName?: string;
  @Input() public disabled = false;
  @Input() public required = false;
  @Input() public showToggleAll = true;

  public selectedOptions: unknown[] = [];
  public selectedOptionsPreview: unknown[] = [];
  
  private _isAllSelected = false;

  constructor(
    @Optional()
    @Host()
    @SkipSelf()
    private controlContainer: ControlContainer
  ) {}

  // Verifica se 'displayExpr' é uma string
  public isString(value: any): boolean {
    return typeof value === 'string';
  }

  // Verifica se 'displayExpr' é uma função
  public isFunction(value: any): boolean {
    return typeof value === 'function';
  }

  protected _onChange: (value: unknown[]) => void = () => {};
  protected _onTouched = () => {};

  public registerOnChange(fn: (value: unknown[]) => void): void {
    this._onChange = fn;
  }

  public registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }

  public setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  public writeValue(obj: unknown[]): void {
    const convertArray = Array.isArray(obj) ? obj : [obj];
    const dto = new Array(...convertArray);

    if (this._isAllMarked(dto) && obj && obj.length) {
      dto.push('TODOS');
    }

    this._setSelectedOptions(dto);
  }

  private _onValueChange(): void {
    const dto = [...this.selectedOptions]
    const index = dto.findIndex(value => value === 'TODOS');

    if(index !== -1) {
      dto.splice(index, 1);
    }

    this._onChange(dto);
  }

  private _getOptionsAll() {
    const dto = this.dataSource.map(res => res[this.value]);
    dto.push('TODOS');

    return dto;
  }

  public toggleSelectAll(event: Event) {

    event.preventDefault();
    event.stopPropagation();

    let dto = [];

    setTimeout(() => {
      if (!this._isAllSelected) {
        dto = this._getOptionsAll();
      }

      this._setSelectedOptions(dto);
      this._onValueChange();
    });
  }

  public onSelectionChange(selected: unknown[]) {
    const dto = [...selected];
    const index = dto.findIndex(value => value === 'TODOS');

    if (this._isAllMarked(dto) && index === -1) {
      dto.push('TODOS');
    } else if(index !== -1) {
      dto.splice(index, 1);
    }
    this._setSelectedOptions(dto);
    this._onValueChange();
  }

  private _isAllMarked(selected: unknown[]) {
    return (selected.length >= this.dataSource.length) && this.showToggleAll;
  }

  private _setSelectedOptions(data: unknown[]) {
    this.selectedOptions = data;

    this.selectedOptionsPreview = this.dataSource
    .filter(item => data.includes(item[this.value]))
    .map(res => this.isString(this.selectedPreviewDisplay) ? res[this.selectedPreviewDisplay as string] : (this.selectedPreviewDisplay as Function)(res));

    this._isAllSelected = this._isAllMarked(data);
  }

  private _getAbsControl(): AbstractControl {
    if (!this.controlContainer) 
      return null;
    return this.controlContainer.control.get(this.formControlName);
  }

  public get labelDisplayError(): boolean {

    if (!this._getAbsControl()) {
      return false;
    }

    return this._getAbsControl().errors?.required && 
      (this._getAbsControl()?.touched || !this._getAbsControl()?.pristine);
  }
  
  public get isRequired() {
    return this._getAbsControl().errors?.required || this.required;
  }

  public clearSelection() {
    this.selectedOptions = [];
    this.selectedOptionsPreview = [];
    this._onChange([]);
  }
}
