<div class="form-control-group">

  <label
    *ngIf="label"
    class="label"
    for="{{ name }}" 
    [attr.aria-label]="label"
  >
    {{ label }} {{ isRequired ? '*' : '' }}
  </label>

  <dx-date-box
    #dateBoxRef
    stylingMode="outlined"
    type="date"
    pickerType="calendar"
    [(ngModel)]="value"
    [readOnly]="isReadyOnly"
    [disabled]="disabled"
    [attr.id]="name"
    [placeholder]="placeholder"
    [showClearButton]="showClearButton"
    [useMaskBehavior]="useMaskBehavior"
    [displayFormat]="displayFormat"
    [inputAttr]="{ 'aria-label': 'Date' }"
    [showDropDownButton]="showDropDownButton"
  ></dx-date-box>

  <div
    *ngIf="labelDisplayError"
    class="invalid-feedback d-block"
  >
    <div>{{ label + ' é obrigatório' }}</div>
  </div>
</div>