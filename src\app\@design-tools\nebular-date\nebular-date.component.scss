@import 'src/assets/scss/_variables.scss';

.invalid-feedback {
  color: $nb-marker-danger !important;
}

:host ::ng-deep {
  .dx-dropdowneditor-input-wrapper {
    border-radius: var(--input-rectangle-border-radius) !important;
    color: var(--input-basic-text-color) !important;
    -webkit-appearance: none !important;
    appearance: none !important;
    box-shadow: none !important;
  }

  .dx-texteditor.dx-editor-outlined {
    background-color: var(--input-basic-background-color) !important;
    color: var(--input-basic-text-color) !important;
    box-shadow: inset 0 0 0 1px var(--input-basic-border-color) !important;
    border-radius: var(--input-rectangle-border-radius) !important;
  }

  .dx-texteditor.dx-editor-outlined.dx-state-focused {
    background-color: var(--input-basic-focus-background-color) !important;
    box-shadow: inset 0 0 0 1px var(--input-basic-focus-border-color) !important;
  }

  .dx-texteditor.dx-editor-outlined.dx-state-hover {
    box-shadow: none !important;
    background-color: var(--background-basic-color-3) !important;
  }

} 