import { Component, forwardRef, Input, ViewChild } from '@angular/core';
import { AbstractControl, ControlContainer, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { DxDateBoxComponent } from 'devextreme-angular';
import { Format } from 'devextreme/localization';
import moment from 'moment';

type DateDto = Date | string | null;

const VALUE_PROVIDER = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => NebularDateComponent),
  multi: true,
}

@Component({
    selector: 'eqp-nebular-date',
    templateUrl: './nebular-date.component.html',
    styleUrls: ['./nebular-date.component.scss'],
    providers: [VALUE_PROVIDER],
})
export class NebularDateComponent implements ControlValueAccessor {

  @ViewChild("dateBoxRef", { static: false }) dateBox: DxDateBoxComponent;

  @Input() public formControlName = '';
  @Input() public label = '';
  @Input() public name = '';
  @Input() public placeholder = '';
  @Input() public required = false;
  @Input() public disabled = false;
  @Input() public isReadyOnly = false;
  @Input() public formatDateOutput = "YYYY-MM-DD";

  @Input() public showClearButton = false;
  @Input() public useMaskBehavior = true;
  @Input() public displayFormat: Format | string = 'dd/MM/yyyy';
  @Input() public showDropDownButton = true;

  private _innerValue: DateDto = null;

  constructor(private _controlContainer: ControlContainer) { }

  ngAfterViewInit(): void {
    this.setIconCalendar();
  }

  get value() {
    return this._innerValue;
  }

  set value(v: DateDto) {
    if (v && moment(v).isValid()) {
      this._innerValue = v;
      this.onChange(moment(v).format(this.formatDateOutput));
    } else {
      this._innerValue = null;
      this.onChange('');
    }
  }

  onChange: (value: DateDto) => void = () => {};
  onTouched: () => void = () => {};

  public writeValue(value: DateDto): void {
    if(value && moment(value, this.formatDateOutput, true).isValid()){
      this.value = this.converteDateValue(value);
    } else if (!value) {
      this.value = null;
    }
  }

  public registerOnChange(fn: (value: DateDto) => void): void {
    this.onChange = fn;
  }

  public registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  public setDisabledState?(disabled: boolean): void {
    this.isReadyOnly = disabled;
    this.disabled = disabled;
  }

  private _getAbsControl(): AbstractControl {
    if (!this._controlContainer) 
      return null;
    return this._controlContainer.control.get(this.formControlName);
  }

  public get labelDisplayError(): boolean {

    if (!this._getAbsControl()) {
      return false;
    }

    return this._getAbsControl().errors?.required && 
      (this._getAbsControl()?.touched || !this._getAbsControl()?.pristine);
  }

  private converteDateValue(value: DateDto) {
    return value ? moment(value, this.formatDateOutput).toDate() : null;
  }

  private setIconCalendar() {
    const buttonContent = this.dateBox.instance.element().querySelector('.dx-button-content');

    if (buttonContent) {
      buttonContent.innerHTML = `
        <div class="dx-button-content d-flex align-items-center">
          <i class="fa fa-calendar"></i>
        </div>
      `;
    }
  }

  public get isRequired() {
    return this._getAbsControl().errors?.required || this.required;
  }

}
