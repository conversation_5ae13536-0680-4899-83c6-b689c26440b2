.small {
  max-width: 100vw;
  width: 30vw;
  max-height: 90vh;
}

.medium {
  max-width: 100vw;
  width: 50vw;
  max-height: 90vh;
}

.large {
  max-width: 100vw;
  width: 70vw;
  max-height: 90vh;
}

.extra-large {
  max-width: 100vw;
  width: 90vw;
  max-height: 90vh;
}

.full {
  max-width: 100vw;
  width: 100vw;
  height: 100vh;
  max-height: 100vh;
}

.back-button {
  text-transform: capitalize !important;
  border-radius: 0.25rem !important;
  color: #fff !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  background-color: #343a40 !important;
  border-color: #343a40 !important;
  font-weight: 400 !important;
}

.deny-button {
  text-transform: capitalize !important;
  border-radius: 0.25rem !important;
  color: #fff !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  font-weight: 400 !important;
}

.confirm-button {
  text-transform: capitalize !important;
  border-radius: 0.25rem !important;
  color: #fff !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  font-weight: 400 !important;
}

.confirm-button:disabled {
  opacity: 0.65;
}


