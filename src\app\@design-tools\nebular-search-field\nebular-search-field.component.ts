import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormBuilder,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'eqp-nebular-search-field',
  templateUrl: './nebular-search-field.component.html',
  styleUrls: ['./nebular-search-field.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: NebularSearchFieldComponent,
      multi: true,
    },
  ],
})
export class NebularSearchFieldComponent
  implements ControlValueAccessor, OnInit, OnDestroy
{
  @Input() label: string = '';
  @Input() codeLabel: string = 'Código';
  @Input() nameLabel: string = 'Nome';
  @Input() codeKey = 'codigo';
  @Input() nameKey = 'nome';
  @Input() idKey = 'uuid';
  @Input() hideName = false;
  @Input() hideButton = false;
  @Input() disabled = false;
  @Input() disabledCodeInput = false;
	@Input() required = false;
	@Input() displayError = false;
  @Input() maxlength: number;
  @Input() public primaryMask?: string = null
  @Input() private waitingTime: number = 300


  @Output() onButtonClick = new EventEmitter<boolean>();
  @Output() onInputChange = new EventEmitter<any>();

  model: FormGroup;
  codeSubscription: Subscription;
  changeSubscription: Subscription;

  onTouched = () => {};
  touched = false;

  constructor(private builder: FormBuilder) {}

  registerOnTouched(touched: any) {
    this.onTouched = touched;
  }

  ngOnInit(): void {
    this.model = this.builder.group({
      [this.idKey]: [undefined],
      [this.codeKey]: [undefined],
      [this.nameKey]: [undefined],
    });
    this.initializeHandlres();
  }

  ngOnDestroy(): void {
    this.codeSubscription && this.codeSubscription.unsubscribe();
    this.changeSubscription && this.changeSubscription.unsubscribe();
  }

  initializeHandlres() {
    this.codeSubscription = this.model
      .get(this.codeKey)
      .valueChanges.pipe(debounceTime(this.waitingTime), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.onInputChange.emit(value);
        } else {
          this.onInputChange.emit(null);
        }
        this.markAsTouched();
      });
  }

  writeValue(value: any) {
    if (value) {
      this.model.patchValue(value, { emitEvent: false });
    } else {
      this.model.reset(undefined, { emitEvent: false });
    }
    this.markAsTouched();
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  registerOnChange(fn: (value: any) => void) {
    this.changeSubscription = this.model.valueChanges.subscribe(fn);
  }
}
