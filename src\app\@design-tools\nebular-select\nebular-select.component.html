<div class="form-control-group">
  <label *ngIf="label" class="label" for="{{ name }}" [attr.aria-label]="label">
    {{ label }} {{ _required ? '*' : '' }}
  </label>
  <div class="select-container">
    <dx-select-box
      [(ngModel)]="currentValue"
      [name]="name"
      [dataSource]="dataSource"
      [disabled]="disabled"
      [searchMode]="searchMode"
      [searchEnabled]="true"
      [placeholder]="placeholder"
      [valueExpr]="getValueExpr()"
      [displayExpr]="getDisplayExpr()"
      (ngModelChange)="modelChanged($event)"
      [required]="_required"
      stylingMode="outlined"
      valueChangeEvent="keyup"
      [searchExpr]="searchExprOption"
      [validationStatus]="
        _required && (getAbsControl()?.touched || !getAbsControl()?.pristine)
          ? getAbsControl()?.invalid
            ? 'invalid'
            : 'valid'
          : 'valid'
      "
    >
    </dx-select-box>

    <button
      *ngIf="enableAddButton"
      type="button"
      nbButton
      status="primary"
      (click)="onAddButtonClick.emit($event)"
    >
      +
    </button>
  </div>

  <div
    *ngIf="
      getAbsControl()?.errors?.required &&
      (getAbsControl()?.touched || !getAbsControl()?.pristine)
    "
    class="invalid-feedback"
    [ngClass]="{ 'd-block': getAbsControl()?.errors }"
  >
    <div>{{ label + ' é obrigatório' }}</div>
  </div>
</div>
