import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormGroupDirective,
  Validators,
} from '@angular/forms';
import { LIST_OF_MONTHS } from '@common/helpers/month-list';
import { DefaultListDropDownInterface } from '@core/interfaces/default';
import { Subject } from 'rxjs';
import { startWith, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-period-field',
  templateUrl: './period-field.component.html',
})
export class PeriodFieldComponent implements OnInit, OnDestroy {
  @Input() disableInitialMonth = false;
  @Input() disableFinalMonth = false;
  @Input() initial = 1;
  @Input() final = 0;
  @Input() initialFormName = 'mesInicial';
  @Input() finalFormName = 'mesFinal';
  @Input() labelInitial = 'Período';
  @Input() labelFinal = 'a';

  remainingMonths: DefaultListDropDownInterface[] = [];
  periodList: DefaultListDropDownInterface[] = LIST_OF_MONTHS;

  unsubscriber$ = new Subject<void>();

  model!: FormGroup;

  constructor(private formGroupDirective: FormGroupDirective) {}

  
  get startMonth() {
    return this.model.get(this.initialFormName) as FormControl;
  }
  
  get endMonth() {
    return this.model.get(this.finalFormName) as FormControl;
  }

  ngOnDestroy(): void {
    this.unsubscriber$.next()
    this.unsubscriber$.complete()
  }

  ngOnInit(): void {
    this.model = this.formGroupDirective.control;
    this._insertControls();
  }

  private _insertControls() {
    this._insertControl(
      this.initialFormName,
      this.initial,
      this.disableInitialMonth,
    );
    this._insertControl(this.finalFormName, this.final, this.disableFinalMonth);
    this._observableInitialMonth();
  }

  private _insertControl(
    formName: string,
    initialValue: number,
    disabled: boolean,
  ) {
    if (formName && !this.model.get(formName)) {
      this.model.addControl(
        formName,
        new FormControl(
          {
            value: initialValue,
            disabled: disabled,
          },
          Validators.required,
        ),
      );
    } else if (disabled) {
      this.model.get(formName)?.disable();
    }
  }

  private _observableInitialMonth() {
    if (this.disableInitialMonth) {
      this.remainingMonths = this.periodList.filter(
        (e) => (e.key as number) >= this.initial,
      );
    } else {
      this.startMonth.valueChanges
        .pipe(takeUntil(this.unsubscriber$), startWith(this.startMonth.value))
        .subscribe((res) => {
          this.endMonth.reset();
          this.remainingMonths = this.periodList.filter(
            (e) => (e.key as number) >= res,
          );
        });
    }
  }
}
