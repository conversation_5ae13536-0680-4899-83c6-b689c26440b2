<nb-card [nbSpinner]="loading">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8 d-flex align-items-center" style="gap: 8px">
        <i
          [class]="'far fa-star cursor '"
          *ngIf="!favorito && mostrar"
          (click)="incluirFavorito()"
        ></i>
        <i
          class="fas fa-star cursor favorito"
          *ngIf="favorito && mostrar"
          (click)="desFavoritar()"
        ></i>
        <h5>{{ tituloPrincipal }}</h5>
        <span
          *ngIf="habilitarStatusLabel"
          [class]="'badge badge-' + tipoStatusLabel"
        >
          {{ statusLabelTexto }}
        </span>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <br />
    <ng-content></ng-content>
    <br />
    <div class="row mt-3">
      <div class="col-sm-12 col-lg-3" *ngIf="!ocultarCampoTipo">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo de arquivo *"
          placeholder=""
          formControlName="tipo"
          [dataSource]="tipoArquivoDados"
          [displayExpr]="'valor'"
          [disabled]="desabilitarCampoTipo"
          [valueExpr]="'chave'"
        >
        </eqp-nebular-select>
      </div>
      <div class="col col-12 col-md-2 mt-4 d-flex align-items-center">
        <eqp-nebular-checkbox
          label="Visualizar"
          formControlName="visualizar"
        ></eqp-nebular-checkbox>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="d-flex justify-content-between align-items-center">
      <button *ngIf="modal" (click)="botaoVoltarEmitter.emit()" class="btn btn-dark">
        Voltar
      </button>
    
      <div class="d-flex justify-content-end flex-grow-1">
        <button
          class="btn btn-primary mr-1"
          [disabled]="model.invalid"
          (click)="baixar()"
        >
          Baixar
        </button>
        <button
          class="btn btn-success"
          [disabled]="model.invalid"
          (click)="visualizar()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
