import { Component, EventEmitter, Host, Input, OnInit, Output } from '@angular/core';
import {
  ControlContainer,
  FormControl,
  FormGroup,
  FormGroupDirective,
  Validators,
} from '@angular/forms';
import { Params, Router } from '@angular/router';
import { CrudService } from '@common/services/crud.service';
import { MenuService } from '@pages/menu.service';
import { ReportDialogService } from '@pages/planning/shared/reports/services/report-dialog.service';
import { Subject } from 'rxjs';
import { first, take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-relatorio-template',
  templateUrl: './relatorio-template.component.html',
  styleUrls: ['./relatorio-template.component.scss'],
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective },
  ],
})
export class RelatorioTemplateComponent implements OnInit {
  @Input() tituloPrincipal: string =
    'Interface padrão para relatórios equiplano';

  @Input() urlTipoArquivo: string =
    'transparencia/pre_carregamento_relatorio/tipo_relatorio';

  @Input() public prepararDto?: (dto) => Params;

  @Input() public uri: string = '';

  @Input() public habilitarPost: boolean;
  
  @Input() public desabilitarCampoTipo: boolean

  @Input() public ocultarCampoTipo: boolean

  @Input() public modal: boolean

  //Status label
  @Input()
  habilitarStatusLabel: boolean = false;

  @Input()
  tipoStatusLabel: 'warning' | 'success' | 'danger' | 'info' = 'warning';

  @Input()
  statusLabelTexto: string = 'Label text';

  @Output() botaoVoltarEmitter: EventEmitter<Event> = new EventEmitter();

  public favorito: any;
  public loading = false
  public mostrar = false;
  private rota: any;
  private menu: any[];
  public tipoArquivoDados = [];
  public model: FormGroup;
  private unsub$ = new Subject<null>();

  constructor(
    private menuService: MenuService,
    private router: Router,
    @Host() private formGroupDirective: FormGroupDirective,
    private crudService: CrudService,
    private reportDialogService: ReportDialogService,
  ) {}

  ngOnInit(): void {
    this.obterLoadingRelatorio();
    this.obterFavorito();
    this.gerarFormulario();
    this.carregarComboTipoArquivos();
  }

  private obterLoadingRelatorio() {
    this.reportDialogService
      .getLoading()
      .pipe(takeUntil(this.unsub$))
      .subscribe((res) => {
        this.loading = res;
      });
  }

  private gerarFormulario() {
    this.model = this.formGroupDirective.form;
    this.model.addControl('tipo', new FormControl('PDF', [Validators.required]));
    this.model.addControl('visualizar', new FormControl(true));
  }

  private carregarComboTipoArquivos() {
    this.crudService
      .getSingleData<any>(this.urlTipoArquivo)
      .pipe(take(1))
      .subscribe((res) => {
        this.tipoArquivoDados = res.dados;
      });
  }

  private obterFavorito() {
    this.rota = this.router.routerState.snapshot.url;
    this.menuService
      .get()
      .pipe(first())
      .subscribe((data) => {
        this.menu = data.dados;
        this.mostrar = this.acharMenu(this.menu, null) != null;
        const favorites: any[] = JSON.parse(localStorage.getItem('favorites'));
        let favorite: any;
        if (favorites) {
          favorite = favorites.find((i) => {
            return i.link === this.rota;
          });
          if (favorite) {
            this.favorito = favorite;
          }
        }
      });
  }
  public incluirFavorito(): void {
    const favorites: any[] = JSON.parse(localStorage.getItem('favorites'));

    let favorite: any;
    if (favorites) {
      favorite = favorites.find((i) => {
        return i.link === this.rota;
      });
      if (!favorite) {
        const item = this.acharMenu(this.menu, null);
        this.favorito = item;
        favorites.push(item);
        localStorage.setItem('favorites', JSON.stringify(favorites));
      }
    } else {
      const temp: any[] = [];
      const item = this.acharMenu(this.menu, null);
      this.favorito = item;
      temp.push(item);
      localStorage.setItem('favorites', JSON.stringify(temp));
    }
  }

  public desFavoritar(): void {
    this.favorito = null;
    const favorites: any[] = JSON.parse(localStorage.getItem('favorites'));
    if (favorites) {
      const item = favorites.find((favorite) => {
        return favorite.link === this.rota;
      });

      const index = favorites.findIndex((i) => {
        return i.link === this.rota;
      });

      if (item) {
        favorites.splice(index, 1);
        localStorage.setItem('favorites', JSON.stringify(favorites));
      }
    }
  }

  private acharMenu(menu: any[], fav: any): any {
    let favorito = fav;
    if (menu) {
      menu.forEach((item) => {
        if (!favorito) {
          if (item.link === this.rota) {
            favorito = item;
          } else {
            favorito = this.acharMenu(item.children, favorito);
          }
        }
      });
    }
    return favorito;
  }

  
  private manipularDto() {
    const dto = this.prepararDto
      ? this.prepararDto(this.model.getRawValue())
      : this.model.getRawValue();
    return {
      ...dto,
    };
  }

  public baixar() {
    const dto = this.manipularDto()
    this.reportDialogService.baixar(dto, this.tituloPrincipal, this.uri, this.habilitarPost)
  }

  public visualizar() {
    const dto = this.manipularDto()
    this.reportDialogService.getReportBase64(dto, this.tituloPrincipal, this.uri, this.habilitarPost)
  }
}
