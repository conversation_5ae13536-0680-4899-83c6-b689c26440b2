<label class="label">{{
  required && displayRequired ? label + ' *' : label
}}</label>
<div class="d-flex w-100" [formGroup]="model">
  <nb-form-field
    [ngClass]="{ 'w-100': hideName }"
    [ngStyle]="{ width: codeInputWidth }"
  >
    <input
      nbInput
      [fullWidth]="true"
      [fieldSize]="'small'"
      formControlName="codigo"
      [placeholder]="codeLabel"
      [type]="inputType"
      [mask]="primaryMask"
      [readonly]="disabled || disabledCodeInput"
      [tabIndex]="tabIndex"
      inputNumberDirective
      [validarInputNumerico]="onlyNumberKey"
      [maxlength]="maxlength"
      inputNumberDirective
      [validarInputNumerico]="onlyNumberKey"
    />
    <button
      [size]="'small'"
      [title]="'Buscar'"
      nbSuffix
      nbButton
      ghost
      [disabled]="disabled"
      [hidden]="disabled || hideButton"
      (click)="onButtonClick()"
    >
      <nb-icon icon="search" pack="eva"></nb-icon>
    </button>
  </nb-form-field>
  <input
    *ngIf="!hideName"
    class="ml-1"
    [type]="'text'"
    [fullWidth]="true"
    [fieldSize]="'small'"
    [formControlName]="nameKey"
    [placeholder]="nameLabel"
    [value]="nameValue"
    nbInput
    readonly
  />
</div>
<div
  *ngIf="displayError"
  class="invalid-feedback"
  [ngClass]="{
    'd-block': true
  }"
>
  <div>{{ label + ' é obrigatório' }}</div>
</div>

