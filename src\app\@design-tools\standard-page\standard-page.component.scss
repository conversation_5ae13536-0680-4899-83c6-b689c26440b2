.custom-button {
  text-transform: capitalize !important;
  border-radius: 0.25rem !important;
  color: #fff !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  background-color: #3366ff !important;
  border-color: #3366ff !important;
  font-weight: 400 !important;
}

.custom-button:disabled {
  opacity: 0.65;
}

.confirm-button {
  text-transform: capitalize !important;
  border-radius: 0.25rem !important;
  color: #fff !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  font-weight: 400 !important;
}

.back-button {
  text-transform: capitalize !important;
  border-radius: 0.25rem !important;
  color: #fff !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  background-color: #343a40 !important;
  border-color: #343a40 !important;
  font-weight: 400 !important;
}

.deny-button {
  text-transform: capitalize !important;
  border-radius: 0.25rem !important;
  color: #fff !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  font-weight: 400 !important;
}

.confirm-button:disabled {
  opacity: 0.65;
}
