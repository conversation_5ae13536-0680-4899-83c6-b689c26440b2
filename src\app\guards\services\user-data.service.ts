import { Injectable } from '@angular/core'
import { SessaoService } from '@common/services/sessao.service'

import { environment } from '@environments/environment'
import { UserDataInterface } from '@guards/services/user-data'
import { CrossStorageClient } from 'cross-storage'
import { first } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class UserDataService {
  public storage: CrossStorageClient = new CrossStorageClient(
    `${environment.backoOfficeUrl}`,
  )
  public iframe = this.storage
  public promisse = this.storage.onConnect()
  private _userData: UserDataInterface
  private _theme: string
  private _themeDev: string

  public get userData(): UserDataInterface {
    return this._userData
  }

  public set userData(value: UserDataInterface) {
    this._userData = value
  }

  public get theme(): string {
    return this._theme
  }

  public set theme(value: string) {
    this._theme = value
  }

  public get themeDev(): string {
    return this._themeDev
  }

  public set themeDev(value: string) {
    this._themeDev = value
  }

  constructor(private service: SessaoService) {}

  public getUserData(): any {
    return JSON.parse(localStorage.getItem('userData'))
  }

  public async recuperarToken(idToken: string): Promise<void> {
    this.service
      .recuperaSessao(idToken)
      .pipe(first())
      .subscribe(sessaoSalva => {
        localStorage.removeItem('userData')

        localStorage.setItem('userData', JSON.stringify(sessaoSalva.dados))
      })
  }

   public crossClientGet(key: string): any {
    console.log('crossClientGet.key', key);
    const tokenFake =
      '{"idToken":"7e069f2e-cdd5-4a15-8557-be4c28f24c23","entidadeUuid":"3615bb1b-9f72-4329-ba4d-a261b2022b73","municipioClienteUuid":"48abd028-34f5-4d28-8d93-861f6e78e3e4","exercicioUuid":"0efc58b5-f95c-411b-9371-f89b5cbdbe77","exercicio":2025,"clienteUuid":"64eb94a7-63d3-4fb7-afa7-bec44ca87519","cpf":"admin","email":"<EMAIL>","entidade":{"uuid":"3615bb1b-9f72-4329-ba4d-a261b2022b73","clienteUuid":"64eb94a7-63d3-4fb7-afa7-bec44ca87519","codigo":80,"nome":"Município de Pinhal de São Bento","uf":"PR"},"nome":"admin","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SiZpuM9J1KszWPSNjgOY2TW6icf6EtblpWsU1psK1T0","tokenJwt":"eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FJ63ZtlZGXI567TqhC3169I5frtRWUQjkZRE63UE2hNaiVuxsPZ1v-FKhYq3BZx7knJ5CgkYXSCIjCVlke6ryA","uuid":"664f8da6-f13f-4ba6-8bbe-ede27f852b97","exercise":{"status":{"id":4,"uuid":"e3574fe1-177e-4e0a-913a-b061a2dc29c5","codigo":4,"nome":"Execução"},"texto":"2025","valor":"0efc58b5-f95c-411b-9371-f89b5cbdbe77"}}';
    return (
      this.promisse
        // .then(() => this.iframe.get(key))
        .then(() =>
          key == 'userData'
            ? tokenFake
            : key == 'theme'
            ? 'dark'
            : 'material.nebular.dark',
        )
        .then((data: string) => {
          if (key === 'userData') {
            const userData = JSON.parse(data);
            this.userData = userData;
          } else if (key === 'theme') {
            this.theme = data;
          } else if (key === 'devExtremeTheme') {
            this.themeDev = data;
          }
          localStorage.setItem(key, data);
          if (
            key !== 'userData' ||
            (this.userData &&
              this.userData !== undefined &&
              this.userData !== null)
          ) {
            return true;
          } else {
            const url = window.location.href;
            let prefix: string = '/#/login';
            // window.open(
            //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
            //     url,
            //   )}`,
            //   '_self',
            // );
          }
        })
        .catch(() => {
          if (key === 'userData') {
            const url = window.location.href;
            let prefix: string = '/#/login';
            // window.open(
            //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
            //     url,
            //   )}`,
            //   '_self',
            // );
          }
        })
    );
  }

  public crossClientSet(key: string, value: any): any {
    return this.promisse
      .then(() =>
        this.iframe.set(
          key,
          typeof value === 'string' ? value : JSON.stringify(value),
        ),
      )
      .then((data: string) => {
        if (key === 'userData') {
          const userData = JSON.parse(data)
          this.userData = userData
        } else if (key === 'theme') {
          this.theme = data
        } else if (key === 'devExtremeTheme') {
          this.themeDev = data
        }

        localStorage.setItem(key, data)

        if (data && data !== undefined && data !== null) {
          return true
        } else return false
      })
      .catch(() => {
        // do something
      })
  }

  public crossClientDel(key: string): any {
    return this.promisse
      .then(() => this.iframe.del(key))
      .then((data: string) => {
        localStorage.removeItem(key)
        if (key === 'userData') {
          console.log('sessao token delete')
          const url = window.location.href
          let prefix: string = '/#/login'
          // window.open(
          //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(url)}`,
          //   '_self',
          // )
        }
      })
      .catch(() => {})
  }
}
