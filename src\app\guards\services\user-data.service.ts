import { Injectable } from '@angular/core'
import { SessaoService } from '@common/services/sessao.service'

import { environment } from '@environments/environment'
import { UserDataInterface } from '@guards/services/user-data'
import { CrossStorageClient } from 'cross-storage'
import { first } from 'rxjs/operators'

@Injectable({
  providedIn: 'root',
})
export class UserDataService {
  public storage: CrossStorageClient = new CrossStorageClient(
    `${environment.backoOfficeUrl}`,
  )
  public iframe = this.storage
  public promisse = this.storage.onConnect()
  private _userData: UserDataInterface
  private _theme: string
  private _themeDev: string

  public get userData(): UserDataInterface {
    return this._userData
  }

  public set userData(value: UserDataInterface) {
    this._userData = value
  }

  public get theme(): string {
    return this._theme
  }

  public set theme(value: string) {
    this._theme = value
  }

  public get themeDev(): string {
    return this._themeDev
  }

  public set themeDev(value: string) {
    this._themeDev = value
  }

  constructor(private service: SessaoService) {}

  public getUserData(): any {
    return JSON.parse(localStorage.getItem('userData'))
  }

  public async recuperarToken(idToken: string): Promise<void> {
    this.service
      .recuperaSessao(idToken)
      .pipe(first())
      .subscribe(sessaoSalva => {
        localStorage.removeItem('userData')

        localStorage.setItem('userData', JSON.stringify(sessaoSalva.dados))
      })
  }

  public crossClientGet(key: string): any {
    console.log('crossClientGet.key', key);
    const tokenFake =
      '{"idToken":"6ef1a805-08a4-41dc-8465-30d23d448f49","entidadeUuid":"efd6cb0f-543e-401a-8565-c874a07b72a9","municipioClienteUuid":"a4d08b4d-e98f-4f8f-bc0f-773f6f7b052d","exercicioUuid":"c3cb64fc-be85-4b55-9402-d552653cbc1c","exercicio":2025,"clienteUuid":"b2ad475e-226b-4545-9b78-951a35c70d64","cpf":"admin","email":"<EMAIL>","entidade":{"uuid":"efd6cb0f-543e-401a-8565-c874a07b72a9","clienteUuid":"b2ad475e-226b-4545-9b78-951a35c70d64","codigo":82,"nome":"Município de Diamante do Sul","uf":"PR"},"nome":"admin","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.czRt-uehQoqK3ciiRPukcBJn0lg9ewVkqYsunSCMzlY","tokenJwt":"eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BZoZt8l0ya1j_Ff20dRwRYuCMrDOnhmWVFuZv3LJBhmb2B-pRk8chtGrHkRtYSXMNet1lBWW20_qAUWtBrr5nA","uuid":"664f8da6-f13f-4ba6-8bbe-ede27f852b97","exercise":{"status":{"id":4,"uuid":"325f8036-11f7-423b-a0a6-045ab7c32353","codigo":4,"nome":"Execução"},"texto":"2025","valor":"c3cb64fc-be85-4b55-9402-d552653cbc1c"}}';
    return (
      this.promisse
        // .then(() => this.iframe.get(key))
        .then(() =>
          key == 'userData'
            ? tokenFake
            : key == 'theme'
            ? 'dark'
            : 'material.nebular.dark',
        )
        .then((data: string) => {
          if (key === 'userData') {
            const userData = JSON.parse(data);
            this.userData = userData;
          } else if (key === 'theme') {
            this.theme = data;
          } else if (key === 'devExtremeTheme') {
            this.themeDev = data;
          }
          localStorage.setItem(key, data);
          if (
            key !== 'userData' ||
            (this.userData &&
              this.userData !== undefined &&
              this.userData !== null)
          ) {
            return true;
          } else {
            const url = window.location.href;
            let prefix: string = '/#/login';
            // window.open(
            //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
            //     url,
            //   )}`,
            //   '_self',
            // );
          }
        })
        .catch(() => {
          if (key === 'userData') {
            const url = window.location.href;
            let prefix: string = '/#/login';
            // window.open(
            //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(
            //     url,
            //   )}`,
            //   '_self',
            // );
          }
        })
    );
  }

  public crossClientSet(key: string, value: any): any {
    return this.promisse
      .then(() =>
        this.iframe.set(
          key,
          typeof value === 'string' ? value : JSON.stringify(value),
        ),
      )
      .then((data: string) => {
        if (key === 'userData') {
          const userData = JSON.parse(data)
          this.userData = userData
        } else if (key === 'theme') {
          this.theme = data
        } else if (key === 'devExtremeTheme') {
          this.themeDev = data
        }

        localStorage.setItem(key, data)

        if (data && data !== undefined && data !== null) {
          return true
        } else return false
      })
      .catch(() => {
        // do something
      })
  }

  public crossClientDel(key: string): any {
    return this.promisse
      .then(() => this.iframe.del(key))
      .then((data: string) => {
        localStorage.removeItem(key)
        if (key === 'userData') {
          console.log('sessao token delete')
          const url = window.location.href
          let prefix: string = '/#/login'
          // window.open(
          //   `${environment.backoOfficeUrl}${prefix}?wayBack=${encodeURI(url)}`,
          //   '_self',
          // )
        }
      })
      .catch(() => {})
  }
}
