<eqp-standard-page [mainTitle]="currentMenu?.title">
  <p class="mb-5">{{ currentMenu?.description }}</p>

  <nb-accordion multi>
    <nb-accordion-item
      *ngFor="let menu of currentMenu?.children"
      [expanded]="openMenu.includes(menu.code)"
      (collapsedChange)="toogleAccordion($event, menu)"
    >
      <nb-accordion-item-header>{{ menu.title }}</nb-accordion-item-header>
      <nb-accordion-item-body>
        <div 
          *ngFor="let children of menu?.children"
          class="mb-2"
        >
          <a 
            [routerLink]="children.link"
            [queryParams]="{ action: action}"
          >
            {{ children.title }}
          </a>
        </div>
      </nb-accordion-item-body>
    </nb-accordion-item>
  </nb-accordion>
</eqp-standard-page>