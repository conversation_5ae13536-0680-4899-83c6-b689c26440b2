import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { MenuInterface } from '@core/interfaces/menu';
import { ActionDTO, MenuStorageService } from '@core/services/reports/menu-storage.service';
import { Subject } from 'rxjs';
import { filter, map, take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-menu-reports',
  templateUrl: './menu-reports.component.html',
  styleUrls: ['./menu-reports.component.scss'],
})
export class MenuReportsComponent implements OnInit, OnDestroy {

  private action: ActionDTO = this.route.snapshot.params['action'];

  private subject$ = new Subject();
  
  public currentMenu?: MenuInterface;
  protected openMenu: string[] = [];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private menuStorageService: MenuStorageService,
  ) {}

  ngOnInit(): void {
    this.buildMenu();
    this.queryParamsObservable();
    this.routerNavigationEnd();
  }

  ngOnDestroy(): void {
    this.subject$.next();
    this.subject$.complete();
  }

  private routerNavigationEnd() {
    this.router.events
      .pipe(takeUntil(this.subject$))
      .subscribe((event) => {
        if (event instanceof NavigationEnd) {
          this.action = this.route.snapshot.params['action'];
          this.buildMenu();
        }
      });
  }

  private queryParamsObservable() {
    this.route.queryParams
    .pipe(
      map(el => el['open'] || []),
      takeUntil(this.subject$),
    )
    .subscribe(res => {
      this.openMenu = res;
    });
  }

  private buildMenu(): void {
    this.menuStorageService.get()
      .pipe(
        filter((res) => res[this.action].singleMenu.length > 0),
        take(1),
      )
      .subscribe(res => {
        this.currentMenu = res[this.action].singleMenu[0];
      })
  }

  protected toogleAccordion(event: boolean, menu: MenuInterface): void {
    let newArray: Array<string> = [];
    if(!event){
      newArray = this.addMenuQueryParams(menu.code);
    } else {
      newArray = this.removeMenuQueryParams(menu.code);
    }
    this.updateRouteWithMenuCode(newArray);
  }

  private addMenuQueryParams(codeMenu: string): Array<string> {
    const newArray = [...this.openMenu, codeMenu];
    return newArray;
  }

  private removeMenuQueryParams(codeMenu: string): Array<string> {
    const newArray = [...this.openMenu];
    const indexMenu = newArray.findIndex(el => el === codeMenu);
    if( indexMenu !== -1 ) {
      newArray.splice(indexMenu, 1);
    }
    return newArray;
  }

  private updateRouteWithMenuCode(code: Array<string>): void {
    this.router.navigate([], {
      queryParams: {
        open: code,
      },
    })
  }
}
