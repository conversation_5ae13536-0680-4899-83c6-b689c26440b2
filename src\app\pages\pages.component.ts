import {
  <PERSON><PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON><PERSON>t,
  ViewChild,
} from '@angular/core'
import { SwUpdate } from '@angular/service-worker'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import {
  NbDialogService,
  NbMenuService,
  NbPopoverDirective,
} from '@nebular/theme'
import { Subject } from 'rxjs'
import { first } from 'rxjs/operators'

import { MenuService } from './menu.service'
import { PreLoadService } from './pre-load.service'

@Component({
  selector: 'eqp-pages',
  styleUrls: ['pages.component.scss'],
  template: `
    <eqp-one-column-layout>
      <nb-menu [items]="[]" [autoCollapse]="true" tag="menu-sidebar"></nb-menu>
      <router-outlet></router-outlet>
    </eqp-one-column-layout>
  `,
})
export class PagesComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {
  menu = []

  private readonly destroy$ = new Subject<void>()

  public actualMenuData: any
  public actualIsFavorite: boolean = false

  @ViewChild(NbPopoverDirective) popover: NbPopoverDirective

  constructor(
    private nbMenuService: NbMenuService,
    private preLoadService: PreLoadService,
    private swUpdate: SwUpdate,
    private appRef: ApplicationRef,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
    private menuService: MenuService,
  ) {
    this.updateClient()
  }

  public ngOnInit(): void {
    this.buildMenu()
  }

  public ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  private updateClient(): void {
    if (!this.swUpdate.isEnabled) {
      console.log('Not Enabled')
      return
    }
    this.swUpdate.available.subscribe(event => {
      const content = {
        body: 'Deseja instalar a atualização?',
        confirmText: 'Instalar',
        confirmIcon: 'fas fa-sync-alt text-white',
        confirmType: 'success',
        cancelText: 'Adiar',
      }

      const dialogRef = this.dialogService.open(ConfirmationComponent, {
        closeOnEsc: false,
        closeOnBackdropClick: false,
      })

      dialogRef.componentRef.instance.dialogTitle = `Existe uma atualização para o sistema`
      dialogRef.componentRef.instance.confirmationContent = content

      dialogRef.onClose.subscribe((data: boolean) => {
        if (data) {
          this.toastr.send({
            success: true,
            message:
              'A atualização foi instalada com sucesso e será aplicada dentro dos próximos 3 segundos.',
          })
          setTimeout(() => {
            this.swUpdate.activateUpdate().then(() => location.reload())
          }, 3000)
        } else {
          this.toastr.send({
            warning: true,
            message: 'A atualização foi adiada.',
          })
          return
        }
      })
      console.log(`current`, event.current, `available `, event.available)
    })

    this.swUpdate.activated.subscribe(event => {
      console.log(`current`, event.previous, `available `, event.current)
    })
  }

  private buildMenu(): void {
    if (this.menu.length === 0) {
      this.menuService
        .get()
        .pipe(first())
        .subscribe(data => {
          this.nbMenuService.addItems(data.dados, 'menu-sidebar')
          this.menu = data.dados
        })
    }
  }
}
