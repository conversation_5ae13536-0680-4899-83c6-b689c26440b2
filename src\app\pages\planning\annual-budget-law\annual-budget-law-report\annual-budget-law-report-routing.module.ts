import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { BudgetedExpenseReportComponent } from './modules/budgeted-expense-report/budgeted-expense-report.component'
import { BudgetedExpenseReportModule } from './modules/budgeted-expense-report/budgeted-expense-report.module'
import { BudgetedRevenueReportComponent } from './modules/budgeted-revenue-report/budgeted-revenue-report.component'
import { BudgetedRevenueReportModule } from './modules/budgeted-revenue-report/budgeted-revenue-report.module'
import { EvolutionRevenueReportComponent } from './modules/evolution-revenue-report/evolution-revenue-report.component'
import { EvolutionExpenseReportComponent } from './modules/evolution-expense-report/evolution-expense-report.component'
import { FinancialProgrammingReportComponent } from './modules/financial-programming-report/financial-programming-report.component'
import { ParallelRevenueExpenseReportComponent } from './modules/parallel-revenue-expense-report/parallel-revenue-expense-report.component'
import { DisbursementScheduleReportComponent } from './modules/disbursement-schedule-report/disbursement-schedule-report.component';

const routes: Routes = [
  {
    path: 'orcamento-anual-despesa-orcada',
    component: BudgetedExpenseReportComponent,
  },
  {
    path: 'orcamento-anual-receita-orcamentaria-prevista',
    component: BudgetedRevenueReportComponent,
  },
  {
    path: 'evolucao-da-receita',
    component: EvolutionRevenueReportComponent,
  },
  {
    path: 'evolucao-da-despesa',
    component: EvolutionExpenseReportComponent,
  },
  {
    path: 'programacao-financeira-receita',
    component: FinancialProgrammingReportComponent,
  },
  {
    path: 'paralelo-receita-despesa-fonte',
    component: ParallelRevenueExpenseReportComponent,
  },
  {
    path: 'cronograma-desembolso',
    component: DisbursementScheduleReportComponent
  }
]

@NgModule({
  imports: [
  RouterModule.forChild(routes),
    BudgetedExpenseReportModule,
    BudgetedRevenueReportModule,
  ],
  exports: [RouterModule],
})
export class AnnualBudgetLawReportRoutingModule {}
