import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'

import { AnnualBudgetLawReportRoutingModule } from './annual-budget-law-report-routing.module'
import { AnnualBudgetLawReportComponent } from './annual-budget-law-report.component'
import { EvolutionRevenueReportComponent } from './modules/evolution-revenue-report/evolution-revenue-report.component'
import { CommonToolsModule } from '@common/common-tools.module';
import { EvolutionExpenseReportComponent } from './modules/evolution-expense-report/evolution-expense-report.component';
import { FinancialProgrammingReportComponent } from './modules/financial-programming-report/financial-programming-report.component';
import { ParallelRevenueExpenseReportComponent } from './modules/parallel-revenue-expense-report/parallel-revenue-expense-report.component';
import { DisbursementScheduleReportComponent } from './modules/disbursement-schedule-report/disbursement-schedule-report.component'

@NgModule({
  declarations: [
    AnnualBudgetLawReportComponent,
    EvolutionRevenueReportComponent,
    EvolutionExpenseReportComponent,
    FinancialProgrammingReportComponent,
    ParallelRevenueExpenseReportComponent,
    DisbursementScheduleReportComponent,
  ],
  imports: [
    CommonModule,
    CommonToolsModule,
    AnnualBudgetLawReportRoutingModule,
  ],
})
export class AnnualBudgetLawReportModule {}
