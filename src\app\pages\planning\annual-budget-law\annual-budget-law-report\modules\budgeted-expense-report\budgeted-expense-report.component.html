<eqp-standard-page [mainTitle]="pageTitle" [rightApproveButtonVisible]="true" [rightApproveButtonIconVisible]="true"
  [rightApproveButtonIcon]="'fas fa-save'" [rightApproveButtonId]="'submit-budgeted-expense-report'"
  [rightApproveButtonDisabled]="!model?.valid || model?.pristine" (rightApproveButtonEmitter)="submit()">
  <div class="container" [formGroup]="model">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <div class="row">
      <div class="col col-12 col-md-2">
        <eqp-nebular-select formControlName="tipoRelatorio" [dataSource]="tipoRelatorioData" label="Tipo de Relatório"
          valueExpr="chave" displayExpr="valor"></eqp-nebular-select>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-select formControlName="tipoResumoUuid" [dataSource]="tipoResumoData" label="Tipo de Resumo"
          valueExpr="chave" displayExpr="valor" [disabled]="disableFilterBy"></eqp-nebular-select>
      </div>
    </div>
    <div *ngIf="false" class="row mt-2">
      <div class="col col-12 col-md-6">
        <label class="label">Entidade</label>
        <ng-container *ngIf="isMultiple; else isSingle">
          <nb-select #entitySelect [fullWidth]="true" [size]="'small'" formControlName="entidade" placeholder="Entidades" [multiple]="true">
            <nb-option [value]="entity.uuid" *ngFor="let entity of entityData">{{ entity.nome }}</nb-option>
          </nb-select>
        </ng-container>
        <ng-template #isSingle>
          <nb-select #entitySelect [fullWidth]="true" [size]="'small'" formControlName="entidade" placeholder="Entidades" [multiple]="false">
            <nb-option [value]="entity.uuid" *ngFor="let entity of entityData">{{ entity.nome }}</nb-option>
          </nb-select>
        </ng-template>
      </div>
    </div>
    <div *ngIf="false" class="mt-2">
      <eqp-fieldset label="Filtros">
        <div class="content">
          <div class="row">
            <div class="col col-12 col-md-2">
              <eqp-nebular-select formControlName="filtrarPor" label="Filtrar por:" [dataSource]="filtrarPorData"
                valueExpr="chave" displayExpr="valor" [disabled]="disableFieldsByReportType"></eqp-nebular-select>
            </div>
            <div class="col col-12 col-md-2">
              <eqp-nebular-search-field formControlName="contaDespesa" label="Conta de despesa" [hideName]="true"
                (onButtonClick)="onExpensePlanDialog()"
                (onInputChange)="onSearchInput($event, 'contaDespesa', 'plano_despesa')"
                [disabled]="!filterByExpenseAccount"></eqp-nebular-search-field>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Orgão</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="orgao" multiple
                [disabled]="filterByExpenseAccount" placeholder="Orgão">
                <nb-option [value]="organ.uuid" *ngFor="let organ of organData" value="1">{{organ.codigo}} - {{
                  organ.nome }}</nb-option>
              </nb-select>
            </div>
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Unidade</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="unidade" multiple
                [disabled]="filterByExpenseAccount" placeholder="Unidade">
                <nb-option [value]="unity.uuid" *ngFor="let unity of unityData" value="1">{{unity.codigo}} - {{
                  unity.nome }}</nb-option>
              </nb-select>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Função</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="funcao" multiple
                [disabled]="filterByExpenseAccount" placeholder="Função">
                <nb-option [value]="function.uuid" *ngFor="let function of functionData" value="1">{{function.codigo}} -
                  {{ function.nome }}</nb-option>
              </nb-select>
            </div>
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Subfunção</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="subfuncao" multiple
                [disabled]="filterByExpenseAccount" placeholder="Subfunção">
                <nb-option [value]="subFunction.uuid" *ngFor="let subFunction of subFunctionData"
                  value="1">{{subFunction.codigo}} - {{ subFunction.nome }}</nb-option>
              </nb-select>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Programa</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="programa" multiple
                [disabled]="filterByExpenseAccount" placeholder="Programa">
                <nb-option [value]="program.uuid" *ngFor="let program of programData" value="1">{{program.codigo}} - {{
                  program.nome }}</nb-option>
              </nb-select>
            </div>
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Projeto/Atividade</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="projetoAtividade" multiple
                [disabled]="filterByExpenseAccount" placeholder="Projeto/Atividade">
                <nb-option [value]="projectActivity.uuid" *ngFor="let projectActivity of projectActivityData"
                  value="1">{{ projectActivity.tipoOrdemNome }}</nb-option>
              </nb-select>
            </div>
          </div>
          <div class="row mt-2 mb-4">
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Fonte de recurso</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="fonteRecurso" multiple
                [disabled]="filterByExpenseAccount" placeholder="Fonte de recurso">
                <nb-option [value]="resourceSource.uuid" *ngFor="let resourceSource of resourceSourceData" value="1">{{
                  resourceSource.codigoEhNome }}</nb-option>
              </nb-select>
            </div>
            <div class="col col-12 col-md-6">
              <label style="display: block" for="" class="label">Natureza Despesa</label>
              <nb-select [fullWidth]="true" [size]="'small'" formControlName="naturezaDespesa" multiple
                [disabled]="filterByExpenseAccount" placeholder="Natureza Despesa">
                <nb-option [value]="expenseNature.uuid" *ngFor="let expenseNature of expenseNatureData"
                  value="1">{{expenseNature.codigo}} - {{ expenseNature.nome }}</nb-option>
              </nb-select>
            </div>
          </div>
        </div>
      </eqp-fieldset>
    </div>
    <div class="row mt-2">
      <div class="col col-12 col-md-2">
        <eqp-nebular-select formControlName="type" [dataSource]="tipoArquivoData" label="Tipo de arquivo*"
          [displayExpr]="'valor'" [valueExpr]="'chave'"></eqp-nebular-select>
      </div>
    </div>
  </div>
</eqp-standard-page>
