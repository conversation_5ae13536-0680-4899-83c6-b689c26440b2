import {
  After<PERSON>iewInit,
  ChangeDetectionStrategy,
  ChangeDetector<PERSON><PERSON>,
  <PERSON>mponent,
  <PERSON>ement<PERSON><PERSON>,
  HostL<PERSON>ener,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { EntitySearchComponent } from '@common/dialogs/entity-search/entity-search.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService, NbSelectComponent } from '@nebular/theme'
import { EntityInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-projection-ppa-ldo'
import { ReportPreviewComponent } from '@pages/planning/shared/reports/components/report-preview/report-preview.component'
import { ReportService } from '@pages/planning/shared/reports/services/report.service'
import { ExpensePlanSearchComponent } from '@pages/planning/shared/searchs/expense-plan-search/expense-plan-search.component'
import { FunctionSearchComponent } from '@pages/planning/shared/searchs/function-search/function-search.component'
import { OrganSearchComponent } from '@pages/planning/shared/searchs/organ-search/organ-search.component'
import { ProgramSearchComponent } from '@pages/planning/shared/searchs/program-search/program-search.component'
import { ProjectActivitySearchComponent } from '@pages/planning/shared/searchs/project-activity-search/project-activity-search.component'
import { ResourceSourceSearchComponent } from '@pages/planning/shared/searchs/resource-source-search/resource-source-search.component'
import { UnitySearchComponent } from '@pages/planning/shared/searchs/unity-search/unity-search.component'
import { DataSourceHelperService } from '@pages/planning/shared/services/data-source-helper.service'
import DataSource from 'devextreme/data/data_source'
import { Subject, Subscription } from 'rxjs'
import { filter, first, take, takeUntil, tap } from 'rxjs/operators'
import { FunctionInterface } from '@pages/planning/shared/interfaces/function'
import { SubFunctionInterface } from '@pages/planning/shared/interfaces/sub-function'
import { ProgramInterface } from '@pages/planning/multi-year-plan/interfaces/program'
import { ProjectActivityInterface } from '@pages/planning/shared/interfaces/project-activity'
import { ExpensePlanInterface } from '@pages/planning/multi-year-plan/interfaces/expense-plan'
import { ResourceSourceCombinationInterface } from '@pages/planning/resource-source/interfaces/resource-source-combination'

@Component({
  selector: 'eqp-budgeted-expense-report',
  templateUrl: './budgeted-expense-report.component.html',
  styleUrls: ['./budgeted-expense-report.component.scss'],
})
export class BudgetedExpenseReportComponent implements OnDestroy, OnInit {
  pageTitle = 'Relatório - Despesa Orçada'
  loading = false

  model: FormGroup
  uri: string = 'orcamento_anual_despesa_orcada'

  @ViewChild('entitySelect') entitySelect

  tipoRelatorioData: DataSource
  tipoResumoData: DataSource
  filtrarPorData: DataSource
  tipoArquivoData: DataSource
  organData: any[]
  unityData: any[]
  functionData: FunctionInterface[]
  subFunctionData: SubFunctionInterface[]
  programData: ProgramInterface[]
  projectActivityData: ProjectActivityInterface[]
  resourceSourceData: ResourceSourceCombinationInterface[]
  expenseNatureData: ExpensePlanInterface[]
  entityData = []

  disableFieldsByReportType: boolean = true
  disableFilterBy: boolean = false
  filterByExpenseAccount: boolean = false
  isMultiple: boolean

  reportTypeFilterSubcription: Subscription
  filterBySubcription: Subscription

  get tipo() {
    return this.model.get('type').value
  }

  private unsub$ = new Subject<null>()

  constructor(
    private toastr: ToastrService,
    private _builder: FormBuilder,
    private crudService: CrudService,
    private reportService: ReportService,
    private dialogService: NbDialogService,
    public sourceHelper: DataSourceHelperService,
    private userDataService: UserDataService,
  ) {}

  ngOnDestroy(): void {
    this.reportTypeFilterSubcription.unsubscribe()
    this.filterBySubcription.unsubscribe()
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelectData()
    this.loadHandlers()
    this.model.markAsDirty()
  }

  getNewModel(): FormGroup {
    return this._builder.group({
      tipoRelatorio: ['RESUMED'],
      tipoResumoUuid: ['ORGAN'],
      entidade: [],
      filtrarPor: [],
      contaDespesa: [],
      orgao: [],
      unidade: [],
      funcao: [],
      subfuncao: [],
      programa: [],
      projetoAtividade: [],
      fonteRecurso: [],
      naturezaDespesa: [],
      type: ['PDF', [Validators.required]], //arquivo
    })
  }

  loadHandlers() {
    this.reportTypeFilterSubcription = this.model
      .get('tipoRelatorio')
      .valueChanges.pipe(takeUntil(this.unsub$))
      .subscribe(res => {
        this.tipoRelatorioData
          .store()
          .byKey(res)
          .then((item: { chave: string; valor: string }) => {
            if (item.chave === 'RESUMED') {
              this.isSelectMultiple()
              this.resetMultipleSelects()
            } else {
              this.isSelectSingle()
            }
          })
      })

    this.filterBySubcription = this.model
      .get('filtrarPor')
      .valueChanges.pipe(takeUntil(this.unsub$))
      .subscribe(res => {
        this.filtrarPorData
          .store()
          .byKey(res)
          .then(item => {
            if (item.chave === 'EXPENSE_ACCOUNT') {
              this.filterByExpenseAccount = true
              this.resetMultipleSelects()
            } else {
              this.filterByExpenseAccount = false
              this.model.get('contaDespesa').reset()
            }
          })
      })
  }

  resetMultipleSelects() {
    const fieldsToReset = [
      'orgao',
      'unidade',
      'funcao',
      'subfuncao',
      'programa',
      'projetoAtividade',
      'fonteRecurso',
      'naturezaDespesa',
    ]

    fieldsToReset.forEach(item => {
      this.model.get(item).patchValue([null])
    })
    this.model.get('contaDespesa').reset()
  }

  isSelectMultiple() {
    this.disableFieldsByReportType = true
    this.disableFilterBy = false
    this.isMultiple = true
    this.entitySelect.reset()
    this.model.get('filtrarPor').reset()
    this.loadLoggedEntity(true)
  }

  isSelectSingle() {
    this.disableFieldsByReportType = false
    this.disableFilterBy = true
    this.isMultiple = false
    this.model.get('tipoResumoUuid').reset()
    this.entitySelect.reset()
    this.loadLoggedEntity(false)
  }

  loadLoggedEntity(isMultiple: boolean) {
    const entityUuid = this.userDataService.userData.entidadeUuid
    if (isMultiple && this.model.get('entidade').pristine) {
      this.model.get('entidade').patchValue([entityUuid])
    } else if (!isMultiple && this.model.get('entidade').pristine) {
      this.model.get('entidade').patchValue(entityUuid)
    }
  }

  async loadSelectData() {
    this.tipoRelatorioData = new DataSource({
      store: {
        data: [
          {
            chave: 'RESUMED',
            valor: 'Resumido',
          },
        ],
        type: 'array',
        key: 'chave',
      },
    })
    this.tipoResumoData = await this.sourceHelper.getDataSourceFromList(
      `${this.uri}/tipo_resumo`,
    )
    this.tipoResumoData.filter(res => res.chave != 'RESOURCE_SOURCE')
    this.filtrarPorData = await this.sourceHelper.getDataSourceFromList(
      `${this.uri}/filtrar_por`,
      'chave',
    )

    this.tipoArquivoData = await this.sourceHelper.getDataSourceFromList(
      `transparencia/pre_carregamento_relatorio/tipo_relatorio`,
    )
  }

  loadFiltersData() {
    const entityUuid = this.userDataService.userData.entidadeUuid

    this.crudService
      .getSingleData<EntityInterface[]>('entidade/paginado?take=0')
      .pipe(take(1))
      .subscribe(res => {
        this.entityData = res.data
      })
    this.model.get('entidade').patchValue(entityUuid)
    this.crudService
      .getSingleData<any[]>('orgao/paginado?take=0')
      .pipe(take(1))
      .subscribe(res => {
        this.organData = res.data
      })

    this.crudService
      .getSingleData<any[]>('unidade/paginado?take=0')
      .pipe(take(1))
      .subscribe(res => {
        this.unityData = res.data
      })

    this.crudService
      .getSingleData<FunctionInterface[]>('funcao/paginado?take=0')
      .pipe(take(1))
      .subscribe(res => {
        this.functionData = res.data
      })

    this.crudService
      .getSingleData<SubFunctionInterface[]>('subfuncao/paginado?take=0')
      .pipe(take(1))
      .subscribe(res => {
        this.subFunctionData = res.data
      })

    this.crudService
      .getSingleData<ProgramInterface[]>('ppa_programa/paginado?take=0')
      .pipe(take(1))
      .subscribe(res => {
        this.programData = res.data
      })

    this.crudService
      .getSingleData<ProjectActivityInterface[]>(
        'projeto_atividade/paginado?take=0',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.projectActivityData = res.data
      })

    this.crudService
      .getSingleData<ResourceSourceCombinationInterface[]>(
        'fonte_recurso_combinacao/paginado?take=0',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.resourceSourceData = res.data
      })

    this.crudService
      .getSingleData<ExpensePlanInterface[]>('plano_despesa/paginado?take=0')
      .pipe(take(1))
      .subscribe(res => {
        this.expenseNatureData = res.data
      })
  }

  customDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  onSearchInput(value: any, field: string, uri?: string) {
    if (!uri) uri = field
    if (!value || value == '') {
      this.model.get(field).reset()
      return
    }
    this.loading = true
    this.crudService
      .getDataSourceFiltroComposto(
        'uuid',
        `${uri}/paginado`,
        10,
        `["codigo","=",${value}]`,
      )
      .load()
      .then(
        res => {
          this.loading = false
          if (res.length == 0) {
            this.toastr.send({
              error: true,
              message: 'Registro não encontrado.',
            })
            this.model.get(field).reset()
          } else this.model.get(field).patchValue(res[0])
        },
        err => {
          this.loading = false
          this.toastr.send({
            error: true,
            message: `${err}`,
          })
        },
      )
  }

  public onEntityDialog() {
    const ref = this.dialogService.open(EntitySearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('entidade').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onExpensePlanDialog() {
    const ref = this.dialogService.open(ExpensePlanSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('contaDespesa').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onOrganDialog() {
    const ref = this.dialogService.open(OrganSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('orgao').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onUnityDialog() {
    const ref = this.dialogService.open(UnitySearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('unidade').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onFunctionDialog() {
    const ref = this.dialogService.open(FunctionSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('funcao').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onSubfunctionDialog() {
    const ref = this.dialogService.open(FunctionSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.componentRef.instance.uri = 'subfuncao/paginado'
    ref.componentRef.instance.pageTitle = 'Selecionar subfunção'
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('subfuncao').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onProgramDialog() {
    const ref = this.dialogService.open(ProgramSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('programa').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onProjectActivityDialog() {
    const ref = this.dialogService.open(ProjectActivitySearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('projetoAtividade').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  public onResourceSourceDialog() {
    const ref = this.dialogService.open(ResourceSourceSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.model.get('fonteRecurso').patchValue(res)
          this.model.markAsDirty()
        }
      })
  }

  private prepare(formData: any) {
    const dto = {
      tipoRelatorio: formData.tipoRelatorio,
      tipoResumoUuid: formData.tipoResumoUuid,
      type: formData.type
    }

    return dto
  }

  public submit() {
    const dto = this.prepare(this.model.getRawValue())

    if (this.tipo == 'PDF') {
      this.dialogService.open(ReportPreviewComponent, {
        context: {
          downloadName: this.pageTitle,
          rInfo: {
            dto: dto,
            url: this.uri,
          },
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
    } else {
      this.downloadReport(dto)
    }
  }

  private downloadReport(data: any) {
    this.loading = true
    this.reportService
      .generateReport(data, this.uri)
      .pipe(takeUntil(this.unsub$))
      .subscribe(
        res => {
          this.reportService.donwloadReport(res.dados, this.pageTitle)
        },
        err => (this.loading = false),
        () => (this.loading = false),
      )
  }
}
