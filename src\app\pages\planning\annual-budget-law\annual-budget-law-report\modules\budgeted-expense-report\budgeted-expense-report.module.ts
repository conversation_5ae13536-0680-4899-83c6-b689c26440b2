import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { ReportPreviewModule } from '@pages/planning/shared/reports/components/report-preview/report-preview.module'
import { ExpensePlanSearchModule } from '@pages/planning/shared/searchs/expense-plan-search/expense-plan-search.module'
import { FunctionSearchModule } from '@pages/planning/shared/searchs/function-search/function-search.module'
import { OrganSearchModule } from '@pages/planning/shared/searchs/organ-search/organ-search.module'
import { ProgramSearchModule } from '@pages/planning/shared/searchs/program-search/program-search.module'
import { ProjectActivitySearchModule } from '@pages/planning/shared/searchs/project-activity-search/project-activity-search.module'
import { UnitySearchModule } from '@pages/planning/shared/searchs/unity-search/unity-search.module'
import { BudgetedExpenseReportComponent } from './budgeted-expense-report.component'

@NgModule({
  declarations: [BudgetedExpenseReportComponent],
  imports: [
    CommonModule,
    CommonToolsModule,
    ExpensePlanSearchModule,
    OrganSearchModule,
    UnitySearchModule,
    FunctionSearchModule,
    ProgramSearchModule,
    ProjectActivitySearchModule,
    ReportPreviewModule
  ],
  exports: [BudgetedExpenseReportComponent],
})
export class BudgetedExpenseReportModule {}
