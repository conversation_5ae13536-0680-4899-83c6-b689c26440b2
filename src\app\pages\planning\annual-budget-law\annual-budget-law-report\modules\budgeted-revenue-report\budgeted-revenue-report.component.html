<eqp-standard-page
  [mainTitle]="pageTitle"
  [rightApproveButtonVisible]="true"
  [rightApproveButtonDisabled]="!model?.valid || model?.pristine"
  (rightApproveButtonEmitter)="submit()"
>
 <div class="container" [formGroup]="model">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <div class="row">
      <div class="col">
        <label style="display: block" for="" class="label">Entidade</label>
        <nb-select
          [fullWidth]="true"
          [size]="'small'"
          formControlName="entidadeUuid"
          multiple
          placeholder="Entidade"
        >
          <nb-option
            [value]="entity.uuid"
            *ngFor="let entity of entityData"
            value="1"
            >{{ entity.nome }}</nb-option
          >
        </nb-select>
      </div>
    </div>
    <div class="mt-2">
      <eqp-fieldset label="Filtros">
        <div class="content">
          <div class="row">
            <div class="col">
              <eqp-search-field
                formControlName="contaReceitaUuid"
                label="Conta de receita"
                primaryMask="0.0.0.0.00.0.0.***********.00"
                [uri]="uri + '/plano_receita'"
                searchColumnsType="revenuePlanColumns"
                dialogTitle="Plano receita"
                messageNotFound="Conta de receita não encontrada."
                waitingTime="2000"
              ></eqp-search-field>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col">
              <eqp-search-field
                formControlName="fonteRecursoUuid"
                label="Fonte recurso"
                [uri]="uri + '/fonte_recurso'"
                searchColumnsType="codeNameColumns"
                dialogTitle="Fonte de recurso"
                messageNotFound="Fonte de recurso não encontrada."
                waitingTime="2000"
              ></eqp-search-field>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col">
              <div class="d-flex mt-3">
                <eqp-field-toggle
                  formControlName="totalizarPorFonte"
                  title="Totalizar por fonte de recurso"
                  (ngModelChange)="flagChange($event, 'totalizarPorContaFonte')"
                ></eqp-field-toggle>
                <label class="label mt-2"> Totalizar por fonte de recurso </label>
              </div>
              <div class="d-flex">
                <eqp-field-toggle
                  formControlName="totalizarPorContaFonte"
                  title="Totalizar por Conta/Fonte"
                  (ngModelChange)="flagChange($event, 'totalizarPorFonte')"
                ></eqp-field-toggle>
              <label class="label mt-2"> Totalizar por Conta/Fonte </label>
              </div>
            </div>
          </div>
        </div>
      </eqp-fieldset>
    </div>
    <div class="row mt-2">
      <div class="col col-12 col-md-2">
        <eqp-nebular-select
          formControlName="tipo"
          [dataSource]="typeData"
          label="Tipo de arquivo*"
          [displayExpr]="'valor'"
          [valueExpr]="'chave'"
        ></eqp-nebular-select>
      </div>
    </div>
  </div>
</eqp-standard-page>
