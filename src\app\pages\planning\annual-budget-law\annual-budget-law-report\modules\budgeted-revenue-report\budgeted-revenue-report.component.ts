import { Component, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { EntityInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-projection-ppa-ldo'
import { ReportPreviewComponent } from '@pages/planning/shared/reports/components/report-preview/report-preview.component'
import { ReportInterface } from '@pages/planning/shared/reports/interfaces/report'
import { ReportService } from '@pages/planning/shared/reports/services/report.service'
import DataSource from 'devextreme/data/data_source'
import { Subject } from 'rxjs'
import { finalize, take, takeUntil } from 'rxjs/operators'

@Component({
  selector: 'eqp-budgeted-revenue-report',
  templateUrl: './budgeted-revenue-report.component.html',
  styleUrls: ['./budgeted-revenue-report.component.scss'],
})
export class BudgetedRevenueReportComponent implements OnInit {
  pageTitle: string = 'Receita orçamentária prevista'
  loading: boolean = false
  model: FormGroup

  uri: string = 'relatorio_receita_orcamentaria'
  entityData: EntityInterface[]
  typeData: DataSource

  private unsub$ = new Subject<null>()

  get tipo() {
    return this.model.get('tipo').value
  }

  get totalizarPf() {
    return this.model.get('totalizarPorFonte').value
  }

  constructor(
    private _builder: FormBuilder,
    private toastr: ToastrService,
    private crudService: CrudService,
    private reportService: ReportService,
    private dialogService: NbDialogService,
    private userDataService: UserDataService
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelects()
  }

  private getNewModel(): FormGroup {
    return this._builder.group({
      entidadeUuid: [undefined, Validators.required],
      tipo: ['PDF', Validators.required],
      contaReceitaUuid: [],
      fonteRecursoUuid: [],
      totalizarPorFonte: [],
      totalizarPorContaFonte: [],
    })
  }

  private loadSelects() {
    const {entidadeUuid} = this.userDataService.userData
    this.model.get('entidadeUuid').patchValue([entidadeUuid])
    this.model.markAsDirty()

    this.loading = true
    this.crudService.getSingleData<EntityInterface[]>('relatorio_receita_orcamentaria/entidade?take=0').pipe(take(1), finalize(() => this.loading = false)).subscribe((res) => {
      this.entityData = res.data
    })

    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.typeData = new DataSource({ store: res.dados })
      })
  }

  private prepare(formData: any) {
    const dto = {
      ...formData,
    }

    return dto
  }

  public submit() {
    const dto = this.prepare(this.model.getRawValue())

    if (this.tipo == 'PDF') {
      this.dialogService.open(ReportPreviewComponent, {
        context: {
          downloadName: 'Receita Orçamentária - Prevista',
          rInfo: {
            dto: dto,
            url: this.uri,
          },
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
    } else {
      this.downloadReport(dto)
    }
  }

  private downloadReport(data: any) {
    this.loading = true
    this.reportService
      .generateReport(data, this.uri)
      .pipe(takeUntil(this.unsub$), finalize(() => this.loading = false))
      .subscribe(
        res => {
          const dto: ReportInterface = {
            ...res.dados,
            tipo: {
              extension: this.tipo
            }
          }
          this.reportService.donwloadReport(dto, this.pageTitle)
        },
        _ => (this.loading = false),
        () => (this.loading = false),
      )
  }

  public flagChange(value: string, field: string) {
    if (value == 'S') this.model.get(field).setValue('N')
  }
}
