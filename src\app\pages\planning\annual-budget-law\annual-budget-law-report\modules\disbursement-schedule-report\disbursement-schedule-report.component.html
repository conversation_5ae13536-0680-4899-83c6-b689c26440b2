<eqp-standard-page
[mainTitle]="pageTitle"
[rightApproveButtonVisible]="true"
[rightApproveButtonIconVisible]="true"
[rightApproveButtonIcon]="'fas fa-save'"
[rightApproveButtonId]="'submit-accounting-trial-balance-report'"
[rightApproveButtonDisabled]="model.invalid"
(rightApproveButtonEmitter)="submit()"
>
   <div class="container">
      <ng-container [formGroup]="model">
         <div class="mb-3">
            <label style="display: block;" for="" class="label">Entidades</label>
            <nb-select [fullWidth]="true" [size]="'small'" formControlName="entidadeUuids" multiple placeholder="Entidades">
               <nb-option [value]="entity.uuid" *ngFor="let entity of entityData" value="1">{{entity.nome}}</nb-option>
            </nb-select>
         </div>
         <div class="row mb-3">
            <div class="col-12 col-md-4">
               <eqp-fieldset label="Cronograma de desembolso">
                  <div class="row">
                     <div class="col-6">
                        <eqp-nebular-toggle label="Por fonte de recurso" formControlName="porFonteRecurso"></eqp-nebular-toggle>
                     </div>
                     <div class="col-6">
                        <eqp-nebular-toggle label="Por natureza de despesa" formControlName="porNaturezaDespesa"></eqp-nebular-toggle>
                     </div>
                  </div>
               </eqp-fieldset>
            </div>
            <div class="col-12 col-md-8">
               <eqp-fieldset label="Filtros">
                  <div class="row">
                     <div class="col-6">
                        <eqp-nebular-search-field
                           label="Fonte de recurso"
                           formControlName="fonteRecursoUuid"
                           [nameKey]="'nome'"
                           (onInputChange)="onInputChange($event, 'fonteRecursoUuid')"
                           (onButtonClick)="onButtonClick('fonteRecursoUuid')"
                           [disabled]="porNaturezaDespesa"
                          ></eqp-nebular-search-field>
                     </div>
                     <div class="col-6">
                        <eqp-nebular-search-field
                           label="Natureza de despesa"
                           formControlName="naturezaDespesaUuid"
                           [nameKey]="'nome'"
                           (onInputChange)="onInputChange($event, 'naturezaDespesaUuid')"
                           (onButtonClick)="onButtonClick('naturezaDespesaUuid')"
                           [disabled]="!porNaturezaDespesa"
                        ></eqp-nebular-search-field>
                     </div>
                  </div>
               </eqp-fieldset>
            </div>
         </div>
         <div class="row">
            <div class="col-3">
               <eqp-nebular-select
                  [size]="'small'"
                  [shape]="'rectangle'"
                  label="Tipo de arquivo*"
                  placeholder=""
                  formControlName="tipo"
                  [dataSource]="typeData"
                  [displayExpr]="'valor'"
                  [valueExpr]="'chave'"
                ></eqp-nebular-select>
            </div>
         </div>
      </ng-container>
   </div>
</eqp-standard-page>
