import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CrudService } from '@common/services/crud.service';
import { UserDataService } from '@guards/services/user-data.service';
import DataSource from 'devextreme/data/data_source';
import { take, takeUntil, map } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ResourceSourceSearchComponent } from '@pages/planning/shared/searchs/resource-source-search/resource-source-search.component';
import { NbDialogService, NbSelectComponent } from '@nebular/theme';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { Subject } from 'rxjs';
import { ReportService } from '@pages/planning/shared/reports/services/report.service';
import { ReportPreviewComponent } from '@pages/planning/shared/reports/components/report-preview/report-preview.component';
import { ExpensePlanSearchComponent } from '@pages/planning/shared/searchs/expense-plan-search/expense-plan-search.component';
import { EntityInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-projection-ppa-ldo';

type SearchComponentType = ExpensePlanSearchComponent | ResourceSourceSearchComponent

@Component({
  selector: 'eqp-disbursement-schedule-report',
  templateUrl: './disbursement-schedule-report.component.html',
  styleUrls: ['./disbursement-schedule-report.component.scss']
})
export class DisbursementScheduleReportComponent implements OnInit {
  private destroy$: Subject<void> = new Subject<void>()

  entityData: any[] = []
  typeData: DataSource

  model: FormGroup

  loading: boolean = false
  disableExpenseNatureSearch: boolean = true

  expensePlanSearch: any = ExpensePlanSearchComponent
  resourceSourceSearch: any = ResourceSourceSearchComponent

  pageTitle: string = "Relatório - Cronograma de desembolso"
  uri: string = 'cronograma_desembolso_conta_despesa'

  @ViewChild(NbSelectComponent) select: NbSelectComponent;

  get tipo() {
    return this.model.get('tipo').value
  }

  get porNaturezaDespesa(){
    return this.model.get("porNaturezaDespesa").value
  }

  constructor(
    private _crudService: CrudService,
    private _userDataService: UserDataService,
    private _builder: FormBuilder,
    private _dialogService: NbDialogService,
    private _toastr: ToastrService,
    private _service: ReportService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelect()
    this.loadHandlers()
  }

  ngAfterViewInit(){
    this.loadCurrentEntity()
  }

  private getNewModel(): FormGroup{
    return this._builder.group({
      tipo: ['PDF', Validators.required],
      entidadeUuids: [],
      porFonteRecurso: [false],
      porNaturezaDespesa: [true],
      fonteRecursoUuid: [],
      naturezaDespesaUuid: []
   })
  }

  loadSelect(){
    this._crudService.getSingleData<EntityInterface[]>('cronograma_desembolso_conta_despesa/entidade')
    .pipe(take(1)).subscribe((res) => { this.entityData = res.data})
    this._crudService.getSingleData<any>('cronograma_desembolso_conta_despesa/pre_carregamento_relatorio')
    .pipe(take(1))
    .subscribe(res => {
      this.typeData = new DataSource({ store: res.dados })
    })
  }

  public getDataSource(url: string, key = 'uuid', size = 10) {
    return new DataSource({
      store: this._crudService.getDataSourceFiltro(key, url, size),
      pageSize: 10,
      paginate: false,
    })
  }

  loadCurrentEntity(){
    const currentEntityUuid = this._userDataService.userData.entidadeUuid
    const selectedEntitys = [currentEntityUuid];
    this.model.get('entidadeUuids').patchValue(selectedEntitys)
  }

  async onInputChange(event: any, controlName: string){
    const uri = this.getReferenceData(controlName)
     if (!event || event == '') {
      this.model.get(`${controlName}`).reset()
      return
    }
    this.loading = true
    await this._crudService
      .getDataSourceFiltro('uuid', `${uri}`, 10, 'codigo', `${event}`)
      .load()
      .then(
        res => {
          this.loading = false
          if (res.length == 0) {
            this._toastr.send({
              error: true,
              title: 'Erro',
              message: 'Não encontrado(a).',
            })
            this.model.get(`${controlName}`).reset()
          } else {
            this.model.get(controlName).patchValue(res[0])
          }
        },
        err => {
          this.loading = false
          this._toastr.bulkSend({
            error: true,
            message: `${err}`,
          })
        },
      )
  }

  onButtonClick(controlName){
    const component: TemplateRef<SearchComponentType> = this.selectSearchComponent(controlName)
    const dialogRef = this._dialogService.open(component, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.componentRef.instance.uri = this.getReferenceData(controlName)
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.model.get(controlName).patchValue(res)
      }
    })
  }

  private getReferenceData(controlName: string): string{
    let uri = "cronograma_desembolso_conta_despesa/natureza_despesa"
    if(controlName == 'fonteRecursoUuid') uri = 'cronograma_desembolso_conta_despesa/fonte_recurso'
    return uri
  }

  private selectSearchComponent(controlName: string){
    let component: TemplateRef<SearchComponentType>  = this.expensePlanSearch
    if(controlName == 'fonteRecursoUuid') component = this.resourceSourceSearch
    return component
  }

  loadHandlers(){
   this.model.get('porNaturezaDespesa').valueChanges
   .pipe(takeUntil(this.destroy$)).subscribe(res => {
      if(!res) this.model.get('naturezaDespesaUuid').reset()
      else this.model.get('fonteRecursoUuid').reset()
      this.model.get('porFonteRecurso').patchValue(!res, {emitEvent: false})
   })
   this.model.get('porFonteRecurso').valueChanges
   .pipe(takeUntil(this.destroy$)).subscribe(res => {
      if(!res) this.model.get('fonteRecursoUuid').reset()
      else this.model.get('naturezaDespesaUuid').reset()
      this.model.get('porNaturezaDespesa').patchValue(!res, {emitEvent: false})
   })
  }

  prepare(data: any){
    const dto = {
      ...data,
      naturezaDespesaUuid: data.naturezaDespesaUuid?.uuid,
      fonteRecursoUuid: data.fonteRecursoUuid?.uuid,
      porNaturezaDespesa: data.porNaturezaDespesa ? "S" : "N",
      porFonteRecurso: data.porFonteRecurso ? "S" : "N"
    }
    return dto
  }

  submit(){
    const dto = this.prepare(this.model.getRawValue())

    if(this.tipo == 'PDF') {
      this.dialogService.open(ReportPreviewComponent, {
        context: {
          downloadName: this.pageTitle,
          rInfo: {
            dto: dto,
            url: this.uri
          }
        },
        closeOnBackdropClick: false,
        closeOnEsc: false
      })
    } else {
      this.downloadReport(dto)
    }
  }

  private downloadReport(data: any) {
    this.loading = true
    this._service
      .generateReport(data, this.uri)
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        this._service.donwloadReport(res.dados, this.pageTitle)
      }, _ => this.loading = false,
      () => this.loading = false)
  }
}
