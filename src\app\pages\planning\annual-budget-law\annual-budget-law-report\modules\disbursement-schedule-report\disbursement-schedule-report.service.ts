import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DisbursementScheduleReportService {

  constructor(protected http: HttpClient) { }

  public get(filters?: any): Observable<any> {
    const headers = new HttpHeaders()
    let params = new HttpParams()

    if (filters) {
      Object.keys(filters).forEach(p => (params = params.append(p, filters[p])))
    }
    return this.http.get<any>('cronograma_desembolso_conta_despesa/relatorio', {
      headers,
      params,
    })
  }
}
