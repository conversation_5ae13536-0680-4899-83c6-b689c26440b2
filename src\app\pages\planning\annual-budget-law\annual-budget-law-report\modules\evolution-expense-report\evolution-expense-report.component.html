<eqp-standard-page
  [mainTitle]="pageTitle"
  [rightApproveButtonVisible]="true"
  [rightApproveButtonIconVisible]="true"
  [rightApproveButtonIcon]="'fas fa-save'"
  [rightApproveButtonId]="'submit-accounting-trial-balance-report'"
  [rightApproveButtonDisabled]="!form?.valid || form?.pristine"
  (rightApproveButtonEmitter)="submit(form)"
>
  <ng-container [formGroup]="form">
    <div class="row">
      <div class="col-sm-6 col-lg-9">
        <label style="display: block" for="" class="label">Entidade</label>
        <nb-select
          [fullWidth]="true"
          [size]="'small'"
          formControlName="entidadeUuid"
          multiple
          placeholder="Entidades"
        >
          <nb-option
            [value]="entity.uuid"
            *ngFor="let entity of entityData"
            value="1"
            >{{ entity.nome }}</nb-option
          >
        </nb-select>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-sm-12 col-lg-3">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          [disabled]="true"
          label="Tipo de arquivo*"
          placeholder=""
          [dataSource]="typeData"
          [displayExpr]="'valor'"
          [valueExpr]="'chave'"
          formControlName="tipo"
        >
        </eqp-nebular-select>
      </div>
    </div>
  </ng-container>
</eqp-standard-page>
