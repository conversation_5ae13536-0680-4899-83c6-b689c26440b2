import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ViewReportDialogComponent } from '@common/dialogs/view-report-dialog/view-report-dialog.component'
import { CrudService } from '@common/services/crud.service'
import { NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { finalize, take } from 'rxjs/operators'
import { EvolutionExpenseReportService } from './evolution-expense-report.service'

@Component({
  selector: 'eqp-evolution-expense-report',
  templateUrl: './evolution-expense-report.component.html',
  styleUrls: ['./evolution-expense-report.component.scss'],
})
export class EvolutionExpenseReportComponent implements OnInit, OnDestroy {
  pageTitle: string = 'Evolução da Despesa'
  loading: boolean = false
  form: FormGroup

  typeData: DataSource
  entityData: any[] = []
  uri: string = 'evolucao_despesa/relatorio'

  public subscription: Subscription

  get tipo() {
    return this.form.get('tipo').value
  }

  constructor(
    private builder: FormBuilder,
    private crudService: CrudService,
    private service: EvolutionExpenseReportService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    this.form = this.getNewModel()
    this.loadSelects()
  }

  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      tipo: [[], Validators.required],
      entidadeUuid: [],
    })
  }

  private loadSelects() {
    this.getData(`entidade/paginado`).then(res => (this.entityData = res))

    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.typeData = new DataSource({ store: res.dados })
        this.form.get('tipo').patchValue(res.dados[0].valor)
      })
  }

  private getData(uri: string) {
    return this.crudService.getDataSourceFiltro('uuid', uri, 0).load()
  }

  public submit({ value, valid }: { value: any; valid: boolean }) {
    this.subscription = this.service
      .getPdf(value.entidadeUuid, value.tipo)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res: any) => {
        if (this.tipo == 'PDF') {
          this.dialogService.open(ViewReportDialogComponent, {
            context: {
              downloadName: this.pageTitle,
              data: res.dados.documento,
            },
            closeOnBackdropClick: false,
            closeOnEsc: false,
          })
        }
      })
  }

  public getDataSource(url: string, key = 'uuid', size = 10) {
    return new DataSource({
      store: this.crudService.getDataSourceFiltro(key, url, size),
      pageSize: 10,
      paginate: false,
    })
  }
}
