import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ViewReportDialogComponent } from '@common/dialogs/view-report-dialog/view-report-dialog.component'
import { CrudService } from '@common/services/crud.service'
import { NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { distinctUntilChanged, finalize, first, take } from 'rxjs/operators'
import { EvolutionRevenueReportService } from './evolution-revenue.service'
import { MenuService } from '@pages/menu.service'

@Component({
  selector: 'eqp-evolution-revenue-report',
  templateUrl: './evolution-revenue-report.component.html',
  styleUrls: ['./evolution-revenue-report.component.scss'],
})
export class EvolutionRevenueReportComponent implements OnInit, OnD<PERSON>roy {
  pageTitle: string = 'Evolução da Receita'
  loading: boolean = false
  form: FormGroup

  typeData: DataSource
  entityData: any[] = []
  uri: string = 'evolucao_receita/relatorio'
  public entities: any[] = []
  public entity: any

  public subscription: Subscription

  get tipo() {
    return this.form.get('tipo').value
  }

  constructor(
    private builder: FormBuilder,
    private crudService: CrudService,
    private service: EvolutionRevenueReportService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    this.getEntity()
    this.form = this.getNewModel()
    this.loadSelects()
    this.form.get('entidadeUuid').patchValue([this.entity])
  }

  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      tipo: [[], Validators.required],
      entidadeUuid: [],
      detalharOperacaoReceita: [],
    })
  }

  private loadSelects() {
    this.getData(`entidade/paginado`).then(res => (this.entityData = res))

    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.typeData = new DataSource({ store: res.dados })
        this.form.get('tipo').patchValue(res.dados[0].valor)
      })
  }

  private getData(uri: string) {
    return this.crudService.getDataSourceFiltro('uuid', uri, 0).load()
  }

  getEntity() {
    const userData = localStorage.getItem('userData')
    const userDataParse = JSON.parse(userData)
    this.entity = userDataParse.entidade.uuid
  }

  public submit({ value, valid }: { value: any; valid: boolean }) {
    if (!value.detalharOperacaoReceita) value.detalharOperacaoReceita = 'N'

    this.subscription = this.service
      .getPdf(value.entidadeUuid, value.tipo, value.detalharOperacaoReceita)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res: any) => {
        if (this.tipo == 'PDF') {
          this.dialogService.open(ViewReportDialogComponent, {
            context: {
              downloadName: this.pageTitle,
              data: res.dados.documento,
            },
            closeOnBackdropClick: false,
            closeOnEsc: false,
          })
        }
      })
  }

  public getDataSource(url: string, key = 'uuid', size = 10) {
    return new DataSource({
      store: this.crudService.getDataSourceFiltro(key, url, size),
      pageSize: 10,
      paginate: false,
    })
  }
}
