import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'

@Injectable({
  providedIn: 'root',
})
export class EvolutionRevenueReportService extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'evolucao_receita/relatorio')
  }

  getPdf(entidadeUuid, type, detalharOperacaoReceita?) {
    return this.http.get(
      `evolucao_receita/relatorio?entidadeUuid=${entidadeUuid}&type=${type}&detalharOperacaoReceita=${detalharOperacaoReceita}`,
    )
  }
}
