<eqp-standard-page [mainTitle]="pageTitle" [rightApproveButtonVisible]="true" [rightApproveButtonIconVisible]="true"
  [rightApproveButtonIcon]="'fas fa-save'" [rightApproveButtonId]="'submit-financial-programming-report'"
  [rightApproveButtonDisabled]="!model?.valid || model?.pristine" (rightApproveButtonEmitter)="submit()"
  [formGroup]="model">
  <div class="container">
    <div class="row mb-2">
      <div class="col-12">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" label="Entidade" placeholder=""
          formControlName="entidadeUuid" [dataSource]="entityData" [displayExpr]="'nome'"
          valueExpr="uuid"></eqp-nebular-select>
      </div>
    </div>

    <div class="mt-3">
      <eqp-fieldset label="Programação Financeira">
        <div class="d-flex flex-row" style="gap: 2rem">
          <eqp-nebular-toggle nebularLabel="Por Conta de Receita" [useNebularLabel]="true" nebularLabelPosition="right"
            formControlName="flagPorReceita"></eqp-nebular-toggle>
          <eqp-nebular-toggle nebularLabel="Por Fonte de Recurso" [useNebularLabel]="true" nebularLabelPosition="right"
            formControlName="flagPorFonteRecurso"></eqp-nebular-toggle>
        </div>
      </eqp-fieldset>
    </div>

    <div class="mt-3">
      <eqp-fieldset label="Filtros">
        <div class="row mb-2">
          <div class="col-12" formGroupName="planoReceita">
            <eqp-nebular-search-field label="Conta de receita" formControlName="info" codeLabel="Código"
              nameLabel="Nome" primaryMask="0.0.0.0.00.0.0.00.00.00.00.00" (onButtonClick)="onRevenuePlanSearchDialog()"
              (onInputChange)="onRevenuePlanSearchInput($event)" [disabled]="!revenueFlag"></eqp-nebular-search-field>

          </div>
        </div>
        <div class="row mb-2">
          <div class="col-12" formGroupName="fonteRecurso">
            <eqp-nebular-search-field label="Fonte de recurso" formControlName="info" codeLabel="Código"
              nameLabel="Nome" (onButtonClick)="onResourceSourceSearchDialog()"
              (onInputChange)="onResourceSourceSearchInput($event)" [disabled]="!resourceSourceFlag"></eqp-nebular-search-field>
          </div>
        </div>
      </eqp-fieldset>
    </div>

    <div class="row mt-3">
      <div class="col-3">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" label="Tipo de arquivo*" placeholder=""
          formControlName="tipo" [dataSource]="typeData" [displayExpr]="'valor'"
          [valueExpr]="'chave'"></eqp-nebular-select>
      </div>
    </div>
  </div>
</eqp-standard-page>
