import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan'
import { EntityInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-projection-ppa-ldo'
import { ReportPreviewComponent } from '@pages/planning/shared/reports/components/report-preview/report-preview.component'
import { ReportService } from '@pages/planning/shared/reports/services/report.service'
import { GenericRevenuePlanSearchComponent } from '@pages/planning/shared/searchs/generic-revenue-plan-search/generic-revenue-plan-search.component'
import { ResourceSourceSearchComponent } from '@pages/planning/shared/searchs/resource-source-search/resource-source-search.component'
import DataSource from 'devextreme/data/data_source'
import { Subject, combineLatest } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  first,
  map,
  startWith,
  take,
  takeUntil,
} from 'rxjs/operators'

@Component({
  selector: 'eqp-financial-programming-report',
  templateUrl: './financial-programming-report.component.html',
  styleUrls: ['./financial-programming-report.component.scss'],
})
export class FinancialProgrammingReportComponent implements OnDestroy, OnInit {
  loading = false

  pageTitle = 'Programação Financeira da Receita'

  model: FormGroup

  entityData: DataSource
  typeData: DataSource
  revenuePlandata: DataSource
  entityUuid: string

  private unsub$ = new Subject<null>()

  uri: string = 'relatorio_programacao_financeira'

  get tipo() {
    return this.model.get('tipo').value
  }

  constructor(
    private builder: FormBuilder,
    private crudService: CrudService,
    private dialogService: NbDialogService,
    private reportService: ReportService,
    private toastrService: ToastrService,
    private userDataService: UserDataService,
    private toastr: ToastrService,
  ) {}

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelects()
    this.loadHandlers()
  }

  private async loadSelects() {
    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.typeData = new DataSource({ store: res.dados })
      })

    const entities = await this.crudService
      .customGetSingleData<EntityInterface[]>(
        `relatorio_programacao_financeira/entidade`,
      )
      .toPromise()
    this.entityData = new DataSource({
      store: {
        data: entities?.data || [],
        type: 'array',
        key: 'uuid',
      },
    })
    this.entityData.load()
    const entidadeUuid = this.userDataService.userData.entidadeUuid
    this.model.get('entidadeUuid').patchValue(entidadeUuid)
    this.model.markAsDirty()
  }

  public get revenueFlag() {
    return this.model.get('flagPorReceita').value
  }

  public get resourceSourceFlag() {
    return this.model.get('flagPorFonteRecurso').value
  }

  private loadHandlers() {
    this.model
      .get('flagPorReceita')
      .valueChanges.pipe(takeUntil(this.unsub$))
      .subscribe(res => {
        this.model
          .get('flagPorFonteRecurso')
          .patchValue(!res, { emitEvent: false })
        console.log(res)
        console.log(this.model.get('flagPorFonteRecurso').value)
        if (!res && this.model.get('flagPorFonteRecurso').value) {
          this.model.get('planoReceita').reset()
        } else if (res) {
          this.model.get('fonteRecurso').reset()
        }
      })
    this.model
      .get('flagPorFonteRecurso')
      .valueChanges.pipe(takeUntil(this.unsub$))
      .subscribe(res => {
        this.model.get('flagPorReceita').patchValue(!res, { emitEvent: false })
        if (!res && this.model.get('flagPorReceita').value) {
          this.model.get('fonteRecurso').reset()
        } else if (res) {
          this.model.get('planoReceita').reset()
        }
      })
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      tipo: ['PDF'],
      entidadeUuid: ['', Validators.required],
      flagPorReceita: [true],
      flagPorFonteRecurso: [false],
      programacaoPor: [],
      fonteRecurso: this.builder.group({
        fonteRecursoUuid: [],
        info: [],
      }),
      planoReceita: this.builder.group({
        contaReceitaUuid: [],
        info: [],
      }),
    })
  }

  initializeHandlers() {
    this.model
      .get('planoReceita.codigo')
      .valueChanges.pipe(
        takeUntil(this.unsub$),
        debounceTime(1200),
        distinctUntilChanged(),
      )
      .subscribe(res => {
        this.onRevenuePlanSearchInput(res)
      })
  }

  onResourceSourceSearchDialog() {
    const ref = this.dialogService.open(ResourceSourceSearchComponent, {
      context: {
        uri: 'relatorio_programacao_financeira/fonte_recurso',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        this.setResourceSourceFields(res)
      })
  }

  onRevenuePlanSearchInput(number: any) {
    if (!number || number == '') {
      this.model.get('planoReceita').reset()
      return
    }
    if ((number >= 'a' && number <= 'z') || (number >= 'A' && number <= 'Z')) {
      this.toastr.send({
        error: true,
        message: 'Letras não são permitidas.',
      })
      return
    }
    this.loading = true
    this.revenuePlandata = new DataSource({
      store: this.crudService.getDataSourceFiltroComposto(
        'uuid',
        'relatorio_programacao_financeira/conta_receita',
        10,
        `["codigo","startswith",${number}]`,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.revenuePlandata.load().then(res => {
      if (res.length == 0) {
        this.toastr.send({
          error: true,
          message: 'Conta de receita não encontrada.',
        })
        this.model.get('planoReceita').reset()
        this.loading = false
      } else {
        this.setRevenuePlanFields(res[0])
        this.loading = false
      }
    })
  }

  onResourceSourceSearchInput(value) {
    if (!value || value == '') {
      this.model.get('fonteRecurso').reset()
      return
    }
    this.loading = true
    this.crudService
      .getDataSourceFiltro(
        'uuid',
        'relatorio_programacao_financeira/fonte_recurso',
        10,
        'codigo',
        `${value}`,
      )
      .load()
      .then(res => {
        this.loading = false
        if (res.length == 0) {
          this.toastrService.send({
            error: true,
            message: 'Fonte de recurso não encontrada.',
          })
          this.model.get('fonteRecurso').reset()
        } else {
          this.setResourceSourceFields(res[0])
        }
        err => {
          this.loading = false
          this.toastrService.send({
            error: true,
            message: `${err}`,
          })
        }
      })
  }

  onRevenuePlanSearchDialog() {
    const ref = this.dialogService.open(GenericRevenuePlanSearchComponent, {
      context: {
        uri: 'relatorio_programacao_financeira/conta_receita',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose.pipe(filter(res => res)).subscribe(res => {
      this.setRevenuePlanFields(res[0])
      this.model.markAsDirty()
    })
  }

  setRevenuePlanFields(data: RevenuePlanInterface) {
    this.model.get('planoReceita').patchValue({
      info: data,
      contaReceitaUuid: data.uuid,
    })
  }

  setResourceSourceFields(data) {
    this.model.get('fonteRecurso').patchValue({
      fonteRecursoUuid: data.uuid,
      info: data,
    })
  }

  prepare(formData: any) {
    let tipoProgramacao = {}
    if (formData.flagPorReceita) {
      tipoProgramacao = {
        programacaoPor: 1,
      }
    } else if (formData.flagPorFonteRecurso) {
      tipoProgramacao = {
        programacaoPor: 2,
      }
    }

    const dto = {
      ...formData,
      ...tipoProgramacao,
      fonteRecursoUuid: formData.fonteRecurso.fonteRecursoUuid
        ? formData.fonteRecurso.fonteRecursoUuid
        : null,
      contaReceitaUuid: formData.planoReceita.contaReceitaUuid
        ? formData.planoReceita.contaReceitaUuid
        : null,
    }

    delete dto.fonteRecurso
    delete dto.planoReceita
    delete dto.flagPorFonteRecurso
    delete dto.flagPorReceita
    return dto
  }

  submit() {
    const dto = this.prepare(this.model.getRawValue())

    if (this.tipo == 'PDF') {
      this.dialogService.open(ReportPreviewComponent, {
        context: {
          downloadName: this.pageTitle,
          rInfo: {
            dto: dto,
            url: this.uri,
          },
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
    } else {
      this.downloadReport(dto)
    }
  }

  private downloadReport(data: any) {
    this.loading = true
    this.reportService
      .generateReport(data, this.uri)
      .pipe(takeUntil(this.unsub$))
      .subscribe(
        res => {
          this.reportService.donwloadReport(res.dados, this.pageTitle)
        },
        _ => (this.loading = false),
        () => (this.loading = false),
      )
  }
}
