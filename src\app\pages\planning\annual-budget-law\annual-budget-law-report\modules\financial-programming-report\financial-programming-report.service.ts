import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';

@Injectable({
  providedIn: 'root'
})
export class FinancialProgrammingReportService extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'relatorio_programacao_financeira/relatorio')
  }
}
