import { Component, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { ViewReportDialogComponent } from '@common/dialogs/view-report-dialog/view-report-dialog.component'
import { CrudService } from '@common/services/crud.service'
import { NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { Subject, Subscription } from 'rxjs'
import { finalize, take, takeUntil } from 'rxjs/operators'
import { ParallelRevenueExpenseReportService } from './parallel-revenue-expense-report.service'
import { UserDataService } from '@guards/services/user-data.service'
import { ReportPreviewComponent } from '@pages/planning/shared/reports/components/report-preview/report-preview.component'
import { ReportService } from '@pages/planning/shared/reports/services/report.service'

@Component({
  selector: 'eqp-parallel-revenue-expense-report',
  templateUrl: './parallel-revenue-expense-report.component.html',
  styleUrls: ['./parallel-revenue-expense-report.component.scss'],
})
export class ParallelRevenueExpenseReportComponent implements OnInit {
  pageTitle: string = 'Paralelo de Receita e Despesa por Fonte'
  loading: boolean = false
  form: FormGroup

  typeData: DataSource
  entityData: any[]
  uri: string = 'paralelo_receita_despesa_por_fonte'
  private unsub$ = new Subject<null>()

  public subscription: Subscription

  get tipo() {
    return this.form.get('type').value
  }

  constructor(
    private builder: FormBuilder,
    private crudService: CrudService,
    private reportService: ReportService,
    private dialogService: NbDialogService,
    private userDataService: UserDataService
  ) {}

  ngOnInit(): void {
    this.form = this.getNewModel()
    this.loadSelects()
  }

  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      type: ['PDF', Validators.required],
      entidadesSelecionadasUuid: ['', Validators.required],
    })
  }

  private loadSelects() {
    this.crudService
      .getSingleData<any>(
        'entidade/paginado',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.entityData = res.data
      })
    const {entidadeUuid} = this.userDataService.userData
    this.form.get('entidadesSelecionadasUuid').patchValue([entidadeUuid])
    this.form.markAsDirty()

    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.typeData = new DataSource({ store: res.dados })
      })
  }

  public submit() {
    const dto = this.form.getRawValue()

    if (this.tipo == 'PDF') {
      this.dialogService.open(ReportPreviewComponent, {
        context: {
          downloadName: this.pageTitle,
          rInfo: {
            dto: dto,
            url: this.uri,
          },
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
    } else {
      this.downloadReport(dto)
    }
  }

  private downloadReport(data: any) {
    this.loading = true
    this.reportService
      .generateReport(data, this.uri)
      .pipe(takeUntil(this.unsub$))
      .subscribe(
        res => {
          this.reportService.donwloadReport(res.dados, this.pageTitle)
        },
        err => (this.loading = false),
        () => (this.loading = false),
      )
  }

  public getDataSource(url: string, key = 'uuid', size = 10) {
    return new DataSource({
      store: this.crudService.getDataSourceFiltro(key, url, size),
      pageSize: 10,
      paginate: false,
    })
  }
}
