import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'

@Injectable({
  providedIn: 'root',
})
export class ParallelRevenueExpenseReportService extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'paralelo_receita_despesa_por_fonte/relatorio')
  }

  getPdf(entidadeUuid, type, detalharOperacaoReceita?) {
    return this.http.get(
      `paralelo_receita_despesa_por_fonte/relatorio?entidadeUuid=${entidadeUuid}&type=${type}`,
    )
  }
}
