import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { AnnualBudgetLawComponent } from './annual-budget-law.component'
import { ExpenseEvolutionListComponent } from './expense-evolution/expense-evolution-list/expense-evolution-list.component'
import { InitialExpenseForecastReviewFormComponent } from './initial-expense-forecast-review/initial-expense-forecast-review-form/initial-expense-forecast-review-form.component'
import { InitialExpenseForecastReviewListComponent } from './initial-expense-forecast-review/initial-expense-forecast-review-list/initial-expense-forecast-review-list.component'
import { InitialExpenseForecastFormComponent } from './initial-expense-forecast/initial-expense-forecast-form/initial-expense-forecast-form.component'
import { InitialExpenseForecastListComponent } from './initial-expense-forecast/initial-expense-forecast-list/initial-expense-forecast-list.component'
import { InitialExpenseForecastSourceFormComponent } from './initial-expense-forecast/source/initial-expense-forecast-source-form/initial-expense-forecast-source-form.component'
import { InitialRevenueForecastReviewFormComponent } from './initial-revenue-forecast-review/initial-revenue-forecast-review-form/initial-revenue-forecast-review-form.component'
import { InitialRevenueForecastReviewListComponent } from './initial-revenue-forecast-review/initial-revenue-forecast-review-list/initial-revenue-forecast-review-list.component'
import { InitialRevenueForecastFormComponent } from './initial-revenue-forecast/initial-revenue-forecast-form/initial-revenue-forecast-form.component'
import { InitialRevenueForecastListComponent } from './initial-revenue-forecast/initial-revenue-forecast-list/initial-revenue-forecast-list.component'
import { InitialRevenueForecastOperationComponent } from './initial-revenue-forecast/operation/initial-revenue-forecast-operation.component'
import { RevenueEvolutionDetailComponent } from './revenue-evolution/revenue-evolution-detail/revenue-evolution-detail.component'
import { RevenueEvolutionListComponent } from './revenue-evolution/revenue-evolution-list/revenue-evolution-list.component'
import { RevenueFromToFormComponent } from './revenue-from-to/revenue-from-to-form/revenue-from-to-form.component'
import { RevenueFromToListComponent } from './revenue-from-to/revenue-from-to-list/revenue-from-to-list.component'
import { RevenueProjectionComponent } from './revenue-projection/revenue-projection.component'
import { RelatorioOrcamentoCriancaComponent } from './relatorio-orcamento-crianca/relatorio-orcamento-crianca.component'

const routes: Routes = [
  {
    path: '',
    component: AnnualBudgetLawComponent,
    children: [
      {
        path: 'receita/previsao-inicial',
        component: InitialRevenueForecastListComponent,
      },
      {
        path: 'receita/previsao-inicial/novo',
        component: InitialRevenueForecastFormComponent,
      },
      {
        path: 'receita/previsao-inicial/edit/:uuid',
        component: InitialRevenueForecastFormComponent,
      },
      {
        path: 'receita/previsao-inicial/operacao/:uuid',
        component: InitialRevenueForecastOperationComponent,
      },
      {
        path: 'receita/previsao-inicial/configuracao/:uuid',
        loadChildren: () =>
          import(
            './initial-revenue-forecast/configuration/configuration.module'
          ).then(m => m.ConfigurationModule),
      },
      {
        path: 'receita/evolucao',
        component: RevenueEvolutionListComponent,
      },
      {
        path: 'receita/evolucao/edit/:uuid',
        component: RevenueEvolutionDetailComponent,
      },
      {
        path: 'receita/de-para',
        component: RevenueFromToListComponent,
      },
      {
        path: 'receita/de-para/novo',
        component: RevenueFromToFormComponent,
      },
      {
        path: 'receita/revisao-previsao-inicial',
        component: InitialRevenueForecastReviewListComponent,
      },
      {
        path: 'receita/revisao-previsao-inicial/novo',
        component: InitialRevenueForecastReviewFormComponent,
      },
      {
        path: 'receita/projecao-receita',
        loadChildren: () =>
          import('./revenue-projection/revenue-projection.module').then(
            m => m.RevenueProjectionModule,
          ),
      },
      {
        path: 'receita/revisao-previsao-inicial/edit/:uuid',
        component: InitialRevenueForecastReviewFormComponent,
      },
      {
        path: 'despesa/previsao-inicial',
        component: InitialExpenseForecastListComponent,
      },
      {
        path: 'despesa/previsao-inicial/edit/:uuid',
        component: InitialExpenseForecastFormComponent,
      },
      {
        path: 'despesa/previsao-inicial/edit/:parentUuid/fonte/:uuid',
        component: InitialExpenseForecastSourceFormComponent,
      },
      {
        path: 'despesa/evolucao',
        component: ExpenseEvolutionListComponent,
      },
      {
        path: 'despesa/revisao-previsao-inicial',
        component: InitialExpenseForecastReviewListComponent,
      },
      {
        path: 'despesa/revisao-previsao-inicial/novo',
        component: InitialExpenseForecastReviewFormComponent,
      },
      {
        path: 'despesa/revisao-previsao-inicial/edit/:uuid',
        component: InitialExpenseForecastReviewFormComponent,
      },
      {
        path: 'relatorio',
        loadChildren: () =>
          import(
            './annual-budget-law-report/annual-budget-law-report.module'
          ).then(m => m.AnnualBudgetLawReportModule),
      },
      {
        path: 'relatorio/orcamento-crianca',
        component: RelatorioOrcamentoCriancaComponent
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AnnualBudgetLawRoutingModule {}
