import { PublicationNoteFieldModule } from './../../shared/components/publication-note-field/publication-note-field.module';
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { AnnualBudgetLawRoutingModule } from './annual-budget-law-routing.module'
import { AnnualBudgetLawComponent } from './annual-budget-law.component'
import { ExpenseEvolutionEditComponent } from './expense-evolution/expense-evolution-edit/expense-evolution-edit.component'
import { ExpenseEvolutionListComponent } from './expense-evolution/expense-evolution-list/expense-evolution-list.component'
import { ExpenseEvolutionNewComponent } from './expense-evolution/expense-evolution-new/expense-evolution-new.component'
import { InitialExpenseForecastReviewListComponent } from './initial-expense-forecast-review/initial-expense-forecast-review-list/initial-expense-forecast-review-list.component'
import { InitialExpenseForecastFormDialogComponent } from './initial-expense-forecast/initial-expense-forecast-form-dialog/initial-expense-forecast-form-dialog.component'
import { InitialExpenseForecastListComponent } from './initial-expense-forecast/initial-expense-forecast-list/initial-expense-forecast-list.component'
import { InitialRevenueForecastReviewAccountDialogComponent } from './initial-revenue-forecast-review/initial-revenue-forecast-review-account-dialog/initial-revenue-forecast-review-account-dialog.component'
import { InitialRevenueForecastReviewAccountListComponent } from './initial-revenue-forecast-review/initial-revenue-forecast-review-account-list/initial-revenue-forecast-review-account-list.component'
import { InitialRevenueForecastReviewFormComponent } from './initial-revenue-forecast-review/initial-revenue-forecast-review-form/initial-revenue-forecast-review-form.component'
import { InitialRevenueForecastReviewListComponent } from './initial-revenue-forecast-review/initial-revenue-forecast-review-list/initial-revenue-forecast-review-list.component'
import { InitialRevenueForecastFormDialogComponent } from './initial-revenue-forecast/initial-revenue-forecast-form-dialog/initial-revenue-forecast-form-dialog.component'
import { InitialRevenueForecastFormComponent } from './initial-revenue-forecast/initial-revenue-forecast-form/initial-revenue-forecast-form.component'
import { InitialRevenueForecastListComponent } from './initial-revenue-forecast/initial-revenue-forecast-list/initial-revenue-forecast-list.component'
import { InitialRevenueForecastOperationComponent } from './initial-revenue-forecast/operation/initial-revenue-forecast-operation.component'
import { InitialRevenueForecastSourceListComponent } from './initial-revenue-forecast/operation/initial-revenue-forecast-source-list/initial-revenue-forecast-source-list.component'
import { RevenueFinancialProgramingListComponent } from './initial-revenue-forecast/operation/revenue-financial-programing-list/revenue-financial-programing-list.component'
import { RevenueReestimationFormDialogComponent } from './initial-revenue-forecast/operation/revenue-reestimation-list/revenue-reestimation-form-dialog/revenue-reestimation-form-dialog.component'
import { RevenueReestimationListComponent } from './initial-revenue-forecast/operation/revenue-reestimation-list/revenue-reestimation-list.component'
import { RevenueEvolutionDetailComponent } from './revenue-evolution/revenue-evolution-detail/revenue-evolution-detail.component'
import { RevenueEvolutionFormComponent } from './revenue-evolution/revenue-evolution-form/revenue-evolution-form.component'
import { RevenueEvolutionListComponent } from './revenue-evolution/revenue-evolution-list/revenue-evolution-list.component'
import { RevenuePlanSearchComponent } from './revenue-evolution/revenue-plan-search/revenue-plan-search.component'
import { RevenueFromToFormComponent } from './revenue-from-to/revenue-from-to-form/revenue-from-to-form.component'
import { RevenueFromToListComponent } from './revenue-from-to/revenue-from-to-list/revenue-from-to-list.component';
import { InitialExpenseForecastReviewFormComponent } from './initial-expense-forecast-review/initial-expense-forecast-review-form/initial-expense-forecast-review-form.component';
import { InitialExpenseForecastReviewAccountListComponent } from './initial-expense-forecast-review/initial-expense-forecast-review-account-list/initial-expense-forecast-review-account-list.component';
import { InitialExpenseForecastReviewAccountDialogComponent } from './initial-expense-forecast-review/initial-expense-forecast-review-account-dialog/initial-expense-forecast-review-account-dialog.component';
import { InitialExpenseForecastFormComponent } from './initial-expense-forecast/initial-expense-forecast-form/initial-expense-forecast-form.component';
import { InitialExpenseForecastSourceFormComponent } from './initial-expense-forecast/source/initial-expense-forecast-source-form/initial-expense-forecast-source-form.component'
import { InitialExpenseForecastSourceListComponent } from './initial-expense-forecast/source/initial-expense-forecast-source-list/initial-expense-forecast-source-list.component';
import { InitialExpenseForecastSourceFormDialogComponent } from './initial-expense-forecast/source/initial-expense-forecast-source-form-dialog/initial-expense-forecast-source-form-dialog.component';
import { DisbursementScheduleSourceComponent } from './initial-expense-forecast/source/disbursement-schedule-source/disbursement-schedule-source.component'
import { NbButtonModule, NbFormFieldModule } from '@nebular/theme';
import { ExpensePlanBudgetLevelSearchComponent } from './initial-expense-forecast/initial-expense-forecast-form-dialog/dialogs/expense-plan-budget-level-search/expense-plan-budget-level-search.component';
import { ProgramSearchModule } from '../shared/searchs/program-search/program-search.module';
import { UnitySearchModule } from '../shared/searchs/unity-search/unity-search.module';
import { FunctionSearchModule } from '../shared/searchs/function-search/function-search.module';
import { OrganSearchModule } from '../shared/searchs/organ-search/organ-search.module';
import { ProjectActivitySearchModule } from '../shared/searchs/project-activity-search/project-activity-search.module';
import { DefaultSearchDialogModule } from '../shared/searchs/default-search-dialog/default-search-dialog.module';
import { InitialExpenseForecastSourceSearchComponent } from './initial-expense-forecast/initial-expense-forecast-source-search/initial-expense-forecast-source-search.component';
import { CopiarOrcamentoReceitaComponent } from './initial-revenue-forecast/copiar-orcamento-receita/copiar-orcamento-receita.component';
import { BuscaAvancadaMultiplaVisualizarModule } from '@pages/shared/components/busca-avancada-multipla-visualizar/busca-avancada-multipla-visualizar.module';
import { CopiarOrcamentoDespesaComponent } from './initial-expense-forecast/copiar-orcamento-despesa/copiar-orcamento-despesa.component';
import { RelatorioOrcamentoCriancaComponent } from './relatorio-orcamento-crianca/relatorio-orcamento-crianca.component';

@NgModule({
  declarations: [
    AnnualBudgetLawComponent,
    InitialRevenueForecastFormComponent,
    InitialRevenueForecastListComponent,
    RevenueEvolutionDetailComponent,
    RevenueEvolutionListComponent,
    RevenueEvolutionFormComponent,
    RevenuePlanSearchComponent,
    InitialRevenueForecastReviewListComponent,
    InitialRevenueForecastReviewFormComponent,
    InitialRevenueForecastFormDialogComponent,
    InitialRevenueForecastReviewAccountListComponent,
    InitialRevenueForecastReviewAccountDialogComponent,
    InitialRevenueForecastOperationComponent,
    RevenueReestimationListComponent,
    RevenueReestimationFormDialogComponent,
    RevenueFinancialProgramingListComponent,
    InitialExpenseForecastListComponent,
    InitialExpenseForecastFormDialogComponent,
    InitialExpenseForecastReviewListComponent,
    ExpenseEvolutionListComponent,
    ExpenseEvolutionEditComponent,
    ExpenseEvolutionNewComponent,
    InitialRevenueForecastSourceListComponent,
    RevenueFromToFormComponent,
    RevenueFromToListComponent,
    InitialExpenseForecastReviewFormComponent,
    InitialExpenseForecastReviewAccountListComponent,
    InitialExpenseForecastReviewAccountDialogComponent,
    InitialExpenseForecastFormComponent,
    InitialExpenseForecastSourceListComponent,
    InitialExpenseForecastSourceFormComponent,
    InitialExpenseForecastSourceFormDialogComponent,
    DisbursementScheduleSourceComponent,
    ExpensePlanBudgetLevelSearchComponent,
    InitialExpenseForecastSourceSearchComponent,
    CopiarOrcamentoReceitaComponent,
    CopiarOrcamentoDespesaComponent,
    RelatorioOrcamentoCriancaComponent,
  ],
  imports: [CommonModule,
    AnnualBudgetLawRoutingModule,
    CommonToolsModule,
    NbFormFieldModule,
    PublicationNoteFieldModule,
    NbButtonModule,
    ProgramSearchModule,
    UnitySearchModule,
    FunctionSearchModule,
    OrganSearchModule,
    ProjectActivitySearchModule,
    ProgramSearchModule,
    DefaultSearchDialogModule,
    BuscaAvancadaMultiplaVisualizarModule
  ],
  exports: [AnnualBudgetLawRoutingModule],
})
export class AnnualBudgetLawModule {}
