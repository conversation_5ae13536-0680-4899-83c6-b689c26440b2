<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <eqp-nebular-input
      [style]="'basic'"
      [size]="'small'"
      [shape]="'rectangle'"
      formControlName="planoDespesa"
      name="planoDespesa"
      label="Plano de despesa"
      readonly="true"
    >
    </eqp-nebular-input>
    <br />
    <h6>Despesa realizada</h6>
    <div class="row">
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio1"
          name="vlrExercicio1"
          [label]="data.exercicioLogado - 2"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <br />
    <h6>Despesa autorizada</h6>
    <div class="row">
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio2"
          name="vlrExercicio2"
          [label]="data.exercicioLogado - 1"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio3"
          name="vlrExercicio3"
          [readonly]="true"
          [label]="data.exercicioLogado"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="model.invalid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
