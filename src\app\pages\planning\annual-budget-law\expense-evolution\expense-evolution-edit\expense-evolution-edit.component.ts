import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import { finalize, first } from 'rxjs/operators';
import { ExpenseEvolutionService } from '../services/expense-evolution.service';

@Component({
  selector: 'eqp-expense-evolution-edit',
  templateUrl: './expense-evolution-edit.component.html',
  styleUrls: ['./expense-evolution-edit.component.scss']
})
export class ExpenseEvolutionEditComponent implements OnInit {

	@Input() public data: {
		evolucaoDespesaUuid: string
		planoDespesa: {
			nome: string,
			uuid: string
		}
		vlrExercicio1: number
		vlrExercicio2: number
		vlrExercicio3: number
		exercicioLogado: number
	}

	public model: FormGroup
	public pageTitle = 'Editar plano de despesa'
	public loading: boolean = false

  constructor(
		protected ref: NbDialogRef<ExpenseEvolutionEditComponent>,
		private formBuilder: FormBuilder,
		private service: ExpenseEvolutionService,
		private toastrService: ToastrService
	) {}

  ngOnInit(): void {
		this.model = this.getNovoFormulario()
  }

	public getNovoFormulario() {
		return this.formBuilder.group({
			planoDespesa: [this.data.planoDespesa.nome, Validators.required],
			vlrExercicio1: [this.data.vlrExercicio1, Validators.required],
			vlrExercicio2: [this.data.vlrExercicio2, Validators.required],
			vlrExercicio3: [this.data.vlrExercicio3, Validators.required]
		})
	}

	public cancel() {
		this.ref.close(null)
	}

	public confirm() {
		this.loading = true
		const {
			vlrExercicio1, vlrExercicio2, vlrExercicio3
		} = this.model.getRawValue()
		const dto = {
			uuid: this.data.evolucaoDespesaUuid,
			planoDespesaUuid: this.data.planoDespesa.uuid, 
			vlrExercicio1, vlrExercicio2, vlrExercicio3
		}
		this.service.put(dto)
			.pipe(
				first(),
				finalize(() => {
					this.loading = false
				})
			)
			.subscribe(res => {
				this.toastrService.send({
					success: true,
					message: 'Evolução de despesa atualizada com sucesso'
				})
				this.ref.close(res)
			})
	}
}
