import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataInterface } from '@guards/services/user-data'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { filter, finalize, first, take } from 'rxjs/operators'
import { ExpenseEvolutionEditComponent } from '../expense-evolution-edit/expense-evolution-edit.component'
import { ExpenseEvolutionNewComponent } from '../expense-evolution-new/expense-evolution-new.component'
import { ExpenseEvolutionService } from '../services/expense-evolution.service'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'

@Component({
  selector: 'eqp-expense-evolution-list',
  templateUrl: './expense-evolution-list.component.html',
  styleUrls: ['./expense-evolution-list.component.scss'],
})
export class ExpenseEvolutionListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle = 'Evolução da despesa'
  public dataSource: DataSource
  public userData: UserDataInterface
  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  constructor(
    public menuService: MenuService,
    public router: Router,
    private expenseEvolutionService: ExpenseEvolutionService,
    private userService: UserDataService,
    private toastrService: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao('/lei-orcamentaria-anual/despesa/evolucao')
  }

  ngOnInit(): void {
    this.userData = this.userService.userData
    this.fetchGrid()
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.expenseEvolutionService.getDataSourceFiltro(
        'uuid',
        'evolucao_despesa/paginado',
        10,
        'exerciseUuid',
        this.userData.exercicioUuid,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Evolução da despesa'
          item.options.hint = 'Nova operação da despesa'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public modalAvisoImportar() {
    const dialogRef = this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          confirmType: 'success',
          cancelText: 'Não',
          cancelTitle: 'Não',
          confirmText: 'Sim',
          confirmTitle: 'Sim',
          body:
            'Este processo realizará uma busca automática da despesa realizada' +
            ' e autorizada, para todos os exercícios possíveis.\n\nConfirma o processamento?',
        },
        dialogSize: 'medium',
        exiberIcones: false,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose
      .pipe(
        first(),
        filter(res => res),
      )
      .subscribe(res => {
        this.importarValores()
      })
  }

  private importarValores() {
    this.loading = true
    this.expenseEvolutionService
      .importar()
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(_ => {
        this.toastrService.send({
          success: true,
          message: 'Importação realizada com sucesso.',
        })
        this.fetchGrid()
      })
  }

  public novoRegistro(): void {
    const dialogRef = this.dialogService.open(ExpenseEvolutionNewComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(res => {
      if (res) {
        this.fetchGrid()
      }
    })
  }

  public alterar(value) {
    const dialogRef = this.dialogService.open(ExpenseEvolutionEditComponent, {
      context: {
        data: {
          evolucaoDespesaUuid: value.uuid,
          planoDespesa: {
            nome: value.planoDespesa.nome,
            uuid: value.planoDespesa.uuid,
          },
          vlrExercicio1: value.vlrExercicio1,
          vlrExercicio2: value.vlrExercicio2,
          vlrExercicio3: value.vlrExercicio3,
          exercicioLogado: +this.userData.exercicio,
        },
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(res => {
      if (res) {
        this.fetchGrid()
      }
    })
  }
}
