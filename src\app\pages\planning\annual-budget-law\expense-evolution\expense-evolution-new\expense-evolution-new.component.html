<eqp-nebular-dialog
	[dialogTitle]="pageTitle"
	[spinnerActive]="loading"
	[spinnerStatus]="'info'"
	[bottomLeftButtonText]="'Voltar'"
	[bottomLeftButtonVisible]="true"
	(bottomLeftButtonEmitter)="cancel()"
	[topRightButtonVisible]="true"
	[topRightButtonIconVisible]="true"
	[topRightButtonTitle]="'Importar todas as contas de despesa'"
	[topRightButtonIcon]="'fas fa-file-import'"
	[topRightButtonId]="'submit-all-expense-plan'"
	(topRightButtonEmitter)="import()"
	[rightFirstButtonVisible]="true"
	[rightFirstButtonIconVisible]="true"
	[rightFirstButtonText]="'Confirmar'"
	[rightFirstButtonIcon]="'fas fa-check'"
	[rightFirstButtonId]="'submit-expense-plan-selection'"
	[rightFirstButtonDisabled]="selectedRowKeys.length == 0"
	(rightFirstButtonEmitter)="confirm()"
>

  <ng-container>
    <dx-data-grid
      id="planoDespesaGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      [(selectedRowKeys)]="selectedRowKeys"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>

      <dxo-selection
        selectAllMode="page"
        showCheckBoxesMode="onClick"
        mode="multiple"
      ></dxo-selection>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar despesa"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column dataField="codigo" caption="Código">
      </dxi-column>

      <dxi-column dataField="nome" caption="Nome"> </dxi-column>

    </dx-data-grid>
  </ng-container>
	
</eqp-nebular-dialog> 
