import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ExpenseEvolutionNewComponent } from './expense-evolution-new.component';

describe('ExpenseEvolutionNewComponent', () => {
  let component: ExpenseEvolutionNewComponent;
  let fixture: ComponentFixture<ExpenseEvolutionNewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ExpenseEvolutionNewComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ExpenseEvolutionNewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
