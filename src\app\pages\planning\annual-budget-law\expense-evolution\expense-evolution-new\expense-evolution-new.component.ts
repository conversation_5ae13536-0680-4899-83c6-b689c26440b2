import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { ExpensePlanInterface } from '@pages/planning/multi-year-plan/interfaces/expense-plan'
import { ExpensePlanService } from '@pages/planning/multi-year-plan/services/expense-plan.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { ExpenseEvolutionService } from '../services/expense-evolution.service'

@Component({
  selector: 'eqp-expense-evolution-new',
  templateUrl: './expense-evolution-new.component.html',
  styleUrls: ['./expense-evolution-new.component.scss'],
})
export class ExpenseEvolutionNewComponent
  extends BaseTelasComponent
  implements OnInit
{
  loading: boolean
  dataSource: DataSource<ExpensePlanInterface, string>
  selectedRowKeys: string[] = []
  pageTitle = 'Selecionar conta de despesa'

  constructor(
    public router: Router,
    public menuService: MenuService,
    protected ref: NbDialogRef<ExpenseEvolutionNewComponent>,
    private expenseService: ExpensePlanService,
    private expenseEvolutionService: ExpenseEvolutionService,
    private toastrService: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  public fetchGrid() {
    this.dataSource = new DataSource({
      store: this.expenseService.getDataSourceFiltroComposto(
        'uuid',
        'plano_despesa/paginado',
        10,
        `["nivel","=","2"],"or",["nivel","=","1"]`,
      ),
    })
  }

  confirm() {
    this.loading = true
    this.expenseEvolutionService
      .postLote(
        this.selectedRowKeys.map(value => ({
          planoDespesaUuid: value,
          vlrExercicio1: 0,
          vlrExercicio2: 0,
          vlrExercicio3: 0,
        })),
      )
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.toastrService.send({
          success: true,
          message: 'Planos de despesa selecionados foram importados.',
        })
        this.ref.close(res.status)
      })
  }

  import() {
    this.loading = true
    this.expenseEvolutionService
      .importar()
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.toastrService.send({
          success: true,
          message: 'Todos os planos de despesa foram importados.',
        })
        this.ref.close(res.status)
      })
  }

  public cancel() {
    this.ref.close(null)
  }
}
