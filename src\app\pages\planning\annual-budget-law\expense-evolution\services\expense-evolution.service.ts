import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { ExpenseEvolutionImportInterface, ExpenseEvolutionInterface } from '../interfaces/expense-evolution';

@Injectable({
  providedIn: 'root'
})
export class ExpenseEvolutionService extends BaseService<
	ResponseDto<ExpenseEvolutionInterface>,
	ExpenseEvolutionInterface
>
{
  constructor(
		private httpParameter: HttpClient
	) {
		super(httpParameter, 'evolucao_despesa')
	}

	public postLote(lote: ExpenseEvolutionImportInterface[]) {
		const headers = new HttpHeaders()
		return this.http.post<null>(
			'evolucao_despesa/lote', lote, {
			headers,
			observe: 'response'
		})
	}

	public importar() {
		const headers = new HttpHeaders()
		return this.http.post<null>(
			'evolucao_despesa/importar', null, {
				headers,
				observe: 'response'
			}
		)
	}
}
