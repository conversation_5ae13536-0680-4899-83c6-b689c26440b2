<nb-card style="position: relative;">
  <eqp-loading *ngIf="loading"></eqp-loading>
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
        <eqp-breadcrumb></eqp-breadcrumb>
      </div>

    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="projectActivity">
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        [type]="'text'"
        formControlName="tipo"
        name="Tipo"
        label="Tipo"
        placeholder="Tipo"
        errorMessage="É obrigatório preencher o tipo"
        primaryMask="9"
        maxlength="1"
        required="true"
      >
      </eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        [type]="'text'"
        formControlName="ordem"
        name="Ordem"
        label="Ordem"
        placeholder="Ordem"
        errorMessage="É obrigatório preencher o ordem"
        primaryMask="999"
        maxlength="3"
        required="true"
      >
      </eqp-nebular-input>
      </div>
      <div [class]="'col-md-6' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="nome"
        name="Nome"
        label="Nome"
        placeholder="Nome"
        errorMessage="É obrigatório preencher o nome"
        maxlength="100"
        required="true"
      >
      </eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'date'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="dataInclusaoTce"
        name="Inclusão TCE"
        label="Inclusão TCE"
        placeholder="Inclusão TCE"
        errorMessage="É obrigatório preencher a inclusão TCE"
        required="true"
      >
      </eqp-nebular-input>
      </div>
    </div>
    <div class="row" formGroupName="projectActivity">
      <div [class]="'col-md-2' + ' col-sm-12 align-self-center'">
        <eqp-nebular-checkbox 
          [style]="'basic'" 
          name="Covid-19"
          label="Covid-19"
          formControlName="flagCovid19"
          >
        </eqp-nebular-checkbox>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 align-self-center'">
        <eqp-nebular-checkbox 
          [style]="'basic'" 
          name="Pertence ao orçamento criança "
          label="Pertence ao orçamento criança "
          formControlName="flagOrcamentoCrianca"
          (change)="onChange()"
          >
        </eqp-nebular-checkbox>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4 align-self-center'">
      <div 
        class="dx-field" 
        [ngStyle]="{'display': 'grid'}"
      >
        <eqp-nebular-input [style]="'currency'" [options]="{ prefix: ' ', suffix: ' %', thousands: '.', decimal: ',' }"
          [size]="'small'" [shape]="'rectangle'" label="Percentual orçamento" placeholder="00,00"   [disabled]="!formulario.get('projectActivity.flagOrcamentoCrianca')?.value"
          formControlName="percentualOrcamentoCrianca">
        </eqp-nebular-input>
      </div>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Caráter" label="Caráter"
          placeholder="Caráter" formControlName="caraterExclusivoUuid" [dataSource]="formulario.get('projectActivity.flagOrcamentoCrianca')?.value !== true ? caraterExclusivoData : caraterExclusivoPertenceData"
          valueExpr="uuid" displayExpr="nome" required="true" [disabled]="formulario.get('projectActivity.flagOrcamentoCrianca')?.value !== true"></eqp-nebular-select>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Fase" label="Fase"
          placeholder="Fase" formControlName="faseContaUuid" [dataSource]="faseData"
          valueExpr="uuid" displayExpr="nome" disabled></eqp-nebular-select>
      </div>
    </div>
    <div class="row" formGroupName="projectActivity">
      <div [class]="'col-md-12' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'textArea'"
        [size]="'small'"
        [shape]="'rectangle'"
        rows="4"
        formControlName="objetivo"
        name="Objetivo"
        label="Objetivo"
        placeholder="Objetivo"
      >
      </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button
        type="button"
        class="btn btn-dark"
        (click)="cancelar(null)"
      >Voltar
      </button>
      <button
        *ngIf="formulario.get('uuid')?.value"
        type="button"
        class="btn btn-danger ml-3 float-md-right"
        (click)="remover()"
      >Apagar
      </button>
      <button
        type="button"
        class="btn btn-success float-md-right"
        (click)="gravar()"
      >Confirmar
      </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>