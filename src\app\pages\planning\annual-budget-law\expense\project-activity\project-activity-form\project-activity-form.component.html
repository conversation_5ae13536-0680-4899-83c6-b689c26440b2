<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>

    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="projectActivity">
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        [type]="'text'"
        formControlName="tipo"
        name="Tipo"
        label="Tipo"
        placeholder="Tipo"
        errorMessage="É obrigatório preencher o tipo"
        primaryMask="9"
        maxlength="1"
        required="true"
      >
      </eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        [type]="'text'"
        formControlName="ordem"
        name="Ordem"
        label="Ordem"
        placeholder="Ordem"
        errorMessage="É obrigatório preencher o ordem"
        primaryMask="999"
        maxlength="3"
        required="true"
      >
      </eqp-nebular-input>
      </div>
      <div [class]="'col-md-6' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="nome"
        name="Nome"
        label="Nome"
        placeholder="Nome"
        errorMessage="É obrigatório preencher o nome"
        maxlength="100"
        required="true"
      >
      </eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'date'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="dataInclusaoTce"
        name="Inclusão TCE"
        label="Inclusão TCE"
        placeholder="Inclusão TCE"
        errorMessage="É obrigatório preencher a inclusão TCE"
        required="true"
      >
      </eqp-nebular-input>
      </div>
    </div>
    <div class="row" formGroupName="projectActivity">
      <div [class]="'col-md-2' + ' col-sm-12 align-self-center'">
        <eqp-nebular-checkbox 
          [style]="'basic'" 
          name="Covid-19"
          label="Covid-19"
          formControlName="flagCovid19"
          >
        </eqp-nebular-checkbox>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 align-self-center'">
        <eqp-nebular-checkbox 
          [style]="'basic'" 
          name="Pertence ao orçamento criança "
          label="Pertence ao orçamento criança "
          formControlName="flagOrcamentoCrianca"
          (change)="onChange()"
          >
        </eqp-nebular-checkbox>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4 align-self-center'">
      <div 
        class="dx-field" 
        [ngStyle]="{'display': 'grid'}"
      >
        <div class="dx-field-label w-100 mt-1 mb-2" [ngStyle]="{'color': '#8f9bb3', 'font-weight': '700'}">Percentual orçamento</div>
        <div class="dx-field-value w-100">
          <dx-number-box 
          [ngStyle]="{'heigth': '30px', 'color': '#222b45', 'border': '1px solid #e4e9f2', 'background-color': '#f7f9fc'}"
          class="number-input-percentage" 
          format="#,##0,00" 
          formControlName="percentualOrcamentoCrianca"
          [validationStatus]="formulario.get('projectActivity.flagOrcamentoCrianca')?.value === true && formulario.get('projectActivity.percentualOrcamentoCrianca')?.value === null ? 'invalid' : 'valid'"
          [max]="10000"
          [min]="0"
          [disabled]="!formulario.get('projectActivity.flagOrcamentoCrianca')?.value">
        </dx-number-box>
        </div>
      </div>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Caráter" label="Caráter"
          placeholder="Caráter" formControlName="caraterExclusivoUuid" [dataSource]="formulario.get('projectActivity.flagOrcamentoCrianca')?.value !== true ? caraterExclusivoData : caraterExclusivoPertenceData"
          valueExpr="uuid" displayExpr="nome" required="true" [disabled]="formulario.get('projectActivity.flagOrcamentoCrianca')?.value !== true"></eqp-nebular-select>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Fase" label="Fase"
          placeholder="Fase" formControlName="faseContaUuid" [dataSource]="faseData"
          valueExpr="uuid" displayExpr="nome" disabled></eqp-nebular-select>
      </div>
    </div>
    <div class="row" formGroupName="projectActivity">
      <div [class]="'col-md-12' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'textArea'"
        [size]="'small'"
        [shape]="'rectangle'"
        rows="4"
        formControlName="objetivo"
        name="Objetivo"
        label="Objetivo"
        placeholder="Objetivo"
      >
      </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button
        type="button"
        class="btn btn-dark"
        (click)="cancelar(null)"
      >Voltar
      </button>
      <button
        *ngIf="formulario.get('uuid')?.value"
        type="button"
        class="btn btn-danger ml-3 float-md-right"
        (click)="remover()"
      >Apagar
      </button>
      <button
        type="button"
        class="btn btn-success float-md-right"
        (click)="gravar()"
      >Gravar
      </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>