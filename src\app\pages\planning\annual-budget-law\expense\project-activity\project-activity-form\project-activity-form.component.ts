import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import moment from 'moment'
import {
  ModalConfirmarComponent,
} from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { finalize, first } from 'rxjs/operators'

import {
  ToastrService,
} from '../../../../../../@common/services/toastr/toastr.service'
import { MenuService } from '../../../../../menu.service'
import { ProjectActivityService } from '../project-activity.service'
import { forkJoin } from 'rxjs'

@Component({
  selector: 'eqp-project-activity-form',
  templateUrl: './project-activity-form.component.html',
  styleUrls: ['./project-activity-form.component.scss'],
})
export class ProjectActivityFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy {
  public loading: boolean = false
  public pageTitle: string = 'Projeto atividade'
  public exercicioData: any
  public faseData: any
  public formulario: FormGroup
  public nomeData: any
  public caraterExclusivoData: any
  public caraterExclusivoPadrao: any
  public caraterExclusivoPertenceData: any
  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<ProjectActivityFormComponent>

  constructor(
    private formBuilder: FormBuilder,
    private service: ProjectActivityService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/projeto-atividade')
  }

  public ngOnInit(): void {
    this.loadSelects()
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  public ngOnDestroy(): void { }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      projectActivity: this.formBuilder.group({
        uuid: [''],
        caraterExclusivoUuid: [null, Validators.required],
        dataInclusaoTce: [null, Validators.required],
        faseContaUuid: [null, Validators.required],
        exercicioStatus: [null],
        flagCovid19: [null],
        flagOrcamentoCrianca: [null],
        nome: [null, Validators.required],
        objetivo: [null],
        ordem: [null, Validators.min(1)],
        percentualOrcamentoCrianca: [null],
        tipo: [null, Validators.required],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid && !this.modal) this.buscar(uuid)
    })
  }

  public loadSelects(): void {
    this.loading = true

    forkJoin({
      exercicioStatus: this.service.getExercicioStatus().pipe(first()),
      caraterExclusivo: this.service.getCaraterExclusivo().pipe(first()),
      faseConta: this.service.getFaseConta().pipe(first()),
    })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe({
        next: results => {
          this.exercicioData = results.exercicioStatus.dados
          this.caraterExclusivoData = results.caraterExclusivo.data

          results.faseConta.data.forEach((item: any) => {
            item.selecionado = new FormControl(false)
          })
          this.faseData = results.faseConta.data

          this.processarDadosFormulario()
        },
        error: error => {
          this.toastr.send({
            error: true,
            message: 'Erro ao carregar dados. Por favor, tente novamente.',
          })
        },
      })
  }

  private processarDadosFormulario(): void {
    const caraterPadrao = this.caraterExclusivoData.filter((x: any) => x.nome === 'Não se aplica');
    const caraterDiferente = this.caraterExclusivoData.filter((x: any) => x.nome !== 'Não se aplica');

    if (this.formulario && this.formulario.get('projectActivity.uuid').value === '') {
      const execucao = ['Execução Orçamentária', 'Execução Financeira', 'Execução Completa'].includes(this.exercicioData.exercicioStatus.nome);
      const meuExercicio = execucao ? 'Execução' : 'Planejamento';
      const minhaFase = this.faseData.filter((x: any) => x.nome === meuExercicio);

      if (minhaFase.length > 0) {
        if (this.exercicioData.exercicioStatus.nome !== "Inativo" && this.exercicioData.exercicioStatus.nome !== "Fechado") {
          this.formulario.get('projectActivity.faseContaUuid').patchValue(minhaFase.length > 0 && minhaFase[0].uuid);
        }
      } else {
        this.formulario.get('projectActivity.exercicioStatus').patchValue(this.exercicioData.exercicioStatus.nome);
      }

      this.formulario.get('projectActivity.caraterExclusivoUuid').patchValue(caraterPadrao.length > 0 && caraterPadrao[0].uuid);
    }

    this.caraterExclusivoData = caraterPadrao;
    this.caraterExclusivoPadrao = caraterPadrao.length > 0 ? caraterPadrao[0].uuid : null;
    this.caraterExclusivoPertenceData = caraterDiferente;
  }

  public onChange(): void {
    this.formulario.get('projectActivity.caraterExclusivoUuid')?.patchValue(this.caraterExclusivoPadrao);
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('projectActivity').patchValue(data.dados)
        if (this.formulario && this.formulario.get('projectActivity.uuid').value) {
          this.formulario.get('projectActivity.flagCovid19').patchValue(this.formulario.get('projectActivity.flagCovid19').value === 'N' ? false : true)
          this.formulario.get('projectActivity.flagOrcamentoCrianca').patchValue(this.formulario.get('projectActivity.flagOrcamentoCrianca').value === 'N' ? false : true)
          this.formulario.get('projectActivity.dataInclusaoTce').patchValue(moment(this.formulario.get('projectActivity.dataInclusaoTce').value))
          this.formulario.get('projectActivity.caraterExclusivoUuid').patchValue(data.dados.caraterExclusivoUuid.uuid)
          this.formulario.get('projectActivity.faseContaUuid').patchValue(data.dados.faseContaUuid.uuid)
          this.formulario.get('projectActivity.percentualOrcamentoCrianca').patchValue(data.dados.percentualOrcamentoCrianca)
        }
        this.loadSelects()
      })
  }

  public cancelar(retorno: any): void {
    if (!this.modal) {
      this.gravarParametros()
      this.router.navigate([`projeto-atividade`])
    } else {
      this.ref.close(retorno)
    }
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.formulario.get('projectActivity.uuid').value)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Projeto atividade excluído com sucesso.',
              })
              this.cancelar(null)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    const carater = this.caraterExclusivoPertenceData.filter((x: any) => x.uuid === this.formulario.get('projectActivity.caraterExclusivoUuid').value)
    const statusInativo = this.exercicioData.exercicioStatus.nome === "Inativo";
    const statusFechado = this.exercicioData.exercicioStatus.nome === "Fechado";
    const flagOrcamentoCrianca = this.formulario.get('projectActivity.flagOrcamentoCrianca')?.value === true;
    const percentualNulo = this.formulario.get('projectActivity.percentualOrcamentoCrianca')?.value === null;
    const caraterInvalido = carater.length <= 0;
    const ordemInvalida = this.formulario.get('projectActivity.ordem')?.value <= 0;

    if (this.formulario.invalid ||
        statusInativo ||
        statusFechado ||
        (flagOrcamentoCrianca && (percentualNulo || caraterInvalido)) ||
        ordemInvalida) {
      if (statusInativo || statusFechado) {
        this.toastr.send({
          error: true,
          message: 'Só é permitido criar projeto/atividade em fase de planejamento ou execução.',
        })
      } else {
        if (ordemInvalida && this.formulario.get('projectActivity.ordem')?.value !== null) {
          this.toastr.send({
            error: true,
            message: 'O campo ordem deve ser maior que 0.',
          })
        }
        const ordemRequired = this.formulario.get('projectActivity.ordem').errors?.required;
        const dataInclusaoRequired = this.formulario.get('projectActivity.dataInclusaoTce').errors?.required;
        const nomeRequired = this.formulario.get('projectActivity.nome').errors?.required;
        const tipoRequired = this.formulario.get('projectActivity.tipo').errors?.required;
        const orcamentoCriancaInvalido = flagOrcamentoCrianca && (percentualNulo || caraterInvalido);

        if (ordemRequired || dataInclusaoRequired || nomeRequired || tipoRequired || orcamentoCriancaInvalido) {
          this.toastr.send({
            error: true,
            message: 'Preencher os campos obrigatórios.',
          })
        }
      }
      this.formulario.markAllAsTouched()
    } else {
      if (this.formulario.get('projectActivity.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.loading = true
    this.service
      .post(this.getProjetoAtividadeDto())
      .pipe(first(), finalize(() => (this.loading = false)))
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Projeto atividade criado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private atualizar(): void {
    this.loading = true
    this.service
      .put(this.getProjetoAtividadeDto())
      .pipe(first(), finalize(() => (this.loading = false)))
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Projeto atividade atualizado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private getProjetoAtividadeDto(): any {
    const dto = this.formulario.getRawValue()

    dto.projectActivity.percentualOrcamentoCrianca = dto.projectActivity.flagOrcamentoCrianca ? (dto.projectActivity.percentualOrcamentoCrianca) : null;
    dto.projectActivity.flagCovid19 = dto.projectActivity.flagCovid19 === true ? 'S' : 'N';
    dto.projectActivity.flagOrcamentoCrianca = dto.projectActivity.flagOrcamentoCrianca === true ? 'S' : 'N';
    dto.projectActivity.dataInclusaoTce = moment(dto.projectActivity.dataInclusaoTce).format('YYYY-MM-DD');

    return dto.projectActivity
  }
}
