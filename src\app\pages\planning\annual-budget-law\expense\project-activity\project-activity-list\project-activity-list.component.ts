import { Component, OnD<PERSON>roy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { ModalConfirmarComponent } from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogService } from '@nebular/theme'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import { MenuService } from '../../../../../menu.service'
import { ProjectActivityService } from '../project-activity.service'

@Component({
  selector: 'eqp-project-activity-list',
  templateUrl: './project-activity-list.component.html',
  styleUrls: ['./project-activity-list.component.scss'],
})
export class ProjectActivityListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Projeto/Atividade'

  public gridData: any

  private subscription: Subscription
  constructor(
    private service: ProjectActivityService,
    private toastr: ToastrService,
    public router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/projeto-atividade')
  }

  public ngOnInit(): void {
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'projeto_atividade/paginado',
      10,
    )
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Projeto/Atividade'
          item.options.hint = 'Novo projeto/atividade'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`projeto-atividade/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([`projeto-atividade/edit/${uuid}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Projeto/Atividade excluído com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
