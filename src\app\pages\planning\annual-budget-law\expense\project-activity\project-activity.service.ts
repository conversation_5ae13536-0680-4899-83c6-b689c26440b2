import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { UserDataService } from '@guards/services/user-data.service'
import { Observable } from 'rxjs'

import { BaseService } from '../../../../../@common/services/base/base.service'

@Injectable({
  providedIn: 'root',
})
export class ProjectActivityService extends BaseService<any, any> {
  constructor(
    private httpParameter: HttpClient,
    private userService: UserDataService,
    ) {
    super(httpParameter, 'projeto_atividade')
  }

  public getExercicioStatus(): Observable<any> {
    const userData = this.userService.userData
    return this.http.get<any>(`exercicio/${userData.exercicioUuid}`, {})
  }

  public getFaseConta(): Observable<any> {
    return this.http.get<any>('fase_conta/paginado', {})
  }

  public getCaraterExclusivo(): Observable<any> {
    return this.http.get<any>('carater_exclusivo/paginado', {})
  }
}
