<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
        <eqp-breadcrumb></eqp-breadcrumb>
      </div>

    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="financialTransferBudgetForecast">
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" [showSuffixIcon]="true" [primarySuffixIcon]="true"
          [firstSuffixIcon]="'search'" formControlName="entidade" name="Entidade" label="Entidade"
          placeholder="Entidade" disabled (click)="openEntitySearch()" required="true"></eqp-nebular-input>
      </div>
      <div [class]="'col-md-7' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" formControlName="nomeEntidade" name="Nome" label="Nome"
          placeholder="Nome" disabled>
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Fluxo" label="Fluxo" placeholder="Fluxo"
          formControlName="tipoFluxoTransferenciaFinanceiraUuid" [dataSource]="tipoFluxoTransferenciaFinanceiraData"
          valueExpr="uuid" displayExpr="nome" required="true"></eqp-nebular-select>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" [showSuffixIcon]="true" [primarySuffixIcon]="true"
          [firstSuffixIcon]="'search'" formControlName="planoContabil" name="Conta Contábil" label="Conta Contábil"
          placeholder="Conta Contábil" disabled (click)="openAccountPlanSearch()" required="true"></eqp-nebular-input>
      </div>
      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" formControlName="codigoContabil" name="Código" label="Código"
          placeholder="Código" disabled>
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" formControlName="nomeContabil" name="nomeContabil" label="Nome"
          placeholder="Nome" disabled>
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" [shape]="'rectangle'"
          formControlName="vlrPrevisto" name="Valor Previsto" label="Valor Previsto" placeholder="Valor Previsto"
          errorMessage="É obrigatório preencher o Valor Previsto" minlength="1" maxlength="18" required="true" 
          [style]="'currency' "
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-left" >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">Voltar
        </button>
        <button *ngIf="formulario.get('uuid')?.value" type="button" class="btn btn-danger ml-3 float-md-right"
          (click)="remover()">Apagar
        </button>
        <button type="button" [disabled]="formulario.invalid" class="btn btn-success float-md-right"
          (click)="gravar()">Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>