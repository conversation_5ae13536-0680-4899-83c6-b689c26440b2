import { Component, Input, OnDestroy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { AccountPlanSearchComponent } from '@common/dialogs/account-plan-search/account-plan-search.component'
import { EntitySearchComponent } from '@common/dialogs/entity-search/entity-search.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import {
  ModalConfirmarComponent,
} from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'

import {
  ToastrService,
} from '../../../../../@common/services/toastr/toastr.service'
import { MenuService } from '../../../../menu.service'
import { FinancialTransferBudgetForecastService } from '../financial-transfer-budget-forecast.service'

@Component({
  selector: 'eqp-financial-transfer-budget-forecast-form',
  templateUrl: './financial-transfer-budget-forecast-form.component.html',
  styleUrls: ['./financial-transfer-budget-forecast-form.component.scss'],
})
export class FinancialTransferBudgetForecastFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy {
  public loading: boolean = false
  public pageTitle: string = 'Previsão orçamentária da transferência financeira'
  public formulario: FormGroup
  public nomeData: any
  public entidadeData: any
  public tipoFluxoTransferenciaFinanceiraData: any
  public planoContabilData: any
  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<FinancialTransferBudgetForecastFormComponent>

  constructor(
    private formBuilder: FormBuilder,
    private service: FinancialTransferBudgetForecastService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/previsao-orcamentaria-transferencia-financeira/novo')
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  public ngOnDestroy(): void { }

  public getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      financialTransferBudgetForecast: this.formBuilder.group({
        uuid: [''],
        entidade: [''],
        entidadeUuid: [null, Validators.required],
        nomeEntidade: [null],
        tipoFluxoTransferenciaFinanceiraUuid: [null, Validators.required],
        planoContabil: [''],
        planoContabilUuid: ['test-uuid', Validators.required],
        codigoContabil: [null],
        nomeContabil: [null],
        vlrPrevisto: [null, Validators.required],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid && !this.modal) this.buscar(uuid)
      else this.loadSelects()
    })
  }

  private loadSelects(): void {
    this.tipoFluxoTransferenciaFinanceiraData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'tipo_fluxo_transferencia_financeira/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    });
    this.planoContabilData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'plano_contabil/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    });
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('financialTransferBudgetForecast').patchValue(data.dados)
        this.formulario.get('financialTransferBudgetForecast.entidadeUuid').patchValue(data.dados.entidade.uuid)
        this.formulario.get('financialTransferBudgetForecast.nomeEntidade').patchValue(data.dados.entidade.nome)
        this.formulario.get('financialTransferBudgetForecast.entidade').patchValue(data.dados.entidade.codigo)
        this.formulario.get('financialTransferBudgetForecast.tipoFluxoTransferenciaFinanceiraUuid').patchValue(data.dados.tipoFluxoTransferenciaFinanceira.uuid)
        this.formulario.get('financialTransferBudgetForecast.planoContabilUuid').patchValue(data.dados.planoContabil.uuid)
        this.formulario.get('financialTransferBudgetForecast.planoContabil').patchValue(data.dados.planoContabil.codigoReduzido)
        this.formulario.get('financialTransferBudgetForecast.codigoContabil').patchValue(data.dados.planoContabil.codigo)
        this.formulario.get('financialTransferBudgetForecast.nomeContabil').patchValue(data.dados.planoContabil.nome)
        this.loadSelects()
      })
  }

  public cancelar(retorno): void {
    if (!this.modal) {
      this.gravarParametros()
      this.router.navigate([`previsao-orcamentaria-transferencia-financeira`])
    } else {
      this.ref.close(retorno)
    }
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.formulario.get('financialTransferBudgetForecast.uuid').value)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Previsão orçamentária da transferência financeira excluído com sucesso.',
              })
              this.cancelar(null)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      if (this.formulario.get('financialTransferBudgetForecast.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getPrevisaoOrcamentariaFinanceira())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Previsão orçamentária da transferência financeira criado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getPrevisaoOrcamentariaFinanceira())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Previsão orçamentária da transferência financeira atualizado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private getPrevisaoOrcamentariaFinanceira(): any {
    const dto = this.formulario.getRawValue()

    return dto.financialTransferBudgetForecast
  }

  public openAccountPlanSearch(): void {
    const uuid = this.formulario.get('financialTransferBudgetForecast.tipoFluxoTransferenciaFinanceiraUuid').value;
    const data = this.tipoFluxoTransferenciaFinanceiraData._items.filter(x=>x.uuid === uuid);

    const dialogRef = this.dialogService.open(AccountPlanSearchComponent, {
      context: {
        tipoFluxoTransferenciaFinanceiraData: data[0].nome
      }
    })

    dialogRef.onClose.subscribe(aaccountPlan => {
      if (aaccountPlan) {
        this.formulario.get('financialTransferBudgetForecast.planoContabilUuid').patchValue(aaccountPlan.uuid)
        this.formulario.get('financialTransferBudgetForecast.planoContabil').patchValue(aaccountPlan.codigoReduzido)
        this.formulario.get('financialTransferBudgetForecast.codigoContabil').patchValue(aaccountPlan.codigo)
        this.formulario.get('financialTransferBudgetForecast.nomeContabil').patchValue(aaccountPlan.nome)
      }
    })
  }

  public openEntitySearch(): void {
    const dialogRef = this.dialogService.open(EntitySearchComponent, {})

    dialogRef.onClose.subscribe(entity => {
      if (entity) {
        this.formulario.get('financialTransferBudgetForecast.entidadeUuid').patchValue(entity.uuid)
        this.formulario.get('financialTransferBudgetForecast.entidade').patchValue(entity.codigo ? entity.codigo : '*')
        this.formulario.get('financialTransferBudgetForecast.nomeEntidade').patchValue(entity.nome)
      }
    })
  }
}
