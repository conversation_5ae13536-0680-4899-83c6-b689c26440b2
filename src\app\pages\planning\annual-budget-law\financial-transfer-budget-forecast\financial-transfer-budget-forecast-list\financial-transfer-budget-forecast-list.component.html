<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
>
  <ng-container>
    <dx-data-grid
      id="financialTransferBudgetForecastGrid"
      [dataSource]="gridData"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar previsão orçamentária da transferência financeira"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column dataField="entidade.codigo" caption="Entidade"> </dxi-column>

      <dxi-column dataField="entidade.nome" caption="Nome"> </dxi-column>

      <dxi-column
        dataField="tipoFluxoTransferenciaFinanceira.nome"
        caption="Nome do tipo de fluxo"
      >
      </dxi-column>
      <dxi-column dataField="planoContabil.codigo" caption="Contábil">
      </dxi-column>

      <dxi-column
        dataField="vlrPrevisto"
        caption="Valor previsto"
        [format]="{ style: 'currency', currency: 'BRL' }"
      >
      </dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="80"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          title="Alterar"
          (click)="alterar(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          *ngIf="nivelPermissao === 'FULL'"
          title="Remover"
          (click)="remover(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
