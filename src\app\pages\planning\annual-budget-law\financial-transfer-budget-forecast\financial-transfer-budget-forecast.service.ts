import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'

import { BaseService } from '../../../../@common/services/base/base.service'

@Injectable({
  providedIn: 'root',
})
export class FinancialTransferBudgetForecastService extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'previsao_transferencia_financeira')
  }
}
