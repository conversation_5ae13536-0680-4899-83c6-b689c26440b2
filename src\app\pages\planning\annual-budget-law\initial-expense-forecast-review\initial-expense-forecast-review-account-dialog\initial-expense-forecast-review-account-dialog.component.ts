import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { ReviewTypeInterface } from '@pages/planning/shared/interfaces/review-type'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { finalize, take } from 'rxjs/operators'
import { InitialExpenseForecastSourceService } from '../../initial-expense-forecast/services/initial-expense-forecast-source.service'
import { InitialExpenseForecastReviewAccountInterface } from '../interfaces/initial-expense-forecast-review-account'
import { InitialExpenseForecastReviewAccountService } from '../services/initial-expense-forecast-review-account.service'
import { InitialExpenseForecastReviewService } from '../services/initial-expense-forecast-review.service'

@Component({
  selector: 'eqp-initial-expense-forecast-review-account-dialog',
  templateUrl:
    './initial-expense-forecast-review-account-dialog.component.html',
  styleUrls: [
    './initial-expense-forecast-review-account-dialog.component.scss',
  ],
})
export class InitialExpenseForecastReviewAccountDialogComponent
  implements OnInit
{
  pageTitle: string = 'Revisão da previsão inicial da despesa - Conta'
  loading: boolean
  model: FormGroup

  resourceSourceData: DataSource
  reviewTypeData: DataSource

  @Input() parentUuid: string
  @Input() uuid: string

  constructor(
    private service: InitialExpenseForecastReviewAccountService,
    private initialExpenseForecastService: InitialExpenseForecastReviewService,
    private dialogRef: NbDialogRef<InitialExpenseForecastReviewAccountDialogComponent>,
    private builder: FormBuilder,
    private initialExpenseForecastSourceService: InitialExpenseForecastSourceService,
    private toastr: ToastrService,
  ) {}

  codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  expensePlanDisplay(item) {
    return item && `${item.planoDespesa.codigo} - ${item.planoDespesa.nome}`
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.initializeSelectData()
    if (this.uuid) {
      this.loading = true
      this.service
        .getIndividual(this.parentUuid, this.uuid)
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => {
          this.loadForm(res.dados)
        })
    }
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      previsaoInicialDespesaFonte: [undefined, Validators.required],
      tipoRevisao: [undefined, Validators.required],
      valor: [undefined, Validators.required],
      valorEfetivado: [],
      nrControleTce: [],
    })
  }

  private async initializeSelectData() {
    const reviewData = await this.initialExpenseForecastService
      .getSingleData<ReviewTypeInterface[]>('tipo_revisao')
      .toPromise()
    this.reviewTypeData = new DataSource({
      store: {
        data: reviewData?.dados || [],
        type: 'array',
        key: 'uuid',
      },
    })
  }

  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())
      if (!this.uuid) {
        this.loading = true
        this.service
          .post(this.parentUuid, dto)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            res => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Conta associada com sucesso',
              })
              this.dialogRef.close(res.body.dados)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      } else {
        this.loading = true
        this.service
          .put(this.parentUuid, dto, this.uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            res => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Conta atualizada com sucesso',
              })
              this.dialogRef.close(res.body.dados)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  private loadForm(initialData: InitialExpenseForecastReviewAccountInterface) {
    const dto = {
      ...initialData,
      previsaoInicialDespesaFonte:
        initialData.previsaoInicialDespesaFonte,
      tipoRevisao: initialData.tipoRevisao?.uuid,
    }

    this.model.patchValue(dto)
  }

  private prepare(formData: any) {
    const {
      uuid,
      previsaoInicialDespesaFonte,
      tipoRevisao,
      valor,
      valorEfetivado,
      nrControleTce,
    } = formData
    let dto = {
      uuid,
      previsaoInicialDespesaFonte: { uuid: previsaoInicialDespesaFonte?.uuid },
      tipoRevisao: { uuid: tipoRevisao },
      valor,
      valorEfetivado,
      nrControleTce,
    }
    return dto as InitialExpenseForecastReviewAccountInterface
  }
}
