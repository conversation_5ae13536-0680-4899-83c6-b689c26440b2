import { Component, Input, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { filter, finalize, take } from 'rxjs/operators'
import { InitialExpenseForecastReviewAccountDialogComponent } from '../initial-expense-forecast-review-account-dialog/initial-expense-forecast-review-account-dialog.component'
import { InitialExpenseForecastReviewAccountInterface } from '../interfaces/initial-expense-forecast-review-account'
import { InitialExpenseForecastReviewAccountService } from '../services/initial-expense-forecast-review-account.service'
import { InitialExpenseForecastReviewService } from '../services/initial-expense-forecast-review.service'

@Component({
  selector: 'eqp-initial-expense-forecast-review-account-list',
  templateUrl: './initial-expense-forecast-review-account-list.component.html',
  styleUrls: ['./initial-expense-forecast-review-account-list.component.scss'],
})
export class InitialExpenseForecastReviewAccountListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Contas'
  public loading: boolean = false
  private subscription: Subscription

  booleanLookupOptions = [
    {
      text: 'Sim',
      value: 'S',
    },
    {
      text: 'Não',
      value: 'N',
    },
  ]

  currencyFormat = currencyFormat

  dataSource: DataSource<InitialExpenseForecastReviewAccountInterface, string>

  totalAumentativa: number = 0
  totalDiminutiva: number = 0

  @Input() parentUuid: string
  @Input() effectivated: boolean

  constructor(
    private dialogService: NbDialogService,
    private service: InitialExpenseForecastReviewAccountService,
    private initialExpenseForecastservice: InitialExpenseForecastReviewService,
    private toastr: ToastrService,
    public router: Router,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  fetchGrid() {
    this.dataSource = new DataSource({
      store: this.initialExpenseForecastservice.getDataSourceFiltro(
        'uuid',
        `revisao_previsao_inicial_despesa/${this.parentUuid}/conta`,
        10,
      ),
      map: (data) => {
        return {
          ...data,
          valor: +data.valor
        }
      },
      paginate: true,
      pageSize: 10,
    })
    this.dataSource.store().on('loaded', data => {
      this.calculateTotals(data)
    })
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Conta'
          item.options.hint = 'Conta'
          item.options.onClick = () => this.openEditDialogByUuid()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(res => {
      if (res === 'S') {
        this.service
          .delete(this.parentUuid, uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Conta removida com sucesso',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  private openEditDialogByUuid(uuid?: string) {
    if (!this.effectivated) {
      const dialogRef = this.dialogService.open(
        InitialExpenseForecastReviewAccountDialogComponent,
        {
          context: {},
          closeOnEsc: false,
          closeOnBackdropClick: false,
        },
      )
      dialogRef.componentRef.instance.parentUuid = this.parentUuid
      dialogRef.componentRef.instance.uuid = uuid
      dialogRef.onClose.pipe(filter(res => res)).subscribe(res => {
        this.fetchGrid()
      })
    }
  }

  calculateTotals(items: InitialExpenseForecastReviewAccountInterface[]) {
    let aumentativa = 0
    let diminutiva = 0
    items.forEach(item => {
      if (item.tipoRevisao.codigo === 1) aumentativa += item.valor
      if (item.tipoRevisao.codigo === 2) diminutiva += item.valor
    })
    this.totalAumentativa = aumentativa
    this.totalDiminutiva = diminutiva
  }

  edit(uuid: string) {
    this.openEditDialogByUuid(uuid)
  }
}
