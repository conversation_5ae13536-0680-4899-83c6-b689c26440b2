<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [formGroup]="model"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  bottomLeftButtonId="initial-expense-forecast-review-return-button"
  [rightApproveButtonText]="'Gravar'"
  [rightApproveButtonVisible]="!effectivated"
  [rightApproveButtonType]="'primary'"
  [rightApproveButtonId]="'initial-expense-forecast-review-submit-button'"
  [rightApproveButtonDisabled]="!model.valid || model.pristine"
  (rightApproveButtonEmitter)="submit()"
  [rightDenyButtonText]="'Cancelar efetivação'"
  [rightDenyButtonVisible]="effectivated"
  [rightDenyButtonId]="'initial-expense-forecast-review-cancel-effectivation-button'"
  (rightDenyButtonEmitter)="cancelEffectivation()"
  [rightCustomButtonText]="'Efetivar'"
  [rightCustomButtonVisible]="!effectivated && uuid"
  [rightCustomButtonType]="'success'"
  [rightCustomButtonId]="'initial-expense-forecast-review-effectivate-button'"
  [rightCustomButtonDisabled]="effectivated || !model.pristine"
  (rightCustomButtonEmitter)="effectivate()"
  [enableStatusLabel]="uuid"
  [statusLabelType]="effectivated ? 'success' : 'warning'"
  [statusLabelText]="effectivated ? 'Efetivada' : 'Em edição'"
>
  <nb-tabset (changeTab)="onChangeTab($event)">
    <nb-tab tabTitle="Revisão" class="pb-0">
      <div class="row">
        <div class="col col-12 col-md-3">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Número"
            placeholder="Número"
            formControlName="numero"
            type="number"
            [disabled]="true"
          >
          </eqp-nebular-input>
        </div>
        <div class="col col-12 col-md-3">
          <eqp-nebular-input
            [size]="'small'"
            [style]="'date'"
            [shape]="'rectangle'"
            name="Data"
            label="Data*"
            placeholder="Data"
            formControlName="data"
            [disabled]="effectivated"
            ></eqp-nebular-input>
        </div>
        <div class="col col-md-3">
          <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Revisão aumentativa*"
          placeholder="Revisão aumentativa"
          formControlName="vlrRevisaoAumentativa"
          [disabled]="effectivated"
          >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-3">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Revisão diminutiva*"
          placeholder="Revisão diminutiva"
          formControlName="vlrRevisaoDiminutiva"
          [disabled]="effectivated"
          >
        </eqp-nebular-input>
      </div>
    </div>
    </nb-tab>
    <nb-tab tabId="accounts" [disabled]="!uuid" tabTitle="Contas" class="pb-0">
      <ng-container *ngIf="uuid && tabWasTouched('accounts')">
        <eqp-initial-expense-forecast-review-account-list
          [parentUuid]="uuid"
          [effectivated]="effectivated"
        ></eqp-initial-expense-forecast-review-account-list>
      </ng-container>
    </nb-tab>
  </nb-tabset>
</eqp-standard-page>
