import { OperationTypeInterface } from '@pages/planning/shared/interfaces/operation-type'
import { InitialExpenseForecastInterface } from '../../initial-expense-forecast/interfaces/initial-expense-forecast'
import { ResourceSourceInitialForecastInterface } from '../../initial-revenue-forecast/interfaces/resource-source-initial-forecast'

export interface InitialExpenseForecastReviewAccountInterface {
  uuid: string
  previsaoInicialDespesa?: InitialExpenseForecastInterface //[BACK] retornar no response (não existe relação direta na tabela)
  previsaoInicialDespesaFonte: ResourceSourceInitialForecastInterface
  tipoRevisao: OperationTypeInterface
  valor: number
  valorEfetivado: number
  nrControleTce: number
}
