import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseChildService } from '@common/services/base/base-child.service'
import { InitialExpenseForecastReviewAccountInterface } from '../interfaces/initial-expense-forecast-review-account'

@Injectable({
  providedIn: 'root',
})
export class InitialExpenseForecastReviewAccountService extends BaseChildService<InitialExpenseForecastReviewAccountInterface> {
  constructor(protected http: HttpClient) {
    super(http, 'revisao_previsao_inicial_despesa', 'conta')
  }
}
