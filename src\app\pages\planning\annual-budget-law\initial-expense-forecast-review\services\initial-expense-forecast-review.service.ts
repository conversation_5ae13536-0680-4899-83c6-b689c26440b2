import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { InitialExpenseForecastReviewInterface } from '../interfaces/initial-expense-forecast-review'

@Injectable({
  providedIn: 'root',
})
export class InitialExpenseForecastReviewService extends BaseService<
  ResponseDto<InitialExpenseForecastReviewInterface[]>,
  InitialExpenseForecastReviewInterface
> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'revisao_previsao_inicial_despesa')
  }

  effectivate(uuid: string) {
    return this.http.put<ResponseDto<InitialExpenseForecastReviewInterface>>(
      `revisao_previsao_inicial_despesa/${uuid}/efetivar`,
      {},
      {
        observe: 'response',
      },
    )
  }

  cancelEffectivation(uuid: string) {
    return this.http.put<ResponseDto<InitialExpenseForecastReviewInterface>>(
      `revisao_previsao_inicial_despesa/${uuid}/cancelar_efetivacao`,
      {},
      {
        observe: 'response',
      },
    )
  }
}
