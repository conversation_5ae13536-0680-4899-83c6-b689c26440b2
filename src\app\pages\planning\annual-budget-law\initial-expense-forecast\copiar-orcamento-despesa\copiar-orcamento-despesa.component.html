<eqp-nebular-dialog
  [dialogTitle]="'Copiar Orçamento de Despesa'"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonId]="'voltar-copiar-orcamento'"
  [bottomLeftButtonTitle]="'Voltar'"
  (bottomLeftButtonEmitter)="cancelar()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonId]="'confirmar-copiar-orcamento'"
  [rightFirstButtonDisabled]="form.invalid"
  (rightFirstButtonEmitter)="confirmar()"
  [dialogSize]="'large'"
>
  <form [formGroup]="form">
    <nb-card>
      <nb-card-header>Configurações</nb-card-header>
      <nb-card-body>
        <div class="row mb-3">
          <div class="col-md-6 col-sm-12">
            <eqp-nebular-checkbox
              [style]="'basic'"
              name="copiarExercicioAnterior"
              label="Copiar valor do exercício anterior"
              formControlName="exercicioAnterior"
            >
            </eqp-nebular-checkbox>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 col-sm-12">
            <eqp-nebular-input
              [style]="'currency'"
              [size]="'small'"
              [shape]="'rectangle'"
              [options]="{
                prefix: '',
                thousands: '.',
                decimal: ',',
                suffix: ' %'
              }"
              name="correcao"
              label="Aplicar % de correção"
              placeholder=""
              formControlName="correcao"
            >
            </eqp-nebular-input>
            <small class="text-muted">Permitido de 0 a 100%</small>
          </div>
        </div>
      </nb-card-body>
    </nb-card>

    <nb-card>
      <nb-card-header>Natureza de Despesa</nb-card-header>
      <nb-card-body>
        <div class="row mb-3">
          <div class="col col-12">
            <eqp-search-field
              label="Plano de despesa"
              [uri]="'plano_despesa/paginado'"
              [searchColumnsType]="'expensePlanColumns'"
              [dialogTitle]="'Plano de Despesa'"
              formControlName="planoDespesaUuid"
              messageNotFound="Plano de despesa não encontrado."
            ></eqp-search-field>
          </div>
        </div>
        <div class="d-flex flex-row justify-content-center mb-4">
          <eqp-nebular-input
            class="mx-2"
            [style]="'basic'"
            [size]="'small'"
            [type]="'number'"
            [shape]="'rectangle'"
            name="categoria"
            label="Categoria"
            placeholder=""
            formControlName="categoria"
          >
          </eqp-nebular-input>
          <eqp-nebular-input
            class="mx-2"
            [style]="'basic'"
            [size]="'small'"
            [type]="'number'"
            [shape]="'rectangle'"
            name="grupo"
            label="Grupo"
            placeholder=""
            formControlName="grupo"
          >
          </eqp-nebular-input>
          <eqp-nebular-input
            class="mx-2"
            [style]="'basic'"
            [size]="'small'"
            [type]="'number'"
            [shape]="'rectangle'"
            name="modalidade"
            label="Modalidade"
            placeholder=""
            formControlName="modalidade"
          >
          </eqp-nebular-input>
          <eqp-nebular-input
            class="mx-2"
            [style]="'basic'"
            [size]="'small'"
            [type]="'number'"
            [shape]="'rectangle'"
            name="elemento"
            label="Elemento"
            placeholder=""
            formControlName="elemento"
          >
          </eqp-nebular-input>
        </div>
      </nb-card-body>
    </nb-card>

    <nb-card>
      <nb-card-header>Filtros Adicionais</nb-card-header>
      <nb-card-body>
        <div class="row mb-3">
          <div class="col-md-6 col-sm-12">
            <eqp-search-field
              label="Fonte de recurso"
              [uri]="'fonte_recurso_combinacao/paginado'"
              [searchColumnsType]="'codeNameColumns'"
              [dialogTitle]="'Fontes de Recurso'"
              messageNotFound="Fonte de recurso não encontrada."
              formControlName="fonteRecursoUuid"
              [waitingTime]="1000"
              [ascOrder]="true"
            ></eqp-search-field>
          </div>
          <div class="col-md-6 col-sm-12">
            <eqp-search-field
              label="Órgão"
              [uri]="'orgao/paginado'"
              [searchColumnsType]="'organColumns'"
              [dialogTitle]="'Órgãos'"
              messageNotFound="Órgão não encontrado."
              formControlName="orgaoUuid"
              (ngModelChange)="onOrgaosChange()"
              [waitingTime]="1000"
              [ascOrder]="true"
            ></eqp-search-field>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6 col-sm-12">
            <eqp-search-field
              label="Unidade"
              [uri]="'unidade/paginado'"
              [searchColumnsType]="'unityColumns'"
              [dialogTitle]="'Unidades'"
              [filter]="['orgao','=',form.get('orgaoUuid').value]"
              messageNotFound="Unidade não encontrada."
              [disabled]="!hasOrgaos"
              formControlName="unidadeUuid"
              [waitingTime]="1000"
              [ascOrder]="true"
            ></eqp-search-field>
          </div>
          <div class="col-md-6 col-sm-12">
            <eqp-search-field
              label="Função"
              [uri]="'funcao/paginado'"
              [searchColumnsType]="'functionColumns'"
              [dialogTitle]="'Funções'"
              messageNotFound="Função não encontrada."
              formControlName="funcaoUuid"
              [waitingTime]="1000"
              [ascOrder]="true"
            ></eqp-search-field>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6 col-sm-12">
            <eqp-search-field
              label="Subfunção"
              [uri]="'subfuncao/paginado'"
              [searchColumnsType]="'subfunctionColumns'"
              [dialogTitle]="'Subfunções'"
              messageNotFound="Subfunção não encontrada."
              formControlName="subfuncaoUuid"
              [waitingTime]="1000"
              [ascOrder]="true"
            ></eqp-search-field>
          </div>
          <div class="col-md-6 col-sm-12">
            <eqp-nebular-search-field
              label="Programa"
              formControlName="programa"
              (onButtonClick)="onProgramSearchDialog()"
              (onInputChange)="onProgramSearchInput($event)"
              [waitingTime]="1000"
            >
            </eqp-nebular-search-field>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6 col-sm-12">
            <eqp-search-field
              label="Projeto/Atividade"
              [uri]="'projeto_atividade/paginado'"
              [searchColumnsType]="'projectActivityColumns'"
              [dialogTitle]="'Projetos/Atividades'"
              messageNotFound="Projeto/Atividade não encontrado."
              formControlName="projetoAtividadeUuid"
              codeKey="ordem"
              [waitingTime]="1000"
              [ascOrder]="true"
            ></eqp-search-field>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </form>
</eqp-nebular-dialog>
