import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { ProgramSearchComponent } from '@pages/planning/shared/searchs/program-search/program-search.component'
import { Subject } from 'rxjs'
import { filter, finalize, map, take, takeUntil } from 'rxjs/operators'
import { CopiarOrcamentoDespesaService } from './copiar-orcamento-despesa.service'

@Component({
  selector: 'eqp-copiar-orcamento-despesa',
  templateUrl: './copiar-orcamento-despesa.component.html',
  styleUrls: ['./copiar-orcamento-despesa.component.scss'],
})
export class CopiarOrcamentoDespesaComponent implements OnInit, OnDestroy {
  public loading = false
  public form: FormGroup

  // Controle de unidades
  public unidadesUri = 'unidade/paginado'
  public hasOrgaos = false

  private destroy$ = new Subject<void>()

  constructor(
    private formBuilder: FormBuilder,
    private service: CopiarOrcamentoDespesaService,
    private toastr: ToastrService,
    protected dialogRef: NbDialogRef<CopiarOrcamentoDespesaComponent>,
    private dialogService: NbDialogService,
    private crudService: CrudService
  ) {}

  ngOnInit(): void {
    this.createForm()
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  private createForm(): void {
    this.form = this.formBuilder.group({
      exercicioAnterior: [false],
      correcao: [0, [Validators.min(0), Validators.max(100)]],
      categoria: ['', [Validators.min(0), Validators.max(9)]],
      grupo: ['', [Validators.min(0), Validators.max(9)]],
      modalidade: ['', [Validators.min(0), Validators.max(99)]],
      elemento: ['', [Validators.min(0), Validators.max(99)]],
      fonteRecursoUuid: [null],
      orgaoUuid: [null],
      unidadeUuid: [null],
      funcaoUuid: [null],
      subfuncaoUuid: [null],
      programa: [null],
      projetoAtividadeUuid: [null],
      planoDespesaUuid: [null],
    })
  }

  public onOrgaosChange(): void {
    const orgaoUuid = this.form.get('orgaoUuid').value
    this.hasOrgaos = orgaoUuid
    this.form.get('unidadeUuid').reset()
  }

  onProgramSearchDialog() {
    const ref = this.dialogService.open(ProgramSearchComponent, {
      context: {
        uri: 'previsao_inicial_despesa/ppa_programa_exercicio_atual',
        paginated: false,
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.form.get('programa').patchValue(res)
      })
  }

  onProgramSearchInput(number: string) {
    if (!number || number == '' || !/^\d+$/.test(number)) {
      this.form.get('programa').reset()
      return
    }
    this.loading = true
    this.crudService
      .getSingleData(
        `previsao_inicial_despesa/ppa_programa_exercicio_atual?codigo=${number}`,
      )
      .pipe(
        take(1),
        map(res => res.dados),
        finalize(() => (this.loading = false)),
      )
      .subscribe((res: any[]) => {
        if (res.length == 0) {
          this.toastr.send({
            error: true,
            message: `Programa não encontrado.`,
          })
          this.form.get('programa').reset()
        } else {
          this.form.get('programa').patchValue(res[0])
        }
        err => {
          this.loading = false
          this.toastr.send({
            error: true,
            message: `${err}`,
          })
        }
      })
  }

  public confirmar(): void {
    if (this.form.invalid) {
      return
    }

    // Exibir mensagem de confirmação usando o ConfirmationComponent
    const dialogRef = this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          body: 'Atenção: os valores atuais do exercício serão atualizados. Deseja continuar?',
          cancelText: 'Não',
          cancelTitle: 'Não',
          confirmText: 'Sim',
          confirmTitle: 'Sim',
        },
        dialogSize: 'medium',
        exiberIcones: false,
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })

    dialogRef.onClose
      .pipe(
        filter(result => result === true),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.loading = true

        const dto = this.prepareDto()

        this.service
          .copiarOrcamentoDespesa(dto)
          .pipe(
            takeUntil(this.destroy$),
            finalize(() => (this.loading = false)),
          )
          .subscribe({
            next: () => {
              this.toastr.send({
                success: true,
                message: 'Orçamento de despesa copiado com sucesso.',
              })
              this.dialogRef.close(true)
            },
          })
      })
  }

  private prepareDto(): any {
    const formValue = this.form.getRawValue()

    return {
      ...formValue,
      programaUuid: formValue.programa ? formValue.programa.uuid : null,
      exercicioAnterior: formValue.exercicioAnterior,
      correcao: formValue.correcao,
      categoria: formValue.categoria,
      grupo: formValue.grupo,
      modalidade: formValue.modalidade,
      elemento: formValue.elemento,
    }
  }

  public cancelar(): void {
    this.dialogRef.close(false)
  }
}
