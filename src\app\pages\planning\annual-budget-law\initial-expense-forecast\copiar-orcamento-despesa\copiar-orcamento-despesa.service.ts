import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CopiarOrcamentoDespesaService {
  constructor(private http: HttpClient) { }

  // Métodos de obtenção de categorias, grupos, modalidades e elementos foram removidos
  // pois agora são campos numéricos

  /**
   * Copia o orçamento de despesa
   * @param dto Dados para cópia do orçamento
   */
  public copiarOrcamentoDespesa(dto: any): Observable<any> {
    return this.http.put<any>('previsao_inicial_despesa/copiar_valores_orcamento', dto);
  }

}
