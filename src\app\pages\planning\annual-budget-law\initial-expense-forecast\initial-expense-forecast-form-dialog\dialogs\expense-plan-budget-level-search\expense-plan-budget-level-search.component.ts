import { Component, Input, OnInit } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'

@Component({
  selector: 'eqp-expense-plan-budget-level-search',
  templateUrl: './expense-plan-budget-level-search.component.html',
  styleUrls: ['./expense-plan-budget-level-search.component.scss'],
})
export class ExpensePlanBudgetLevelSearchComponent implements OnInit {
  public loading: boolean = false

  @Input() public pageTitle = 'Plano despesa | Busca'

  @Input() public isMultiple = false
  @Input() public filter: string
  @Input() public uri =
    'previsao_inicial_despesa/plano_despesa_por_nivel_orcamentario'

  public selected: any
  public dataSource: DataSource
  private subscription: Subscription

  public exerciseData: any[]
  public loggedExercise

  exercicioUuid: string

  constructor(
    protected dialogRef: NbDialogRef<ExpensePlanBudgetLevelSearchComponent>,
    private crudService: CrudService,
  ) {}

  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  private async fetchGrid() {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro('uuid', this.uri, 10),
      paginate: true,
      pageSize: 10,
    })
  }

  public onSelectionChanged(event: any) {
    this.selected = event.selectedRowsData
  }

  public async confirm() {
    if (this.selected) {
      this.dialogRef.close(this.selected)
    }
  }

  public cancel(): void {
    this.dialogRef.close(false)
  }
}
