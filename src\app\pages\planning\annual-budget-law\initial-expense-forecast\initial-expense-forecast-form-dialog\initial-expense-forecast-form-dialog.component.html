<nb-card style="width: 90vw;" [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col col-md-4">
        <eqp-nebular-search-field
          label="Projeto ou atividade *"
          codeKey="ordem"
          codeLabel="Ordem"
          formControlName="projetoAtividade"
          [disabled]="uuid"
          (onButtonClick)="onProjectActivityDialog()"
          (onInputChange)="onSearchInput($event, 'projeto_atividade/paginado', 'projetoAtividade', 'ordem', 'Projeto ou atividade')"
        >
        </eqp-nebular-search-field>
      </div>
      <div class="col col-md-5">
        <eqp-nebular-search-field
          label="Órgão *"
          formControlName="orgao"
          [disabled]="uuid"
          (onButtonClick)="onOrganSearchDialog()"
          (onInputChange)="onSearchInput($event, 'orgao/paginado', 'orgao', 'codigo', 'Orgão')"
        >
        </eqp-nebular-search-field>
      </div>
      <div class="col col-md-3">
        <eqp-nebular-search-field
          label="Unidade *"
          formControlName="unidade"
          [disabled]="!model.get('orgao')?.value?.uuid || uuid"
          (onButtonClick)="onUnityearchDialog()"
          (onInputChange)="onUnitySearchInput($event)"
        >
        </eqp-nebular-search-field>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col col-md-4">
        <eqp-nebular-search-field
          label="Função *"
          formControlName="funcao"
          [disabled]="uuid"
          (onButtonClick)="onFunctionSearchDialog()"
          (onInputChange)="onSearchInput($event, 'funcao/paginado', 'funcao', 'codigo', 'Função')"
        >
        </eqp-nebular-search-field>
      </div>
      <div class="col col-md-4">
        <eqp-nebular-search-field
          label="Subfunção *"
          formControlName="subfuncao"
          [disabled]="uuid"
          (onButtonClick)="onSubFunctionSearchDialog()"
          (onInputChange)="onSearchInput($event, 'subfuncao/paginado', 'subfuncao', 'codigo', 'Subfunção')"
        >
        </eqp-nebular-search-field>
      </div>
      <div class="col col-md-4">
        <eqp-nebular-search-field
          label="Programa *"
          formControlName="programa"
          [disabled]="uuid"
          (onButtonClick)="onProgramSearchDialog()"
          (onInputChange)="onProgramSearchInput($event)"
        >
        </eqp-nebular-search-field>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col col-md-3">
        <label class="label">Natureza da despesa*</label>
        <nb-form-field>
          <input
            #input
            [type]="'text'"
            [fullWidth]="true"
            placeholder="Código"
            [fieldSize]="'small'"
            formControlName="planoDespesa"
            [readonly]="uuid"
            nbInput
          />
          <button
            [size]="'small'"
            [title]="'Buscar'"
            nbSuffix
            nbButton
            ghost
            [disabled]="disableButton"
            (click)="onExpensePlanSearchDialog()"
          >
            <nb-icon icon="search" pack="eva"></nb-icon>
          </button>
        </nb-form-field>
      </div>
      <div class="col col-md-5">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="planoDespesaDescricao"
          label="Descrição"
          placeholder="Nome"
          [disabled]="uuid"
          [readonly]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-md-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo*"
          placeholder="Tipo"
          formControlName="tipoCreditoAdicional"
          [dataSource]="additionalCreditTypeData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
          [disabled]="uuid"
        ></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          *ngIf="!uuid"
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="!model.valid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
