import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { formatIntDigits } from '@pages/planning/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { from, of, Subject, Subscription } from 'rxjs'
import {
  distinctUntilChanged,
  take,
  finalize,
  first,
  takeUntil,
  debounceTime,
  filter,
  map,
  switchMap,
  catchError,
} from 'rxjs/operators'
import { InitialExpenseForecastInterface } from '../interfaces/initial-expense-forecast'
import { InitialExpenseForecastService } from './../services/initial-expense-forecast.service'
import { ExpensePlanBudgetLevelSearchComponent } from './dialogs/expense-plan-budget-level-search/expense-plan-budget-level-search.component'
import { ExpensePlanInterface } from '@pages/planning/multi-year-plan/interfaces/expense-plan'
import { ProjectActivitySearchComponent } from '@pages/planning/shared/searchs/project-activity-search/project-activity-search.component'
import { OrganSearchComponent } from '@pages/planning/shared/searchs/organ-search/organ-search.component'
import { UnitySearchComponent } from '@pages/planning/shared/searchs/unity-search/unity-search.component'
import { FunctionSearchComponent } from '@pages/planning/shared/searchs/function-search/function-search.component'
import { ProgramSearchComponent } from '@pages/planning/shared/searchs/program-search/program-search.component'

@Component({
  selector: 'eqp-initial-expense-forecast-form-dialog',
  templateUrl: './initial-expense-forecast-form-dialog.component.html',
  styleUrls: ['./initial-expense-forecast-form-dialog.component.scss'],
})
export class InitialExpenseForecastFormDialogComponent
  implements OnInit, OnDestroy
{
  validPhases = [
    {
      fase: 'Planejamento',
    },
    {
      fase: 'Execução',
    },
  ]

  pageTitle: string = 'Previsão inicial da despesa'
  loading: boolean
  model: FormGroup

  showUnitityName: boolean = false
  disableButton: boolean = false
  disableProjectActivity: boolean = false

  projectActivityData: DataSource
  organData: DataSource
  unityData: DataSource
  functionData: DataSource
  subfunctionData: DataSource
  programData: DataSource
  expensePlanData: DataSource
  additionalCreditTypeData: DataSource

  organSubscription: Subscription
  typeSubscription: Subscription

  public exercicioData: any

  private unsub$ = new Subject<null>()

  @Input() initialData: InitialExpenseForecastInterface

  constructor(
    private service: InitialExpenseForecastService,
    private dialogRef: NbDialogRef<InitialExpenseForecastFormDialogComponent>,
    private builder: FormBuilder,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
  ) {}

  get uuid() {
    return this.model.get('uuid')?.value
  }

  projectActivityDisplay(item) {
    return (
      item && `${item.tipo}${formatIntDigits(item.ordem, 3)} - ${item.nome}`
    )
  }

  codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.showUnitityName = false
    this.disableButton = false

    if (this.initialData) {
      this.showUnitityName = true
      this.disableButton = true
      this.loadForm(this.initialData)
    } else {
      this.initializeSelectData()
      this.initializeHandlers()
    }
  }

  ngOnDestroy(): void {
    if (this.organSubscription) this.organSubscription.unsubscribe()
    this.unsub$.next()
    this.unsub$.complete()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      projetoAtividade: [undefined, Validators.required],
      orgao: [undefined, Validators.required],
      unidade: [undefined, Validators.required],
      funcao: [undefined, Validators.required],
      subfuncao: [undefined, Validators.required],
      programa: [undefined, Validators.required],
      planoDespesa: [undefined],
      planoDespesaUuid: [undefined, Validators.required],
      planoDespesaDescricao: [''],
      tipoCreditoAdicional: [undefined, Validators.required],
    })
  }

  validateProjectActivity(uuid: string) {
    this.projectActivityData
      .store()
      .byKey(uuid)
      .then(res => {
        if (res.flagOrcamentoCrianca == 'S') {
          this.disableProjectActivity = false
        } else {
          this.disableProjectActivity = true
        }
      })
  }

  initializeHandlers() {
    this.model
      .get('planoDespesa')
      .valueChanges.pipe(
        takeUntil(this.unsub$),
        debounceTime(3000),
        distinctUntilChanged(),
      )
      .subscribe(res => {
        this.onExpensePlanSearchInput(res)
      })
  }

  onExpensePlanSearchInput(number: any) {
    if (!number || number == '') {
      this.limparCamposPlanoDespesa()
      return
    }
    const req$ = this.service
      .getDataSourceFiltro(
        'uuid',
        'previsao_inicial_despesa/plano_despesa_por_nivel_orcamentario',
        10,
        'codigo',
        `${number}`,
      )
      .load()

    this.loading = true
    from(req$)
      .pipe(
        switchMap((res: ExpensePlanInterface[]) => {
          if (res.length === 0) {
            this.mensagemPlanoDespesaNaoEncontrado()
            this.limparCamposPlanoDespesa()
            return of({dados: null})
          }
          return this.validarPlanoDespesaNivel(res[0]).pipe(
            map((planoDespesa) => ({dados: planoDespesa })),
          )
        }),
        filter((res) => res.dados),
        finalize(() => (this.loading = false)),
      )
      .subscribe((res: {dados: ExpensePlanInterface | null}) => {
        this.expensePlanInfo(res.dados)
      })
  }

  private mensagemPlanoDespesaNaoEncontrado() {
    this.toastr.send({
      error: true,
      message: 'Plano despesa não encontrado.',
    })
  }

  private limparCamposPlanoDespesa(): void {
    this.model.get('planoDespesaDescricao').reset();
    this.model.get('planoDespesaUuid').reset();
    this.model.get('planoDespesa').reset();
  }

  private validarPlanoDespesaNivel(planoDespesa: ExpensePlanInterface) {
    return this.service.validarDespesaPorNivel(planoDespesa.uuid).pipe(
      map(({ dados }) => {
        if (!dados.trim()) {
          return planoDespesa
        }

        this.toastr.send({
          error: true,
          message: dados,
        })
        return null
      }),
    )
  }

  onExpensePlanSearchDialog() {
    const ref = this.dialogService.open(ExpensePlanBudgetLevelSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    ref.onClose
      .pipe(
        switchMap(res =>
          this.validarPlanoDespesaNivel(res[0])
        ),
        filter(res => !!res),
        first(),
      )
      .subscribe(res => {
        console.log(res)
        this.expensePlanInfo(res)
      })
  }

  expensePlanInfo(data: ExpensePlanInterface) {
    this.model.get('planoDespesaDescricao').patchValue(`${data.nome}`)
    this.model.get('planoDespesa').patchValue(`${data.codigo}`, {
      emitEvent: false,
    })
    this.model.get('planoDespesaUuid').patchValue(`${data.uuid}`)
  }

  private async initializeSelectData() {
    const additionalCreditType = await this.service
      .customGetSingleData<any>(
        `previsao_inicial_despesa/tipo_credito_adicional`,
      )
      .toPromise()
    this.additionalCreditTypeData = new DataSource({
      store: {
        data: additionalCreditType?.data || [],
        type: 'array',
        key: 'uuid',
      },
    })
  }

  confirm() {
    if (this.model.valid) {
      if (!this.uuid) {
        this.create()
      } else {
        this.update()
      }
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  private loadForm(initialData: InitialExpenseForecastInterface) {
    this.additionalCreditTypeData = new DataSource([
      initialData.tipoCreditoAdicional,
    ])
    const dto = {
      ...initialData,
      projetoAtividade: initialData.projetoAtividade
        ? initialData.projetoAtividade
        : null,
      orgao: initialData.orgao ? initialData.orgao : null,
      unidade: initialData.unidade ? initialData.unidade : null,
      funcao: initialData.funcao ? initialData.funcao : null,
      subfuncao: initialData.subfuncao ? initialData.subfuncao : null,
      programa: initialData.programa ? initialData.programa : null,
      planoDespesaUuid: initialData.planoDespesa
        ? initialData.planoDespesa?.uuid
        : null,
      planoDespesa: initialData.planoDespesa
        ? initialData.planoDespesa.codigo
        : null,
      planoDespesaDescricao: initialData.planoDespesa
        ? initialData.planoDespesa.nome
        : null,
      tipoCreditoAdicional: initialData.tipoCreditoAdicional
        ? initialData.tipoCreditoAdicional?.uuid
        : null,
    }
    this.model.patchValue(dto, { emitEvent: false })
  }

  private prepare(formData: any) {
    let dto = {
      ...formData,
      projetoAtividade: formData.projetoAtividade
        ? { uuid: formData.projetoAtividade.uuid }
        : null,
      orgao: formData.orgao ? { uuid: formData.orgao.uuid } : null,
      unidade: formData.unidade ? { uuid: formData.unidade.uuid } : null,
      funcao: formData.funcao ? { uuid: formData.funcao.uuid } : null,
      subfuncao: formData.subfuncao ? { uuid: formData.subfuncao.uuid } : null,
      programa: formData.programa ? { uuid: formData.programa.uuid } : null,
      planoDespesa: formData.planoDespesaUuid
        ? { uuid: formData.planoDespesaUuid }
        : null,
      tipoCreditoAdicional: formData.tipoCreditoAdicional
        ? { uuid: formData.tipoCreditoAdicional }
        : null,
    }
    return dto
  }

  create() {
    this.loading = true
    this.service
      .post(this.prepare(this.model.getRawValue()))
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.toastr.send({
            title: 'Sucesso',
            success: true,
            message: this.pageTitle + ' cadastrado(a) com sucesso',
          })
          this.dialogRef.close(res.body.dados)
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  update() {
    this.loading = true
    this.service
      .put(this.prepare(this.model.getRawValue()))
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.toastr.send({
            title: 'Sucesso',
            success: true,
            message: this.pageTitle + ' atualizado(a) com sucesso',
          })
          this.dialogRef.close(res.body.dados)
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  onProjectActivityDialog() {
    const ref = this.dialogService.open(ProjectActivitySearchComponent, {
      context: {
        uri: 'projeto_atividade/paginado',
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.model.get('projetoAtividade').patchValue(res)
      })
  }

  onOrganSearchDialog() {
    const ref = this.dialogService.open(OrganSearchComponent, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.model.get('orgao').patchValue(res)
      })
  }

  onUnityearchDialog() {
    const organUuid = this.model.get('orgao')?.value?.uuid
    const ref = this.dialogService.open(UnitySearchComponent, {
      context: {
        filter: ['organUuid', '=', `${organUuid}`],
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.model.get('unidade').patchValue(res)
      })
  }

  onFunctionSearchDialog() {
    const ref = this.dialogService.open(FunctionSearchComponent, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.model.get('funcao').patchValue(res)
      })
  }

  onSubFunctionSearchDialog() {
    const ref = this.dialogService.open(FunctionSearchComponent, {
      context: {
        uri: 'subfuncao/paginado',
        pageTitle: 'Selecionar subfunção',
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.model.get('subfuncao').patchValue(res)
      })
  }

  onProgramSearchDialog() {
    const ref = this.dialogService.open(ProgramSearchComponent, {
      context: {
        uri: 'previsao_inicial_despesa/ppa_programa_exercicio_atual',
        paginated: false,
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.model.get('programa').patchValue(res)
      })
  }

  onSearchInput(
    number: any,
    uri: string,
    field: string,
    key: string,
    title: string,
  ) {
    if (!number || number == '' || !/^\d+$/.test(number)) {
      this.model.get(field).reset()
      return
    }
    this.loading = true
    this.service
      .getDataSourceFiltro('uuid', uri, 10, key, `${number}`)
      .load()
      .then(res => {
        this.loading = false
        if (res.length == 0) {
          this.toastr.send({
            error: true,
            message: `${title} não encontrado(a).`,
          })
          this.model.get(field).reset()
        } else {
          this.model.get(field).patchValue(res[0])
        }
        err => {
          this.loading = false
          this.toastr.send({
            error: true,
            message: `${err}`,
          })
        }
      })
  }

  onProgramSearchInput(number) {
    if (!number || number == '' || !/^\d+$/.test(number)) {
      this.model.get('programa').reset()
      return
    }
    this.loading = true
    this.service
      .getSingleData(
        `previsao_inicial_despesa/ppa_programa_exercicio_atual?codigo=${number}`,
      )
      .pipe(
        take(1),
        map(res => res.dados),
        finalize(() => (this.loading = false)),
      )
      .subscribe((res: any[]) => {
        if (res.length == 0) {
          this.toastr.send({
            error: true,
            message: `Programa não encontrado(a).`,
          })
          this.model.get('programa').reset()
        } else {
          this.model.get('programa').patchValue(res[0])
        }
        err => {
          this.loading = false
          this.toastr.send({
            error: true,
            message: `${err}`,
          })
        }
      })
  }

  onUnitySearchInput(number) {
    const organUuid = this.model.get('orgao')?.value.uuid
    if (!number || number == '' || !/^\d+$/.test(number)) {
      this.model.get('unidade').reset()
      return
    }
    this.loading = true
    this.service
      .getDataSourceFiltroComposto(
        'uuid',
        'unidade/paginado',
        10,
        `[["codigo","=",${number}],"and",["organUuid","=","${organUuid}""]]`,
      )
      .load()
      .then(res => {
        this.loading = false
        if (res.length == 0) {
          this.toastr.send({
            error: true,
            message: `Unidade não encontrada.`,
          })
          this.model.get('unidade').reset()
        } else {
          this.model.get('unidade').patchValue(res[0])
        }
        err => {
          this.loading = false
          this.toastr.send({
            error: true,
            message: `${err}`,
          })
        }
      })
  }
}
