<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancelar()"
  bottomLeftButtonId="initial-expense-forecast-return-button"
>
  <div class="row">
    <div class="col col-md-6">
      <label class="label">Projeto atividade</label>
      <input
        type="text"
        nbInput
        fullWidth
        fieldSize="small"
        placeholder="00,00"
        disabled
        class="text-right"
        [value]="
          data?.projetoAtividade?.tipo + '' + (data?.projetoAtividade?.tipo | number:'3.0')
        "
      />
    </div>
    <div class="col col-md-6">
      <label class="label">&nbsp;</label>
      <input
        type="text"
        nbInput
        fullWidth
        fieldSize="small"
        placeholder="00,00"
        disabled
        class="text-right"
        [value]="data?.projetoAtividade?.nome"
      />
    </div>
  </div>
  <div class="mt-3">
    <nb-tabset>
      <nb-tab
        [disabled]="!uuid"
        tabTitle="Fontes recurso"
        tabId="resource-source"
        class="p-0"
      >
        <eqp-initial-expense-forecast-source-list
          *ngIf="data"
          [parentUuid]="uuid"
        ></eqp-initial-expense-forecast-source-list>
      </nb-tab>
      <!-- <nb-tab
        tabTitle="Classificação contábil"
        tabId="accounting-classification"
      >
      </nb-tab> -->
    </nb-tabset>
  </div>
</eqp-standard-page>
