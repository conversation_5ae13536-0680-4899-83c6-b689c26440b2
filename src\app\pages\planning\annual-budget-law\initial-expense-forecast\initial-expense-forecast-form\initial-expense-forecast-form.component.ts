import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { forkJoin } from 'rxjs'
import { finalize, first, take } from 'rxjs/operators'
import { InitialExpenseForecastInterface } from '../interfaces/initial-expense-forecast'
import { InitialExpenseForecastService } from '../services/initial-expense-forecast.service'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { MenuService } from '@pages/menu.service'

@Component({
  selector: 'eqp-initial-expense-forecast-form',
  templateUrl: './initial-expense-forecast-form.component.html',
  styleUrls: ['./initial-expense-forecast-form.component.scss'],
})
export class InitialExpenseForecastFormComponent extends BaseTelasComponent implements OnInit {
  public pageTitle: string = 'Previsão inicial da despesa'
  public loading: boolean = false
  public columns: DxColumnInterface[] = []

  data: InitialExpenseForecastInterface
  uuid: string

  constructor(
    private toastr: ToastrService,
    public menuService: MenuService,
    private service: InitialExpenseForecastService,
    private route: ActivatedRoute,
    public router: Router,
  ) {
    super(menuService, router)
  }

  ngOnInit(): void {
    const { uuid } = this.route.snapshot.params
    this.uuid = uuid
    this.loadPageData()
  }

  loadPageData() {
    this.loading = true
    forkJoin([this.service.getIndividual(this.uuid)])
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        ([expenseForecast]) => {
          this.data = expenseForecast.dados
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  cancelar() {
    this.gravarParametros()
    this.router.navigate([
      'lei-orcamentaria-anual',
      'despesa',
      'previsao-inicial',
    ])
  }
}
