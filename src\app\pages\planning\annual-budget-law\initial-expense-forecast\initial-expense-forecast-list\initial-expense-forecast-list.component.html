<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update-initial-expense-forecast-list'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="fetchGrid()"
>
  <ng-container>
    <div class="d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center" style="gap: 0.5rem">
        <button
          nbButton
          status="primary"
          (click)="calculateAllDisbursementSchedule()"
          title="Aplicar cronograma desembolso em todas as contas"
        >
          <nb-icon pack="fas" icon="calculator"></nb-icon>
        </button>
        <button
          nbButton
          status="primary"
          (click)="initialExpenseForecastSourceSearch()"
          title="Pesquisa previsão inicial da despesa orçamentária"
        >
          <nb-icon pack="fas" icon="search"></nb-icon>
        </button>
        <eqp-field-toggle
          label="Somente contas com valor autorizado"
          [formControl]="toggleValorPrevisto"
        ></eqp-field-toggle>
      </div>
      <div>
        <button
          class="btn btn-outline-primary"
          title="Copiar orçamento de despesa"
          (click)="abrirModalCopiarOrcamento()"
        >
          <i class="fas fa-copy mr-1"></i> Copiar orçamento
        </button>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col col-12">
        <dx-data-grid
          id="initialExpenseForecastGrid"
          [dataSource]="dataSource"
          [columnAutoWidth]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [rowAlternationEnabled]="true"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          [columnHidingEnabled]="false"
          [remoteOperations]="true"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparing($event)"
          [remoteOperations]="true"
        >
          <dxo-state-storing
            [enabled]="true"
            type="custom"
            [customLoad]="loadState"
            [customSave]="saveState"
            savingTimeout="100"
          ></dxo-state-storing>

          <dxo-paging [pageSize]="10"></dxo-paging>
          <dxo-pager
            [showInfo]="true"
            [showNavigationButtons]="true"
            [showPageSizeSelector]="false"
          >
          </dxo-pager>

          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

          <dxo-group-panel
            [visible]="false"
            [emptyPanelText]="''"
          ></dxo-group-panel>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar previsão"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            dataField="orgao.codigo"
            caption="Orgão"
            dataType="number"
            [format]="{ type: 'decimal', precision: 2 }"
            alignment="left"
          ></dxi-column>
          <dxi-column
            dataField="unidade.codigo"
            caption="Unidade"
            dataType="number"
            [format]="{ type: 'decimal', precision: 3 }"
            alignment="left"
          ></dxi-column>
          <dxi-column
            dataField="funcao.codigo"
            caption="Função"
            dataType="number"
            [format]="{ type: 'decimal', precision: 2 }"
            alignment="left"
          ></dxi-column>
          <dxi-column
            dataField="subfuncao.codigo"
            caption="Subfunção"
            dataType="number"
            [format]="{ type: 'decimal', precision: 3 }"
            alignment="left"
          ></dxi-column>
          <dxi-column
            dataField="programa.codigo"
            caption="Programa"
            dataType="number"
            [format]="{ type: 'decimal', precision: 4 }"
            alignment="left"
          ></dxi-column>

          <dxi-column
            alignment="left"
            dataField="projetoAtividade.tipo"
            caption="Tipo"
          >
          </dxi-column>
          <dxi-column
            alignment="left"
            dataField="projetoAtividade.ordem"
            caption="Ordem"
            [calculateFilterExpression]="calculateFilterExpression"
          >
          </dxi-column>
          <dxi-column
            alignment="left"
            dataField="projetoAtividade.nome"
            caption="Projeto atividade"
          >
          </dxi-column>

          <dxi-column
            dataField="planoDespesa.codigo"
            caption="Natureza da despesa"
          ></dxi-column>

          <dxi-column dataField="planoDespesa.nome" caption="Nome"></dxi-column>

          <dxi-column
            dataField="valorAutorizado"
            caption="Total autorizado"
            [editorOptions]="{ placeholder: '00' }"
            [format]="currencyFormat"
            [allowFiltering]="false"
            [allowSorting]="false"
            [allowSearch]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoCreditoAdicional.nome"
            caption="Tipo"
          ></dxi-column>

          <dxi-column
            dataField="uuid"
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <div class="w-100 d-flex justify-content-end">
              <a
                title="Dados relacionados"
                (click)="openRelationData(data.value)"
                class="dx-link dx-link-edit fas fa-folder dx-link-icon btn-icon-grid"
              >
              </a>
              <a
                title="Alterar"
                (click)="edit(data.value)"
                class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
              >
              </a>
              <a
                *ngIf="nivelPermissao === 'FULL'"
                title="Remover"
                (click)="remove(data.value)"
                class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
              >
              </a>
            </div>
          </div>
        </dx-data-grid>
      </div>
    </div>
  </ng-container>
</eqp-standard-page>
