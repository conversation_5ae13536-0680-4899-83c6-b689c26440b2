import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { Subject, Subscription } from 'rxjs'
import {
  filter,
  finalize,
  first,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs/operators'
import { InitialExpenseForecastFormDialogComponent } from '../initial-expense-forecast-form-dialog/initial-expense-forecast-form-dialog.component'
import { InitialExpenseForecastInterface } from '../interfaces/initial-expense-forecast'
import { InitialExpenseForecastService } from '../services/initial-expense-forecast.service'
import { InitialExpenseForecastSourceSearchComponent } from '../initial-expense-forecast-source-search/initial-expense-forecast-source-search.component'
import { FormControl } from '@angular/forms'

@Component({
  selector: 'eqp-initial-expense-forecast-list',
  templateUrl: './initial-expense-forecast-list.component.html',
  styleUrls: ['./initial-expense-forecast-list.component.scss'],
})
export class InitialExpenseForecastListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle: string = 'Previsão inicial da despesa'
  public loading: boolean = false
  public dataSource: DataSource
  public columns: DxColumnInterface[] = []
  public toggleValorPrevisto = new FormControl('N')

  currencyFormat = currencyFormat

  private subscription: Subscription
  private destroy$ = new Subject<void>()

  booleanLookupOptions = [
    {
      text: 'Sim',
      value: 'S',
    },
    {
      text: 'Não',
      value: 'N',
    },
  ]

  codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  constructor(
    private service: InitialExpenseForecastService,
    public router: Router,
    public menuService: MenuService,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('/lei-orcamentaria-anual/despesa/previsao-inicial')
  }

  public ngOnInit(): void {
    this.fetchGrid()
    this.somenteContasValorPrevisto()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
    this.destroy$.next()
    this.destroy$.complete()
  }

  private somenteContasValorPrevisto(): void {
    this.toggleValorPrevisto.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(valor => {
        const filter = valor === 'S' 
        ? '?valorAutorizadoMaiorQueZero=true'
        : null;
        this.fetchGrid(filter)
      })
  }

  public async fetchGrid(filtro = '') {
    let uri = 'previsao_inicial_despesa_fonte/previsao_inicial_despesa'

    if(filtro)
      uri += filtro

    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        uri,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Previsão'
          item.options.hint = 'Previsão'
          item.options.onClick = () =>
            this.openInitialExpenseForecastFormDialog()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  private openInitialExpenseForecastFormDialog(
    initialData?: InitialExpenseForecastInterface,
  ) {
    const dialogRef = this.dialogService.open(
      InitialExpenseForecastFormDialogComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    if (initialData) dialogRef.componentRef.instance.initialData = initialData
    dialogRef.onClose.pipe(filter(res => res)).subscribe(res => {
      this.fetchGrid()
    })
  }

  public async edit(uuid: string) {
    const expenseForecast = await this.dataSource.store().byKey(uuid)
    this.openInitialExpenseForecastFormDialog(expenseForecast)
  }

  openRelationData(uuid: string) {
    this.gravarParametros()
    this.router.navigate([
      'lei-orcamentaria-anual',
      'despesa',
      'previsao-inicial',
      'edit',
      uuid,
    ])
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Previsão excluída com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  calculateAllDisbursementSchedule() {
    const firstDialogRef = this.dialogService.open(ConfirmationComponent, {
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    firstDialogRef.componentRef.instance.dialogTitle =
      'Cálculo do cronograma de desembolso para todas as contas de receita'
    firstDialogRef.componentRef.instance.confirmationContent = {
      confirmType: 'primary',
      body: 'O processo irá fazer o rateio do percentual de 100% entre os 12 meses o cálculo em todas as contas de despesa com valor previsto no exercício.',
    }
    firstDialogRef.onClose
      .pipe(
        first(),
        filter(res => res),
        switchMap(_ => {
          const secondDialogRef = this.dialogService.open(
            ConfirmationComponent,
            {
              closeOnEsc: false,
              closeOnBackdropClick: false,
            },
          )
          secondDialogRef.componentRef.instance.dialogTitle = 'ATENÇÃO!'
          secondDialogRef.componentRef.instance.confirmationContent = {
            confirmType: 'danger',
            body: 'Os valores de cronograma de desembolso já cadastrados serão substituidos! Deseja continuar o processo de cálculo?',
          }
          return secondDialogRef.onClose.pipe(
            first(),
            filter(res => res),
            tap(_ => (this.loading = true)),
            switchMap(_ =>
              this.service.shareAll().pipe(
                take(1),
                finalize(() => (this.loading = false)),
              ),
            ),
          )
        }),
      )
      .subscribe(
        _ => {
          this.toastr.send({
            success: true,
            title: 'Sucesso',
            message:
              'Cronograma desembolso padrão aplicado para todas as contas previstas!',
          })
        },
        err => {
          this.toastr.send({
            error: true,
            title: 'Erro',
            message:
              err.message ||
              'Falha ao aplicar cronograma desembolso nas contas!',
          })
        },
      )
  }

  public calculateFilterExpression(value: string, _: unknown) {
    if (/^\d+$/.test(value))
      return [
        ['previsaoInicialDespesa.projetoAtividade.ordem', '=', parseInt(value)],
      ]
  }

  public initialExpenseForecastSourceSearch() {
    this.dialogService.open(InitialExpenseForecastSourceSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
  }
}
