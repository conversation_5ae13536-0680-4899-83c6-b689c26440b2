<eqp-nebular-dialog
  [dialogTitle]="mainTitle"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="fetchGrid()"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonId]="'dispose'"
  [bottomLeftButtonTitle]="'Voltar'"
  (bottomLeftButtonEmitter)="dispose()"
  [bottomLeftButtonText]="'Voltar'"
>
  <dx-data-grid
    [dataSource]="dataSource"
    [allowColumnReordering]="true"
    [allowColumnResizing]="true"
    [columnAutoWidth]="true"
    [showColumnLines]="false"
    [showRowLines]="false"
    [showBorders]="false"
    [rowAlternationEnabled]="true"
    [wordWrapEnabled]="true"
    [loadPanel]="false"
    [columnHidingEnabled]="true"
    [remoteOperations]="true"
  >
    <dxo-paging [pageSize]="10"></dxo-paging>
    <dxo-pager
      [showInfo]="true"
      [showNavigationButtons]="true"
      [showPageSizeSelector]="false"
    >
    </dxo-pager>

    <dxo-filter-row [visible]="true"></dxo-filter-row>
    <dxo-sorting mode="multiple"></dxo-sorting>

    <dxi-column
      dataField="codigo"
      caption="Conta de despesa"
      alignment="left"
      dataType="number"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.orgao.codigo"
      caption="Orgão"
      alignment="left"
      dataType="number"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.unidade.codigo"
      caption="Unidade"
      alignment="left"
      dataType="number"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.funcao.codigo"
      caption="Função"
      alignment="left"
      dataType="number"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.subfuncao.codigo"
      caption="Subfunção"
      alignment="left"
      dataType="number"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.programa.codigo"
      caption="Programa"
      alignment="left"
      dataType="number"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.projetoAtividade.tipo"
      caption="Tipo"
      alignment="left"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.projetoAtividade.ordem"
      caption="Ordem"
      alignment="left"
      [calculateFilterExpression]="calculateFilterExpression"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.projetoAtividade.nome"
      caption="Projeto ou atividade"
    ></dxi-column>
    <dxi-column
      dataField="previsaoInicialDespesa.planoDespesa.codigo"
      caption="Natureza"
      alignment="left"
    ></dxi-column>
    <dxi-column
      dataField="fonteRecurso.codigo"
      caption="Fonte recurso"
      alignment="left"
    ></dxi-column>
  </dx-data-grid>
</eqp-nebular-dialog>
