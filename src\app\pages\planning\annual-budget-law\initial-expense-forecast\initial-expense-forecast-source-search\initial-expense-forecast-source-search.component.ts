import { Component, OnInit } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'

@Component({
  selector: 'eqp-initial-expense-forecast-source-search',
  templateUrl: './initial-expense-forecast-source-search.component.html',
  styleUrls: ['./initial-expense-forecast-source-search.component.scss'],
})
export class InitialExpenseForecastSourceSearchComponent implements OnInit {
  public mainTitle = "Pesquisa previsão inicial da despesa orçamentária"
  public dataSource: DataSource

  constructor(
    private crudService: CrudService,
    private ref: NbDialogRef<InitialExpenseForecastSourceSearchComponent>,
  ) {}

  ngOnInit(): void {
    this.fetchGrid()
  }

  public async fetchGrid() {
    this.dataSource = new DataSource({
      store: await this.crudService.getDataSourceFiltro(
        'uuid',
        'previsao_inicial_despesa_fonte/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public dispose() {
    this.ref.close()
  }

  public calculateFilterExpression(value: string, _: unknown) {
    if((/^\d+$/).test(value))
      return [['previsaoInicialDespesa.projetoAtividade.ordem', '=', parseInt(value)]];
  }
}
