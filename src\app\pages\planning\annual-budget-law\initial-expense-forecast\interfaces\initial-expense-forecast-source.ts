import { SourceGroupInterface } from '@pages/planning/shared/interfaces/source-group'
import { ResourceSourceCombinationInterface } from '../../../resource-source/interfaces/resource-source-combination'
import { AccountPhaseInterface } from './account-phase'
import { InitialExpenseForecastInterface } from './initial-expense-forecast'
import { StnMarkerInterface } from './stn-marker'

export interface InitialExpenseForecastSourceInterface {
  uuid: string
  codigo: number
  fonteRecurso: ResourceSourceCombinationInterface
  marcadorStn: StnMarkerInterface
  grupoFonte: SourceGroupInterface
  faseConta: AccountPhaseInterface
  valorAutorizado: number
  percentualOrcamentoCrianca: number
  executarOrcamentoCriancaPorPa: string // S, N
  previsaoInicialDespesa?: InitialExpenseForecastInterface
}
