import { ExpensePlanInterface } from '@pages/planning/multi-year-plan/interfaces/expense-plan'
import { ProgramInterface } from '@pages/planning/multi-year-plan/interfaces/program'
import { OrganInterface } from '@pages/planning/organs-unities/interfaces/organ'
import { UnityInterface } from '@pages/planning/organs-unities/interfaces/unity'
import { AdditionalCreditInterface } from '@pages/planning/shared/interfaces/additional-credit'
import { FunctionInterface } from '@pages/planning/shared/interfaces/function'
import { ProjectActivityInterface } from '@pages/planning/shared/interfaces/project-activity'
import { SubFunctionInterface } from '@pages/planning/shared/interfaces/sub-function'

export interface InitialExpenseForecastInterface {
  uuid: string
  unidade?: UnityInterface
  funcao?: FunctionInterface
  subfuncao?: SubFunctionInterface
  programa?: ProgramInterface
  projetoAtividade?: ProjectActivityInterface
  planoDespesa?: ExpensePlanInterface
  tipoCreditoAdicional?: AdditionalCreditInterface
  orgao?: OrganInterface
  totalAutorizado?: number // verificar calculo com BACK
}
