import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseChildService } from '@common/services/base/base-child.service'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { DisbursementScheduleInterface } from '../interfaces/disbursement-schedule'

@Injectable({
  providedIn: 'root',
})
export class DisbursementScheduleSourceService extends BaseChildService<DisbursementScheduleInterface> {
  constructor(protected httpClient: HttpClient) {
    super(httpClient, 'previsao_inicial_despesa_fonte', 'cronograma_desembolso')
  }

  public postBatch(
    initialRevenueForecastSourceUuid: string,
    data: DisbursementScheduleInterface[],
  ) {
    return this.http.post<ResponseDto<DisbursementScheduleInterface[]>>(
      `previsao_inicial_despesa_fonte/${initialRevenueForecastSourceUuid}/cronograma_desembolso/lote`,
      data,
      {
        observe: 'response',
      },
    )
  }

  public putBatch(
    initialRevenueForecastSourceUuid: string,
    data: DisbursementScheduleInterface[],
  ) {
    return this.http.put<ResponseDto<DisbursementScheduleInterface[]>>(
      `previsao_inicial_despesa_fonte/${initialRevenueForecastSourceUuid}/cronograma_desembolso/lote`,
      data,
      {
        observe: 'response',
      },
    )
  }
}
