import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseChildService } from '@common/services/base/base-child.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { SourceGroupInterface } from '@pages/planning/shared/interfaces/source-group';
import { InitialExpenseForecastSourceInterface } from '../interfaces/initial-expense-forecast-source';

@Injectable({
  providedIn: 'root'
})
export class InitialExpenseForecastSourceService extends BaseChildService<InitialExpenseForecastSourceInterface> {
  constructor(protected httpClient: HttpClient) {
    super(httpClient, 'previsao_inicial_despesa', 'fonte_recurso')
  }

  public initializeResourceSource(uuid: string, sourceGroup: SourceGroupInterface) {
    return this.http.post<ResponseDto<InitialExpenseForecastSourceInterface[]>>(
      `previsao_inicial_despesa/${uuid}/gerar_fonte_recurso`,
      {
        grupoFonte: sourceGroup
      },
      {
        observe: 'response',
      },
    )
  }

  public putBatchInitialExpenseForecastSource(
    initialExpenseForecastUuid: string,
    data: InitialExpenseForecastSourceInterface[],
  ) {
    return this.http.put<ResponseDto<InitialExpenseForecastSourceInterface[]>>(
      `previsao_inicial_despesa/${initialExpenseForecastUuid}/fonte_recurso/lote`,
      data,
      {
        observe: 'response',
      },
    )
  }
}
