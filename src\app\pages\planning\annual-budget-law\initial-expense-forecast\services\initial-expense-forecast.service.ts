import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import { UserDataService } from '@guards/services/user-data.service'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { Observable } from 'rxjs'
import { InitialExpenseForecastInterface } from '../../initial-expense-forecast/interfaces/initial-expense-forecast'

@Injectable({
  providedIn: 'root',
})
export class InitialExpenseForecastService extends BaseService<
  ResponseDto<InitialExpenseForecastInterface[]>,
  InitialExpenseForecastInterface
> {
  constructor(
    protected http: HttpClient,
    private userService: UserDataService,
  ) {
    super(http, 'previsao_inicial_despesa')
  }

  public getFaseConta(): Observable<any> {
    return this.http.get<any>('fase_conta/paginado', {})
  }

  public getExercicioStatus(): Observable<any> {
    const userData = this.userService.userData
    return this.http.get<any>(`exercicio/${userData.exercicioUuid}`, {})
  }

  public validarFonteRecurso(previsaoInicialDespesaUuid: string, codigoFonteRecurso: number) {
    const headers = new HttpHeaders()
    let uri = `previsao_inicial_despesa_fonte/validar_fonte_recurso_saude?previsaoInicialDespesaUuid=${previsaoInicialDespesaUuid}&codigoFonteRecurso=${codigoFonteRecurso}`
    return this.http.get<ResponseDto<string>>(uri, {headers})
  }

  public shareAll() {
    const headers = new HttpHeaders()
    return this.http.put<any>(
      `previsao_inicial_despesa_fonte/cronograma_desembolso_geral/lote`,
      {},
      {
        headers,
        observe: 'response',
      },
    )
  }

  public validarDespesaPorNivel(uuid: string) {
    return this.httpClient.get<ResponseDto<string>>(`previsao_inicial_despesa/validar_plano_despesa_por_nivel_orcamentario/${uuid}`)
  }
}
