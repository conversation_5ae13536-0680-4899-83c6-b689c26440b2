import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { getMonths } from '@pages/planning/shared/helpers/collection.helper'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import { finalize, take } from 'rxjs/operators'
import { DisbursementScheduleSourceService } from '../../services/disbursement-schedule-source.service'
import { DisbursementScheduleInterface } from './../../interfaces/disbursement-schedule'
import * as _ from 'lodash'

@Component({
  selector: 'eqp-disbursement-schedule-source',
  templateUrl: './disbursement-schedule-source.component.html',
  styleUrls: ['./disbursement-schedule-source.component.scss'],
})
export class DisbursementScheduleSourceComponent implements OnInit {
  loading = false
  calculatedModel: FormGroup
  currencyFormat = currencyFormat
  months = getMonths()
  data: DisbursementScheduleInterface[]
  private isEditing = false

  @Input() parentUuid: string
  @Input() valorAutorizado: number

  enableRound: boolean = true

  constructor(
    private builder: FormBuilder,
    private toastr: ToastrService,
    private service: DisbursementScheduleSourceService,
  ) {}

  ngOnInit(): void {
    this.enableRound = true
    this.calculatedModel = this.getModel()
    this.fetchPageData()
  }

  private fetchPageData() {
    this.loading = true
    this.service
      .get(this.parentUuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        const disbScheduleData = res.dados as DisbursementScheduleInterface[]
        if (disbScheduleData && disbScheduleData.length > 0) {
          this.data = disbScheduleData
          this.isEditing = true
        } else {
          this.data = this.months.map(month => {
            return {
              uuid: 'uuid-' + month.value,
              valor: 0,
              percentual: 0,
              mes: month.value,
            }
          })
        }
        this.calculateValues()
      }),
      (resp: any) => this.toastr.bulkSend(resp.mensagens)
  }

  get somaPercentuais() {
    return this.calculatedModel.get('somaPercentuais').value
  }

  private getModel(): FormGroup {
    return this.builder.group({
      valorAutorizado: [this.valorAutorizado],
      somaPercentuais: [],
      diferencaPercentual: [],
      somaValor: [],
      diferencaValor: [],
    })
  }

  monthApportionment() {
    const monthValue = this.valorAutorizado * 0.0833
    const lastMonthValue = this.valorAutorizado * 0.0837
    for (let i = 0; i < this.data.length - 1; i++) {
      this.data[i].percentual = 8.33
      this.data[i].valor = monthValue
    }
    this.data[this.data.length - 1].percentual = 8.37
    this.data[this.data.length - 1].valor = lastMonthValue
    this.calculateValues()
  }

  clearValues() {
    this.data.forEach(month => {
      month.percentual = 0
      month.valor = 0
    })
    this.calculateValues()
  }

  calculateValues() {
    const somaPercentuais = this.data?.reduce(
      (a, b) => a + Number(b.percentual),
      0,
    )
    const somaValor = this.data?.reduce((a, b) => a + Number(b.valor), 0)
    const diferencaPercentual = 100 - somaPercentuais
    const diferencaValor = this.valorAutorizado - somaValor
    this.calculatedModel.patchValue({
      somaPercentuais: _.round(somaPercentuais, 2),
      diferencaPercentual: _.round(diferencaPercentual, 2),
      somaValor: _.round(somaValor, 2),
      diferencaValor: _.round(diferencaValor, 2),
    })
  }

  public onRowUpdating(event: any) {
    let eventPercentage: number = 0
    let eventValue: number = 0
    let validationDiff: number = event.oldData?.percentual
      ? Number(event.oldData.percentual)
      : 0
    if (event.newData?.percentual) {
      eventPercentage = _.round(Number(event.newData.percentual), 2)
      eventValue = this.valorAutorizado * (eventPercentage / 100)
    } else if (event.newData?.valor) {
      eventValue = _.round(Number(event.newData.valor), 2)
      const valuePercentage = (eventValue / this.valorAutorizado) * 100
      eventPercentage = _.round(valuePercentage, 2)
    }
    event.newData = {
      ...event.newData,
      valor: eventValue.toFixed(3),
      percentual: eventPercentage,
    }

    event.cancel = !this.newPercentageIsValid(eventPercentage - validationDiff)
  }

  private newPercentageIsValid(percentage: number) {
    let nextTotal = this.somaPercentuais + Number(percentage)
    if (nextTotal > 100) {
      this.toastr.send({
        title: 'Alerta',
        warning: true,
        message: 'A soma dos percentuais não pode ser superior a 100%',
      })
      return false
    }
    return true
  }

  public submit(): void {
    if (this.isEditing) {
      this.update()
    } else {
      this.create()
    }
  }

  private create(): void {
    this.loading = true
    this.service
      .postBatch(this.parentUuid, this.data)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: 'Cronograma de desembolso cadastrado(a) com sucesso.',
        })
        this.isEditing = true
        this.data = res.body.dados
      })
  }

  private update(): void {
    this.loading = true
    this.service
      .putBatch(this.parentUuid, this.data)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(_ => {
        this.toastr.send({
          success: true,
          message: 'Cronograma de desembolso atualizado(a) com sucesso.',
        })
      })
  }
}
