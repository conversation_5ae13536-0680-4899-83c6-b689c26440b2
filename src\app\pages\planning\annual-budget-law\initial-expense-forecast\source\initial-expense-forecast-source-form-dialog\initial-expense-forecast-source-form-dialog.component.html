<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col col-md-3">
        <nb-form-field>
          <label class="label">Código*</label>
          <input
            #input
            [type]="'text'"
            [fullWidth]="true"
            [fieldSize]="'small'"
            formControlName="codigo"
            placeholder="Código"
            type="number"
            onKeyPress="if(this.value.length==6) return false;"
            nbInput
            [readonly]="uuid && disableAutorizedValue"
          />
        </nb-form-field>
      </div>
      <div class="col">
        <eqp-search-field
          label="Fonte recurso *"
          formControlName="fonteRecurso"
          searchColumnsType="defaultColumns"
          uri="fonte_recurso_combinacao/paginado"
          dialogTitle="Fonte recurso"
          messageNotFound="Fonte de recurso não encontrada."
          [returnAllData]="true"
          waitingTime="1000"
          [disabled]="uuid && disableAutorizedValue"
        >
        </eqp-search-field>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Marcador Stn*"
          placeholder="Marcador Stn"
          formControlName="marcadorStn"
          [dataSource]="stnMarkerData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Grupo fonte*"
          placeholder="Grupo fonte"
          formControlName="grupoFonte"
          [dataSource]="sourceGroupData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
          [disabled]="uuid && disableAutorizedValue"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="valorAutorizado"
          label="Valor autorizado"
          placeholder="Valor autorizado"
          [disabled]="disableAutorizedValue"
        >
        </eqp-nebular-input>
      </div>
      <div class="col">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="percentualOrcamentoCrianca"
          label="Orçamento criança (%)"
          placeholder="Orçamento criança (%)"
          [style]="'currency'"
          [options]="{ prefix: '', thousands: '.', decimal: ',', suffix: '%' }"
          [disabled]="!enableChildBudget"
        >
        </eqp-nebular-input>
      </div>
      <div class="col">
        <eqp-nebular-toggle
          *ngIf="false"
          formControlName="executarOrcamentoCriancaPorPa"
          label="Executar orçamento criança"
          title="Executar orçamento criança"
          [disabled]="!enableChildBudget || (uuid && disableAutorizedValue)"
        ></eqp-nebular-toggle>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="!model.valid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
