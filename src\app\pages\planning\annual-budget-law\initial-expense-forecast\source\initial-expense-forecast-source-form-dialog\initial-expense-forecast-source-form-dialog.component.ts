import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { catchError, debounceTime, filter, finalize, first, map, switchMap, take, takeUntil } from 'rxjs/operators'
import { InitialExpenseForecastSourceInterface } from '../../interfaces/initial-expense-forecast-source'
import { InitialExpenseForecastService } from '../../services/initial-expense-forecast.service'
import { StnMarkerInterface } from '../../interfaces/stn-marker'
import { of, Subject } from 'rxjs'
import { Observable } from 'rxjs-compat'
import { ResourceSourceCombinationInterface } from '@pages/planning/resource-source/interfaces/resource-source-combination'
import { ToastrService } from '@common/services/toastr/toastr.service'

@Component({
  selector: 'eqp-initial-expense-forecast-source-form-dialog',
  templateUrl: './initial-expense-forecast-source-form-dialog.component.html',
  styleUrls: ['./initial-expense-forecast-source-form-dialog.component.scss'],
})
export class InitialExpenseForecastSourceFormDialogComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  pageTitle: string = 'Previsão inicial da despesa - Fonte recurso'
  loading: boolean
  model: FormGroup
  disableButton: boolean = false
  disableAutorizedValue: boolean = false
  enableChildBudget: boolean = false

  stnMarkerData: DataSource
  sourceGroupData: DataSource

  @Input() initialData: InitialExpenseForecastSourceInterface
  @Input() public data: {
    parentUuid: string
  }
  private unsub$ = new Subject()

  constructor(
    public router: Router,
    public menuService: MenuService,
    private expenseForecastService: InitialExpenseForecastService,
    private exercicio: UserDataService,
    private dialogRef: NbDialogRef<InitialExpenseForecastSourceFormDialogComponent>,
    private builder: FormBuilder,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  get uuid() {
    return this.model.get('uuid')?.value
  }

  ngOnInit(): void {
    this.model = this.getNewModel()

    if (parseInt(this.exercicio.userData.exercicio) >= 2023)
      this.model.get('marcadorStn').setValidators(Validators.required)

    this.getExerciseStatus()
    this.initializeSelectData()
    this.fetchPageData(this.data.parentUuid)
    
    if (this.initialData) {
      this.disableButton = true
      this.loadForm(this.initialData)
    } else {
      this.validarFonteRecurso()
    }
  }

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  private fetchPageData(uuid: string) {
    this.loading = true
    this.expenseForecastService
      .getOne(uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.getData(res)
      })
  }

  private getData(res) {
    if (res.projetoAtividade.flagOrcamentoCrianca == 'S') {
      this.enableChildBudget = true
    } else {
      this.enableChildBudget = false
    }
  }

  private getExerciseStatus() {
    this.expenseForecastService
      .getExercicioStatus()
      .pipe(first())
      .subscribe(data => {
        const codigoStatus = data.dados.exercicioStatus.codigo;

        this.disableAutorizedValue = codigoStatus !== 2;
      })
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      codigo: [
        undefined,
        Validators.compose([Validators.maxLength(6), Validators.required]),
      ],
      fonteRecurso: [undefined, Validators.required],
      marcadorStn: [undefined],
      grupoFonte: [undefined, Validators.required],
      valorAutorizado: [0],
      percentualOrcamentoCrianca: [''],
      executarOrcamentoCriancaPorPa: [false],
    })
  }

  private validarFonteRecurso(): void {  
    const fonteRecursoObs$ = this.model.get('fonteRecurso').valueChanges;  
    
    fonteRecursoObs$  
      .pipe(  
        takeUntil(this.unsub$),  
        filter((fonteRecurso) => !!fonteRecurso?.codigo),  
        switchMap(fonteRecurso =>   
          this.expenseForecastService.validarFonteRecurso(  
            this.data.parentUuid,   
            fonteRecurso.codigo  
          ).pipe(  
            catchError(() => of({ dados: '' })) 
          )  
        )  
      )  
      .subscribe({  
        next: ({ dados }) => {  
          if (dados.trim()) {  
            this.toastr.send({  
              error: true,  
              message: dados  
            });  
            this.model.get('fonteRecurso').reset(null, { emitEvent: false });  
          }  
        },  
      });  
  }

  private async initializeSelectData() {
    const stnMarkerData = await this.expenseForecastService
      .customGetSingleData<StnMarkerInterface>(`receita_lancamento/marcador_stn`)
      .toPromise()

    this.stnMarkerData = new DataSource({
      store: {
        data: stnMarkerData?.data || [],
        type: 'array',
        key: 'uuid',
      },
    })

    this.sourceGroupData = new DataSource({
      store: this.expenseForecastService.getDataSourceFiltro(
        'uuid',
        'grupo_fonte/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())
      this.dialogRef.close(dto)
    }
  }

  public codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  private loadForm(initialData: InitialExpenseForecastSourceInterface) {
    let dto = {
      ...initialData,
      fonteRecurso: initialData.fonteRecurso,
      marcadorStn: initialData.marcadorStn?.uuid,
      grupoFonte: initialData.grupoFonte?.uuid,
      executarOrcamentoCriancaPorPa:
        initialData.executarOrcamentoCriancaPorPa === 'S',
    }
    this.model.patchValue(dto)
  }

  private prepare(formData: any) {
    let dto: InitialExpenseForecastSourceInterface = {
      ...formData,
      fonteRecurso: formData.fonteRecurso
        ? { uuid: formData.fonteRecurso.uuid }
        : undefined,
      marcadorStn: formData.marcadorStn
        ? { uuid: formData.marcadorStn }
        : undefined,
      grupoFonte: formData.grupoFonte
        ? { uuid: formData.grupoFonte }
        : undefined,
      valorAutorizado: +formData.valorAutorizado,
      executarOrcamentoCriancaPorPa: formData.executarOrcamentoCriancaPorPa
        ? 'S'
        : 'N',
    }
    
    return dto
  }

  public cancel() {
    this.dialogRef.close(null)
  }
}
