<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancelar()"
  bottomLeftButtonId="initial-expense-forecast-source-return-button"
>
  <div class="row">
    <div class="col">
      <label class="label">Projeto atividade</label>
      <input
        type="text"
        nbInput
        fullWidth
        fieldSize="small"
        placeholder="00,00"
        disabled
        class="text-right"
        [value]="
          data?.projetoAtividade?.tipo +
          '' +
          (data?.projetoAtividade?.ordem | number: '3.0')
        "
      />
    </div>
    <div class="col">
      <label class="label">&nbsp;</label>
      <input
        type="text"
        nbInput
        fullWidth
        fieldSize="small"
        placeholder="00,00"
        disabled
        class="text-right"
        [value]="data?.projetoAtividade?.nome"
      />
    </div>
    <div class="col">
      <label class="label">Fonte de recurso</label>
      <input
        type="text"
        nbInput
        fullWidth
        fieldSize="small"
        placeholder="00,00"
        disabled
        class="text-right"
        [value]="sourceData?.fonteRecurso?.codigo"
      />
    </div>
    <div class="col">
      <label class="label">&nbsp;</label>
      <input
        type="text"
        nbInput
        fullWidth
        fieldSize="small"
        placeholder="00,00"
        disabled
        class="text-right"
        [value]="sourceData?.fonteRecurso?.nome"
      />
    </div>
  </div>
  <div class="mt-3">
    <nb-tabset>
      <nb-tab
        [disabled]="!uuid"
        tabTitle="Cronograma de desembolso"
        tabId="disbursement-scheduleg"
        class="p-0"
      >
        <eqp-disbursement-schedule-source
          *ngIf="sourceData"
          [parentUuid]="uuid"
          [valorAutorizado]="sourceData?.valorAutorizado"
        ></eqp-disbursement-schedule-source>
      </nb-tab>
    </nb-tabset>
  </div>
</eqp-standard-page>
