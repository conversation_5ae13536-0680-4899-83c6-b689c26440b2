import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { forkJoin } from 'rxjs'
import { finalize, take } from 'rxjs/operators'
import { InitialExpenseForecastInterface } from '../../interfaces/initial-expense-forecast'
import { InitialExpenseForecastSourceInterface } from '../../interfaces/initial-expense-forecast-source'
import { InitialExpenseForecastSourceService } from '../../services/initial-expense-forecast-source.service'
import { InitialExpenseForecastService } from '../../services/initial-expense-forecast.service'

@Component({
  selector: 'eqp-initial-expense-forecast-source-form',
  templateUrl: './initial-expense-forecast-source-form.component.html',
  styleUrls: ['./initial-expense-forecast-source-form.component.scss'],
})
export class InitialExpenseForecastSourceFormComponent implements OnInit {
  public pageTitle: string = 'Previsão inicial da despesa - Fonte'
  public loading: boolean = false
  public columns: DxColumnInterface[] = []

  data: InitialExpenseForecastInterface
  sourceData: InitialExpenseForecastSourceInterface
  parentUuid: string
  uuid: string

  constructor(
    private toastr: ToastrService,
    private service: InitialExpenseForecastService,
    private sourceService: InitialExpenseForecastSourceService,
    private route: ActivatedRoute,
    private router: Router,
  ) {}

  ngOnInit(): void {
    const { uuid, parentUuid } = this.route.snapshot.params
    this.uuid = uuid
    this.parentUuid = parentUuid
    this.loadPageData()
  }

  loadPageData() {
    this.loading = true
    forkJoin([
      this.service.getIndividual(this.parentUuid),
      this.sourceService.getIndividual(this.parentUuid, this.uuid),
    ])
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        ([expenseForecast, forecastSource]) => {
          this.data = expenseForecast.dados
          this.sourceData = forecastSource.dados
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  cancelar() {
    this.router.navigate([
      'lei-orcamentaria-anual',
      'despesa',
      'previsao-inicial',
      'edit',
      this.parentUuid,
    ])
  }
}
