<dx-data-grid
  id="initialExpenseForecastSourceGrid"
  [dataSource]="dataSource"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [nbSpinner]="loading"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="false"
  [remoteOperations]="true"
  keyExpr="uuid"
  (onToolbarPreparing)="onToolbarPreparing($event)"
  class="mt-3"
>
  <dxo-export
    [enabled]="true"
    [excelWrapTextEnabled]="true"
    [excelFilterEnabled]="true"
    [fileName]="pageTitle"
  ></dxo-export>

  <dxo-paging [pageSize]="10"></dxo-paging>
  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="true"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar fonte de recurso"
  ></dxo-search-panel>

  <dxo-editing
    mode="cell"
    [allowUpdating]="true"
    [allowDeleting]="false"
    [allowAdding]="nivelPermissao === 'FULL'"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column
    dataField="codigo"
    [allowEditing]="false"
    caption="Código"
  ></dxi-column>

  <dxi-column
    dataField="fonteRecurso.codigoEhNome"
    [allowEditing]="false"
    caption="Fonte de recurso"
  ></dxi-column>

  <dxi-column
    dataField="valorAutorizado"
    [allowEditing]="false"
    caption="Valor autorizado"
    [editorOptions]="{ placeholder: '00' }"
    cellTemplate="colunaValorAutorizado"
  ></dxi-column>

  <div *dxTemplate="let data of 'colunaValorAutorizado'">
    {{ +data.value | currency : 'BRL' }}
  </div>

  <dxi-column
    dataField="faseConta.nome"
    [allowEditing]="false"
    caption="Fase"
  ></dxi-column>

  <dxi-column
    dataField="uuid"
    caption=""
    [width]="80"
    [allowFiltering]="false"
    [allowEditing]="false"
    [allowSorting]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>

  <div *dxTemplate="let data of 'acaoColumn'">
    <div class="w-100 d-flex justify-content-end">
      <a
        title="Dados relacionados"
        (click)="openRelationData(data.value)"
        class="dx-link dx-link-edit fas fa-folder dx-link-icon btn-icon-grid"
      >
      </a>
      <a
        title="Alterar"
        (click)="edit(data.value)"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
      >
      </a>
      <a
        *ngIf="nivelPermissao === 'FULL'"
        title="Remover"
        (click)="remove(data.value)"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
      >
      </a>
    </div>
  </div>
</dx-data-grid>
