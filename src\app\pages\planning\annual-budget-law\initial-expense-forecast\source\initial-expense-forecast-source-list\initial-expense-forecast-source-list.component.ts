import { Component, Input, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { filter, finalize, take } from 'rxjs/operators'
import { InitialExpenseForecastSourceInterface } from '../../interfaces/initial-expense-forecast-source'
import { InitialExpenseForecastService } from '../../services/initial-expense-forecast.service'
import { InitialExpenseForecastSourceFormDialogComponent } from '../initial-expense-forecast-source-form-dialog/initial-expense-forecast-source-form-dialog.component'
import { ToastrService } from './../../../../../../@common/services/toastr/toastr.service'
import { InitialExpenseForecastSourceService } from './../../services/initial-expense-forecast-source.service'

@Component({
  selector: 'eqp-initial-expense-forecast-source-list',
  templateUrl: './initial-expense-forecast-source-list.component.html',
  styleUrls: ['./initial-expense-forecast-source-list.component.scss'],
})
export class InitialExpenseForecastSourceListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle: string = 'Fonte de recurso'

  currencyFormat = currencyFormat
  dataSource: DataSource

  @Input() parentUuid: string

  constructor(
    private service: InitialExpenseForecastService,
    private serviceExpenseForecastSourceService: InitialExpenseForecastSourceService,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
    public router: Router,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  public async fetchGrid() {
    const sourceData = await this.service
      .getSingleData<InitialExpenseForecastSourceInterface[]>(
        `previsao_inicial_despesa/${this.parentUuid}/fonte_recurso`,
      )
      .toPromise()
    this.dataSource = new DataSource({
      store: {
        data: sourceData?.dados || [],
        type: 'array',
        key: 'uuid',
      },
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Fonte recurso'
          item.options.hint = 'Novo(a) fonte recurso'
          item.options.onClick = () => this.openForecastSourceDialog()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(retorno => {
      if (retorno === 'S') {
        this.serviceExpenseForecastSourceService
          .delete(this.parentUuid, uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Fonte de recurso removido com sucesso',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  private openForecastSourceDialog(
    initialData?: InitialExpenseForecastSourceInterface,
  ) {
    const dialogRef = this.dialogService.open(
      InitialExpenseForecastSourceFormDialogComponent,
      {
        context: {
          data: {
            parentUuid: this.parentUuid,
          },
        },
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.componentRef.instance.initialData = initialData
    dialogRef.onClose.pipe(filter(res => res)).subscribe(res => {
      if (!res.uuid) {
        this.loading = true
        this.serviceExpenseForecastSourceService
          .post(this.parentUuid, res)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Fonte de recurso cadastrada com sucesso',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      } else {
        this.loading = true
        this.serviceExpenseForecastSourceService
          .put(this.parentUuid, res, res.uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Fonte de recurso atualizada com sucesso',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  async edit(uuid: string) {
    const data = await this.dataSource.store().byKey(uuid)
    if (data) this.openForecastSourceDialog(data)
  }

  public openRelationData(uuid: string): void {
    this.router.navigate([
      `lei-orcamentaria-anual/despesa/previsao-inicial/edit/${this.parentUuid}/fonte/${uuid}`,
    ])
  }
}
