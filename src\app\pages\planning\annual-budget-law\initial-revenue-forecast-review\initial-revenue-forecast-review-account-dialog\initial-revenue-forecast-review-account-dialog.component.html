<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col col-8">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Conta receita*"
          placeholder="Conta receita"
          formControlName="planoReceita"
          [dataSource]="revenuePlanData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
          [disabled]="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col col-md-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo de operação da receita*"
          placeholder="Tipo de operação da receita"
          formControlName="tipoOperacaoReceita"
          [dataSource]="operationTypeData"
          displayExpr="tipoOperacaoReceita.nome"
          valueExpr="uuid"
          [disabled]="uuid"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col col-md-3">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Fonte de recurso*"
          placeholder="Fonte de recurso"
          formControlName="previsaoInicialReceitaFonte"
          [dataSource]="resourceSourceData"
          displayExpr="fonteRecurso.codigoEhNome"
          valueExpr="uuid"
          [disabled]="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col col-md-3">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo*"
          placeholder="Tipo"
          formControlName="tipoRevisao"
          [dataSource]="reviewTypeData"
          displayExpr="descricao"
          valueExpr="uuid"
          [disabled]="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col col-md-3">
        <eqp-nebular-input
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="valor"
          label="Valor*"
          placeholder="Valor"
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-md-3">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="nrControleTce"
          label="Nº SIM-AM"
          placeholder="Nº SIM-AM"
          type="number"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="!model.valid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
