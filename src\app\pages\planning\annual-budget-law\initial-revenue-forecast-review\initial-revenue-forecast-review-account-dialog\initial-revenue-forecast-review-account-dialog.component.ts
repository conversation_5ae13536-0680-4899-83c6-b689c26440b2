import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { RevenuePlanService } from '@pages/planning/multi-year-plan/services/revenue-plan.service'
import { ReviewTypeInterface } from '@pages/planning/shared/interfaces/review-type'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { distinctUntilChanged, finalize, take } from 'rxjs/operators'
import { InitialRevenueForecastSourceService } from '../../initial-revenue-forecast/services/initial-revenue-forecast-source.service'
import { InitialRevenueForecastReviewAccountService } from '../services/initial-revenue-forecast-review-account.service'
import { InitialRevenueForecastReviewService } from '../services/initial-revenue-forecast-review.service'
import { InitialRevenueForecastReviewAccountInterface } from './../interfaces/initial-revenue-forecast-review-account'

@Component({
  selector: 'eqp-initial-revenue-forecast-review-account-dialog',
  templateUrl:
    './initial-revenue-forecast-review-account-dialog.component.html',
  styleUrls: [
    './initial-revenue-forecast-review-account-dialog.component.scss',
  ],
})
export class InitialRevenueForecastReviewAccountDialogComponent
  implements OnInit, OnDestroy
{
  pageTitle: string = 'Revisão da previsão inicial da receita - Conta'
  loading: boolean
  model: FormGroup

  planoReceitaSubscription: Subscription
  tipoOperacaoReceitaSubscription: Subscription

  revenuePlanData: DataSource
  operationTypeData: DataSource
  resourceSourceData: DataSource
  reviewTypeData: DataSource

  @Input() parentUuid: string
  @Input() uuid: string

  constructor(
    private service: InitialRevenueForecastReviewAccountService,
    private initialRevenueForecastService: InitialRevenueForecastReviewService,
    private dialogRef: NbDialogRef<InitialRevenueForecastReviewAccountDialogComponent>,
    private builder: FormBuilder,
    private revenuePlanService: RevenuePlanService,
    private initialRevenueForecastSourceService: InitialRevenueForecastSourceService,
    private toastr: ToastrService,
  ) {}

  codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.initializeSelectData()
    if (this.uuid) {
      this.loading = true
      this.service
        .getIndividual(this.parentUuid, this.uuid)
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => {
          this.loadForm(res.dados)
        })
    }
  }

  ngOnDestroy(): void {
    if (this.planoReceitaSubscription)
      this.planoReceitaSubscription.unsubscribe()
    if (this.tipoOperacaoReceitaSubscription)
      this.tipoOperacaoReceitaSubscription.unsubscribe()
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      planoReceita: [undefined, Validators.required],
      tipoOperacaoReceita: [undefined, Validators.required],
      previsaoInicialReceitaFonte: [undefined, Validators.required],
      tipoRevisao: [undefined, Validators.required],
      valor: [undefined, Validators.required],
      nrControleTce: [],
    })
  }

  private async initializeSelectData() {
    this.revenuePlanData = new DataSource({
      store: this.initialRevenueForecastService.getDataSourceFiltro(
        'uuid',
        'plano_receita/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    const reviewData = await this.initialRevenueForecastService
      .getSingleData<ReviewTypeInterface[]>('tipo_revisao')
      .toPromise()
    this.reviewTypeData = new DataSource({
      store: {
        data: reviewData?.dados || [],
        type: 'array',
        key: 'uuid',
      },
    })
    this.loadSelectionHandlers()
  }

  private loadSelectionHandlers() {
    this.planoReceitaSubscription = this.model
      .get('planoReceita')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.revenuePlanChange(value)
      })
    this.tipoOperacaoReceitaSubscription = this.model
      .get('tipoOperacaoReceita')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.operationTypeChange(value)
      })
  }

  private async revenuePlanChange(uuid: string) {
    if (uuid) {
      let operationTypeData: any = await this.revenuePlanService
        .getInitialForecast(uuid)
        .toPromise()
      if (operationTypeData) {
        operationTypeData = operationTypeData.dados
      } else {
        operationTypeData = []
      }
      this.operationTypeData = new DataSource({
        store: {
          data: operationTypeData,
          type: 'array',
          key: 'uuid',
        },
      })
    }
  }

  private async operationTypeChange(uuid: string) {
    if (uuid) {
      let sourceData = await this.initialRevenueForecastSourceService
        .get(uuid)
        .toPromise()
      if (sourceData) {
        sourceData = sourceData.dados
      } else {
        sourceData = []
      }
      this.resourceSourceData = new DataSource({
        store: {
          data: sourceData,
          type: 'array',
          key: 'uuid',
        },
      })
    }
  }

  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())
      if (!this.uuid) {
        this.loading = true
        this.service
          .post(this.parentUuid, dto)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            res => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Conta associada com sucesso',
              })
              this.dialogRef.close(res.body.dados)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      } else {
        this.loading = true
        this.service
          .put(this.parentUuid, dto, this.uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            res => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Conta atualizada com sucesso',
              })
              this.dialogRef.close(res.body.dados)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  private loadForm(initialData: InitialRevenueForecastReviewAccountInterface) {
    const dto = {
      ...initialData,
      planoReceita: initialData.previsaoInicialReceita?.planoReceita?.uuid,
      tipoOperacaoReceita: initialData.previsaoInicialReceita?.uuid,
      previsaoInicialReceitaFonte:
        initialData.previsaoInicialReceitaFonte?.uuid,
      tipoRevisao: initialData.tipoRevisao?.uuid,
    }
    this.model.patchValue(dto)
    if (dto?.planoReceita) this.revenuePlanChange(dto?.planoReceita)
    if (dto.tipoOperacaoReceita)
      this.operationTypeChange(dto.tipoOperacaoReceita)
  }

  private prepare(formData: any) {
    const {
      uuid,
      previsaoInicialReceitaFonte,
      tipoRevisao,
      valor,
      nrControleTce,
    } = formData
    let dto = {
      uuid,
      previsaoInicialReceitaFonte: { uuid: previsaoInicialReceitaFonte },
      tipoRevisao: { uuid: tipoRevisao },
      valor,
      nrControleTce,
    }
    return dto as InitialRevenueForecastReviewAccountInterface
  }
}
