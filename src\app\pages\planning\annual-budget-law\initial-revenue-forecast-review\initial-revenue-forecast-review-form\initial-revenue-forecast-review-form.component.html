<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [formGroup]="model"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  bottomLeftButtonId="initial-revenue-forecast-review-return-button"
  [rightCustomButtonText]="'Confirmar'"
  [rightCustomButtonVisible]="!effectivated"
  [rightCustomButtonType]="'primary'"
  [rightCustomButtonId]="'initial-revenue-forecast-review-submit-button'"
  [rightCustomButtonDisabled]="!model.valid || model.pristine"
  (rightCustomButtonEmitter)="submit()"
  rightApproveButtonId="initial-revenue-forecast-review-submit-button"
  [rightDenyButtonText]="'Cancelar efetivação'"
  [rightDenyButtonVisible]="effectivated"
  [rightDenyButtonId]="'initial-revenue-forecast-review-cancel-effectivation-button'"
  (rightDenyButtonEmitter)="cancelEffectivation()"
  [rightApproveButtonText]="'Efetivar'"
  [rightApproveButtonVisible]="!effectivated && uuid"
  [rightApproveButtonType]="'success'"
  [rightApproveButtonId]="'initial-revenue-forecast-review-effectivate-button'"
  [rightApproveButtonDisabled]="effectivated || !model.pristine"
  (rightApproveButtonEmitter)="showEffectConfirmation()"
  [enableStatusLabel]="uuid"
  [statusLabelType]="effectivated ? 'success' : 'warning'"
  [statusLabelText]="effectivated ? 'Efetivada' : 'Em edição'"
>
  <nb-tabset (changeTab)="onChangeTab($event)">
    <nb-tab tabTitle="Revisão" class="pb-0">
      <div class="row">
        <div class="col col-md-6">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Número*"
            placeholder="Número"
            formControlName="numero"
            type="number"
            [disabled]="true"
          >
          </eqp-nebular-input>
        </div>
        <div class="col col-md-6">
          <eqp-nebular-input
            [size]="'small'"
            [style]="'date'"
            [shape]="'rectangle'"
            name="Data"
            label="Data*"
            placeholder="Data"
            formControlName="data"
            [disabled]="effectivated"
          ></eqp-nebular-input>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col col-md-6">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Revisão aumentativa*"
            placeholder="Revisão aumentativa"
            formControlName="vlrRevisaoAumentativa"
            [disabled]="effectivated"
          >
          </eqp-nebular-input>
        </div>
        <div class="col col-md-6">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Revisão diminutiva*"
            placeholder="Revisão diminutiva"
            formControlName="vlrRevisaoDiminutiva"
            [disabled]="effectivated"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </nb-tab>
    <nb-tab tabId="accounts" [disabled]="!uuid" tabTitle="Contas" class="pb-0">
      <ng-container *ngIf="uuid && tabWasTouched('accounts')">
        <eqp-initial-revenue-forecast-review-account-list
          [parentUuid]="uuid"
          [effectivated]="effectivated"
        ></eqp-initial-revenue-forecast-review-account-list>
      </ng-container>
    </nb-tab>
  </nb-tabset>
</eqp-standard-page>
