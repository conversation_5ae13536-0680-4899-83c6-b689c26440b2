import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { getMomentDate, getParsedDate } from '@common/helpers/parsers'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { finalize, first, take } from 'rxjs/operators'
import { InitialRevenueForecastReviewService } from '../services/initial-revenue-forecast-review.service'
import { InitialRevenueForecastReviewInterface } from './../interfaces/initial-revenue-forecast-review'

@Component({
  selector: 'eqp-initial-revenue-forecast-review-form',
  templateUrl: './initial-revenue-forecast-review-form.component.html',
  styleUrls: ['./initial-revenue-forecast-review-form.component.scss'],
})
export class InitialRevenueForecastReviewFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public pageTitle: string = 'Revisão da previsão inicial da receita'
  public loading: boolean = false
  public columns: DxColumnInterface[] = []
  private subscription: Subscription

  model: FormGroup
  forecastReview: InitialRevenueForecastReviewInterface
  touchedTabs: Set<string> = new Set<string>([])

  constructor(
    private service: InitialRevenueForecastReviewService,
    private toastr: ToastrService,
    public router: Router,
    private route: ActivatedRoute,
    public menuService: MenuService,
    private builder: FormBuilder,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao('/revisao-previsao-receita')
  }

  ngOnInit(): void {
    const { uuid } = this.route.snapshot.params
    this.model = this.getNewModel()
    if (uuid) {
      this.loadPageData(uuid)
    }
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  get uuid() {
    return this.model.get('uuid').value
  }

  get effectivated() {
    return this.model.get('flagEfetivada')?.value
  }

  loadPageData(uuid: string) {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.forecastReview = res.dados
          this.loadForm(this.forecastReview)
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      numero: [undefined],
      data: [undefined, Validators.required],
      vlrRevisaoDiminutiva: [undefined, Validators.required],
      vlrRevisaoAumentativa: [undefined, Validators.required],
      flagEfetivada: [undefined],
    })
  }

  public submit(): void {
    if (this.uuid) {
      this.update()
    } else {
      this.create()
    }
  }

  public showEffectConfirmation() {
    this.dialogService
      .open(ConfirmationComponent, {
        context: {
          confirmationContent: {
            body: 'Tem certeza que deseja efetivar a revisão?',
          },
          dialogSize: 'medium',
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.subscribe(res => {
        if (res) this.effectivate()
      })
  }

  effectivate() {
    this.loading = true
    this.service
      .effectivate(this.uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          title: 'Sucesso',
          message: 'Revisão efetivada com sucesso',
        })
        this.loadForm(res.body.dados)
        this.model.markAsPristine()
      })
  }

  cancelEffectivation() {
    const dialogRef = this.dialogService.open(ConfirmationComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.componentRef.instance.confirmationContent = {
      body: 'Deseja realmente cancelar a efetivação da revisão?',
    }
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        this.service
          .cancelEffectivation(this.uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(res => {
            this.toastr.send({
              success: true,
              title: 'Sucesso',
              message: 'Efetivação da revisão cancelada com sucesso',
            })
            this.loadForm(res.body.dados)
            this.model.markAsPristine()
          })
      }
    })
  }

  private loadForm(data: InitialRevenueForecastReviewInterface) {
    let dto = {
      ...data,
      data: data.data ? getMomentDate(`${data.data}`) : null,
      flagEfetivada: data.flagEfetivada === 'S',
    }
    this.model.patchValue(dto)
  }

  private prepare(): any {
    const data = this.model.getRawValue()
    let dto = {
      ...data,
    }
    dto.flagEfetivada = data.flagEfetivada ? 'S' : 'N'
    if (dto.data) {
      dto.data = getParsedDate(dto.data)
    }
    return dto
  }

  private create(): void {
    this.loading = true
    this.service
      .post(this.prepare())
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: 'Revisão da previsão criada com sucesso.',
        })
        this.router.navigate([
          'lei-orcamentaria-anual',
          'receita',
          'revisao-previsao-inicial',
          'edit',
          res.body.dados.uuid,
        ])
      })
  }

  private update(): void {
    this.loading = true
    this.service
      .put(this.prepare())
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(_ => {
        this.toastr.send({
          success: true,
          message: 'Revisão da previsão atualizada com sucesso.',
        })
        this.model.markAsPristine()
      })
  }

  cancel() {
    this.router.navigate([
      'lei-orcamentaria-anual',
      'receita',
      'revisao-previsao-inicial',
    ])
  }

  onChangeTab(event: any) {
    const { tabId } = event
    this.touchedTabs.add(tabId)
  }

  tabWasTouched(tabId: string) {
    return this.touchedTabs.has(tabId)
  }
}
