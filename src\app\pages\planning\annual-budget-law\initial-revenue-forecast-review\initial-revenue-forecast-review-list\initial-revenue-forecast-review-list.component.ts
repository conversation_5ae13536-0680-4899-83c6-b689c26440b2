import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'
import { InitialRevenueForecastReviewService } from '../services/initial-revenue-forecast-review.service'

@Component({
  selector: 'eqp-initial-revenue-forecast-review-list',
  templateUrl: './initial-revenue-forecast-review-list.component.html',
  styleUrls: ['./initial-revenue-forecast-review-list.component.scss'],
})
export class InitialRevenueForecastReviewListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Revisão da previsão da receita'

  public dataSource: DataSource
  public activeData: any

  private subscription: Subscription

  booleanLookupOptions = [
    {
      text: 'Sim',
      value: 'S',
    },
    {
      text: 'Não',
      value: 'N',
    },
  ]

  currencyFormat = currencyFormat

  constructor(
    private service: InitialRevenueForecastReviewService,
    private toastr: ToastrService,
    public router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/lei-orcamentaria-anual/receita/revisao-previsao-inicial')
  }

  ngOnInit(): void {
    this.activeData = [
      { texto: 'Sim', valor: 'S' },
      { texto: 'Não', valor: 'N' },
    ]
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'revisao_previsao_inicial_receita',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Revisão'
          item.options.hint = 'Nova revisão'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([
      `lei-orcamentaria-anual/receita/revisao-previsao-inicial/novo`,
    ])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([
      `lei-orcamentaria-anual/receita/revisao-previsao-inicial/edit/${uuid}`,
    ])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Revisão excluída com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
