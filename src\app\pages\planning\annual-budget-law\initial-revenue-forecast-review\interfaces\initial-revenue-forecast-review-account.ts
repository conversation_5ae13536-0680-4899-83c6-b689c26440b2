import { InitialRevenueForecastInterface } from '../../initial-revenue-forecast/interfaces/initial-revenue-forecast'
import { OperationRevenueTypeInterface } from '../../initial-revenue-forecast/interfaces/operation-revenue-type'
import { ResourceSourceInitialForecastInterface } from '../../initial-revenue-forecast/interfaces/resource-source-initial-forecast'

export interface InitialRevenueForecastReviewAccountInterface {
  uuid: string
  previsaoInicialReceita?: InitialRevenueForecastInterface //[BACK] retornar no response (não existe relação direta na tabela)
  tipoRevisao: OperationRevenueTypeInterface
  previsaoInicialReceitaFonte: ResourceSourceInitialForecastInterface
  valor: number
  nrControleTce: number
}
