import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseChildService } from '@common/services/base/base-child.service'
import { InitialRevenueForecastReviewAccountInterface } from '../interfaces/initial-revenue-forecast-review-account'

@Injectable({
  providedIn: 'root',
})
export class InitialRevenueForecastReviewAccountService extends BaseChildService<InitialRevenueForecastReviewAccountInterface> {
  constructor(protected http: HttpClient) {
    super(http, 'revisao_previsao_inicial_receita', 'conta')
  }
}
