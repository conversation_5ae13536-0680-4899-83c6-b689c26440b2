import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { InitialRevenueForecastReviewInterface } from '../interfaces/initial-revenue-forecast-review'

@Injectable({
  providedIn: 'root',
})
export class InitialRevenueForecastReviewService extends BaseService<
  ResponseDto<InitialRevenueForecastReviewInterface[]>,
  InitialRevenueForecastReviewInterface
> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'revisao_previsao_inicial_receita')
  }

  effectivate(uuid: string) {
    return this.http.put<ResponseDto<InitialRevenueForecastReviewInterface>>(
      `revisao_previsao_inicial_receita/${uuid}/efetivar`,
      {},
      {
        observe: 'response',
      },
    )
  }

  cancelEffectivation(uuid: string) {
    return this.http.put<ResponseDto<InitialRevenueForecastReviewInterface>>(
      `revisao_previsao_inicial_receita/${uuid}/cancelar_efetivacao`,
      {},
      {
        observe: 'response',
      },
    )
  }

  public validateBalance(uuid: string) {
    return this.http.get<
      ResponseDto<{ status: string; message: string }>
    >(`revisao_previsao_inicial_receita/${uuid}/validar`)
  }
}
