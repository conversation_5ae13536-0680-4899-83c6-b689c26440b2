import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AccountingEventConfigSearchDialogComponent } from './accounting-event-config-search-dialog.component';

describe('AccountingEventConfigSearchDialogComponent', () => {
  let component: AccountingEventConfigSearchDialogComponent;
  let fixture: ComponentFixture<AccountingEventConfigSearchDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AccountingEventConfigSearchDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AccountingEventConfigSearchDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
