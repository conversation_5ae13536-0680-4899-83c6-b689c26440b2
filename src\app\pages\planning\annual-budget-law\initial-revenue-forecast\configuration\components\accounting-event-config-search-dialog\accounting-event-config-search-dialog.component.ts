import { Component, Input, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'

@Component({
  selector: 'eqp-accounting-event-config-search-dialog',
  templateUrl: './accounting-event-config-search-dialog.component.html',
  styleUrls: ['./accounting-event-config-search-dialog.component.scss'],
})
export class AccountingEventConfigSearchDialogComponent
  extends BaseTelasComponent
  implements OnInit
{
  @Input() pageTitle: string = 'Selecionar evento contábil'
  @Input() isMultiple: boolean = false
  @Input() uri: string = 'evento_contabil_config'
  @Input() filter: string
  loading: boolean
  public accountPlanData: any

  selectedRowKeys = []

  dataSource: DataSource

  constructor(
    public router: Router,
    public menuService: MenuService,
    private crudService: CrudService,
    private dialogRef: NbDialogRef<AccountingEventConfigSearchDialogComponent>,
  ) {
    super(menuService, router)
    this.permissao(this.uri)
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  fetchGrid() {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltroComposto(
        'uuid',
        this.uri,
        10,
        this.filter,
      ),
      paginate: true,
      pageSize: 10,
    })

    this.accountPlanData = {
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'plano_contabil/paginado',
        10
      ),
      paginate: true,
      pageSize: 10
    }
  }

  async confirm() {
    if (this.isMultiple) {
      const items = []
      const dataSource = await this.dataSource.store().load()
      this.selectedRowKeys.forEach(key => {
        const item = dataSource.find(item => item.uuid === key)
        items.push(item)
      })
      this.dialogRef.close(items)
    } else {
      this.dialogRef.close(this.selectedRowKeys[0])
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  onSelectionChanged(event: any) {
    this.selectedRowKeys = event.selectedRowsData
  }

  public codigoPlanoContabilCredito(data) {
    return data.planoContabilCredito && data.planoContabilCredito.nome    
  }

  public codigoPlanoContabilDebito(data) {
    return data.planoContabilDebito && data.planoContabilDebito.nome
  }
}
