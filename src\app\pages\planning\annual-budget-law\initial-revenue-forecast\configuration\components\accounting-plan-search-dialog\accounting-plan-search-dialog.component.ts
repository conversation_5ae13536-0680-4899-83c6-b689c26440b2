import { Component, Input, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { AccountingEventConfigSearchDialogComponent } from '../accounting-event-config-search-dialog/accounting-event-config-search-dialog.component'

@Component({
  selector: 'eqp-accounting-plan-search-dialog',
  templateUrl: './accounting-plan-search-dialog.component.html',
  styleUrls: ['./accounting-plan-search-dialog.component.scss'],
})
export class AccountingPlanSearchDialogComponent
  extends BaseTelasComponent
  implements OnInit
{
  @Input() pageTitle: string = 'Selecionar conta contábil'
  @Input() isMultiple: boolean = false
  @Input() uri: string = 'plano_contabil'
  @Input() filter: string
  loading: boolean

  selectedRowKeys = []

  dataSource: DataSource

  constructor(
    public router: Router,
    public menuService: MenuService,
    private crudService: CrudService,
    private dialogRef: NbDialogRef<AccountingEventConfigSearchDialogComponent>,
  ) {
    super(menuService, router)
    this.permissao(this.uri)
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  fetchGrid() {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltroComposto(
        'uuid',
        this.uri,
        10,
        this.filter,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  async confirm() {
    if (this.isMultiple) {
      const items = []
      const dataSource = await this.dataSource.store().load()
      this.selectedRowKeys.forEach(key => {
        const item = dataSource.find(item => item.uuid === key)
        items.push(item)
      })
      this.dialogRef.close(items)
    } else {
      this.dialogRef.close(this.selectedRowKeys[0])
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  onSelectionChanged(event: any) {
    this.selectedRowKeys = event.selectedRowsData
  }
}
