<eqp-nebular-dialog
	[dialogTitle]="pageTitle"
	[spinnerActive]="loading"
	[spinnerStatus]="'info'"
	[dialogSize]="'extra-large'"
  [formGroup]="model"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  (rightFirstButtonEmitter)="confirm()"
  [rightFirstButtonDisabled]="model.invalid"
>
  <div class="row">
    <div
      class="col col-12 col-sm-6 col-md-4 col-xl-3 mb-3"
      formGroupName="tipoVariacaoQualitativa"
    >
      <eqp-nebular-select
        [size]="'small'"
        [shape]="'rectangle'"
        label="Variação patrimonial"
        placeholder=""
        formControlName="uuid"
        [dataSource]="QualitativeVariationTypeData"
        displayExpr="nome"
        valueExpr="uuid"
        required="true"
      ></eqp-nebular-select>
    </div>
    <div class="col col-12 col-lg mb-3">
      <eqp-search-field
        label="Contábil *"
        formControlName="planoContabil"
        codeKey="codigoReduzido"
        [returnAllData]="true"
        dialogTitle="Plano contábil"
        codeLabel="Cód. Reduzido"
        searchColumnsType="accountingPlanColumns"
        uri="previsao_inicial_receita_evento/plano_contabil"
        [filter]="[['codigo','<','5000000000000000000'],'and',['analitica','=','S']]"
        nameLabel="código - descrição"
        messageNotFound="Plano contábil não encontrado."
        [multipleNames]="['codigo','nome']"
      ></eqp-search-field>
    </div>
  </div>
</eqp-nebular-dialog>
