import { Component, Input, OnInit } from '@angular/core'
import { Form<PERSON>uilder, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { AccountingPlanInterface } from '@pages/planning/multi-year-plan/interfaces/accounting-plan'
import DataSource from 'devextreme/data/data_source'
import { Observable } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { RevenuePlanAccountingEventInterface } from '../../interfaces/revenue-plan-accounting-event'
import { RevenuePlanAccountingEventService } from '../../services/revenue-plan-accounting-event.service'
import { AccountingPlanSearchDialogComponent } from '../accounting-plan-search-dialog/accounting-plan-search-dialog.component'

@Component({
  selector: 'eqp-revenue-plan-accounting-event-form',
  templateUrl: './revenue-plan-accounting-event-form.component.html',
  styleUrls: ['./revenue-plan-accounting-event-form.component.scss'],
})
export class RevenuePlanAccountingEventFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Contábil por Origem de Operação'
  public loading = false
  public QualitativeVariationTypeData: DataSource

  @Input() parentUuid: string
  @Input() revenuePlanAccountingEvent: RevenuePlanAccountingEventInterface

  constructor(
    public menuService: MenuService,
    public router: Router,
    private dialogRef: NbDialogRef<RevenuePlanAccountingEventFormComponent>,
    private builder: FormBuilder,
    private dialog: NbDialogService,
    private crudService: CrudService,
    private service: RevenuePlanAccountingEventService,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  ngOnInit(): void {
    this.model = this.getModelView()
    this.loadSelect()
    if (this.revenuePlanAccountingEvent) {
      this.model.patchValue({
        ...this.revenuePlanAccountingEvent,
        planoContabil: {
          ...this.revenuePlanAccountingEvent.planoContabil,
          nome: `${this.revenuePlanAccountingEvent.planoContabil.codigo} - ${this.revenuePlanAccountingEvent.planoContabil.nome}`,
        },
      })
    }
  }

  private getModelView() {
    return this.builder.group({
      uuid: [],
      tipoVariacaoQualitativa: this.builder.group({
        uuid: [undefined, [Validators.required]],
      }),
      planoContabil: [undefined, [Validators.required]],
    })
  }

  private loadSelect() {
    this.QualitativeVariationTypeData = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'previsao_inicial_receita_evento/tipo_variacao_qualitativa',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onSearchDialog() {
    this.dialog
      .open(AccountingPlanSearchDialogComponent, {
        context: {
          uri: 'previsao_inicial_receita_evento/plano_contabil',
          filter:
            '["codigo","<","5000000000000000000"],"and",["analitica","=","S"]',
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe((res: AccountingPlanInterface) => {
        if (res) {
          this.model.get('planoContabil').patchValue({
            ...res,
            nome: `${res.codigo} - ${res.nome}`,
          })
        }
      })
  }

  private prepare(dto: any) {
    return {
      uuid: dto.uuid ? dto.uuid : null,
      tipoVariacaoQualitativa: dto.tipoVariacaoQualitativa,
      planoContabil: {
        uuid: dto.planoContabil.uuid,
      },
    }
  }

  public confirm() {
    this.loading = true
    let obs$: Observable<any>
    if (this.revenuePlanAccountingEvent) {
      obs$ = this.service.put(
        this.parentUuid,
        this.prepare(this.model.getRawValue()),
        this.revenuePlanAccountingEvent.uuid,
      )
    } else {
      obs$ = this.service.post(
        this.parentUuid,
        this.prepare(this.model.getRawValue()),
      )
    }
    obs$
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: 'Conta contábil salva com sucesso.',
        })
        this.dialogRef.close(res?.body?.dados)
      })
  }

  public cancel() {
    this.dialogRef.close(null)
  }
}
