<eqp-loading *ngIf="loading"></eqp-loading>
<ng-container>
  <dx-data-grid
    id="revenuePlanAccountingEventGrid"
    [dataSource]="dataSource"
    [allowColumnResizing]="true"
    [columnAutoWidth]="true"
    [showColumnLines]="false"
    [showRowLines]="false"
    [showBorders]="false"
    [rowAlternationEnabled]="true"
    [wordWrapEnabled]="true"
    [loadPanel]="false"
    [columnHidingEnabled]="false"
    [remoteOperations]="true"
    keyExpr="uuid"
    [remoteOperations]="true"
    (onToolbarPreparing)="onToolbarPreparing($event)"
  >
    <dxo-state-storing
      [enabled]="true"
      type="custom"
      [customLoad]="loadState"
      [customSave]="saveState"
      savingTimeout="100"
    ></dxo-state-storing>
    <dxo-export
      [enabled]="true"
      [excelWrapTextEnabled]="true"
      [excelFilterEnabled]="true"
      [fileName]="pageTitle"
    ></dxo-export>

    <dxo-paging [pageSize]="10"></dxo-paging>
    <dxo-pager
      [showInfo]="true"
      [showNavigationButtons]="true"
      [showPageSizeSelector]="false"
    >
    </dxo-pager>

    <dxo-header-filter [visible]="false"> </dxo-header-filter>
    <dxo-filter-row [visible]="true"></dxo-filter-row>

    <dxo-sorting mode="multiple"></dxo-sorting>

    <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

    <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

    <dxo-search-panel [visible]="true" placeholder="Buscar"></dxo-search-panel>

    <dxo-editing
      mode="form"
      [allowUpdating]="false"
      [allowDeleting]="false"
      [allowAdding]="nivelPermissao === 'FULL'"
      [useIcons]="true"
    >
    </dxo-editing>

    <dxi-column
      dataField="tipoVariacaoQualitativa.nome"
      caption="Variação patrimonial"
    ></dxi-column>
    <dxi-column caption="Conta contábil" alignment="center">
      <dxi-column
        alignment="left"
        dataField="planoContabil.codigoReduzido"
        caption="Reduzido"
      ></dxi-column>
      <dxi-column
        dataField="planoContabil.codigo"
        caption="Código"
      ></dxi-column>
      <dxi-column dataField="planoContabil.nome" caption="Nome"></dxi-column>
    </dxi-column>

    <dxi-column
      dataField="uuid"
      caption=""
      [width]="80"
      [allowFiltering]="false"
      [allowSorting]="false"
      cellTemplate="acaoColumn"
    ></dxi-column>

    <div *dxTemplate="let data of 'acaoColumn'">
      <a
        title="Alterar"
        (click)="edit(data.data)"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
      ></a>
      <a
        *ngIf="nivelPermissao === 'FULL'"
        title="Remover"
        (click)="remove(data.value)"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
      ></a>
    </div>
  </dx-data-grid>
</ng-container>
