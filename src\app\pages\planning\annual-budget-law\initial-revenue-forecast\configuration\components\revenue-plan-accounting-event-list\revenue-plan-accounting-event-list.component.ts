import { Component, Input, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { RevenuePlanAccountingEventService } from '../../services/revenue-plan-accounting-event.service'
import { RevenuePlanAccountingEventFormComponent } from '../revenue-plan-accounting-event-form/revenue-plan-accounting-event-form.component'

@Component({
  selector: 'eqp-revenue-plan-accounting-event-list',
  templateUrl: './revenue-plan-accounting-event-list.component.html',
  styleUrls: ['./revenue-plan-accounting-event-list.component.scss'],
})
export class RevenuePlanAccountingEventListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Contábil'
  public loading = false
  public dataSource: DataSource

  @Input() parentUuid: string

  constructor(
    public menuService: MenuService,
    public router: Router,
    private crudService: CrudService,
    private dialog: NbDialogService,
    private service: RevenuePlanAccountingEventService,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
  }

  ngOnInit(): void {
    if (this.parentUuid) {
      this.fetchGrid()
    }
  }

  public fetchGrid() {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `previsao_inicial_receita_evento/${this.parentUuid}/contabil`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event) {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Contábil'
          item.options.hint = 'Nova conta contábil'
          item.options.onClick = () => this.new()
        }
      })
    }
  }

  private new() {
    this.dialog
      .open(RevenuePlanAccountingEventFormComponent, {
        context: {
          parentUuid: this.parentUuid,
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe(res => {
        if (res) {
          this.fetchGrid()
        }
      })
  }

  public edit(data: any) {
    this.dialog
      .open(RevenuePlanAccountingEventFormComponent, {
        context: {
          parentUuid: this.parentUuid,
          revenuePlanAccountingEvent: data,
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe(res => {
        if (res) {
          this.fetchGrid()
        }
      })
  }

  public remove(uuid: string) {
    this.dialog
      .open(ConfirmationComponent, {
        context: {
          confirmationContent: {
            body: 'Confirma a exclusão da configuração contábil da receita?',
          },
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe(res => {
        if (res) {
          this.loading = true
          this.service
            .delete(this.parentUuid, uuid)
            .pipe(
              first(),
              finalize(() => (this.loading = false)),
            )
            .subscribe(() => {
              this.toastr.send({
                success: true,
                message:
                  'Configuração contábil da receita apagada com sucesso.',
              })
              this.fetchGrid()
            })
        }
      })
  }
}
