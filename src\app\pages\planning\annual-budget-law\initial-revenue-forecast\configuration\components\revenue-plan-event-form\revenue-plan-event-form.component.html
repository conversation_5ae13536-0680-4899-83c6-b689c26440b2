<eqp-nebular-dialog
	[dialogTitle]="pageTitle"
	[spinnerActive]="loading"
	[spinnerStatus]="'info'"
	dialogSize="full"
>
	<nb-tabset>
		<nb-tab tabTitle="Evento" [formGroup]="model" [active]="activeInitialTab">
			<div class="row">
				<div class="col col-12 col-md-3 mb-3" formGroupName="origemOperacaoEvento">
					<eqp-nebular-select
						[size]="'small'"
						[shape]="'rectangle'"
						label="Origem de Operação"
						placeholder=""
						formControlName="uuid"
						[dataSource]="OperationEventOriginData"
						displayExpr="nome"
						valueExpr="uuid"
						required="true"
					></eqp-nebular-select>
				</div>
				<div class="col col-12 col-md-4">
					<eqp-nebular-search-field 
						codeKey="numero" 
						codeLabel="Número" 
						(onInputChange)="buscarEventoContabilConfigPorNumero($event)"
						(onButtonClick)="buscarEventoContabilConfigPorDialogo()"
						waitingTime="2000"
						primaryMask="99999999"
						label="Evento contábil"
						formControlName="evento">
					</eqp-nebular-search-field>
				</div>
				<div class="col col-12 col-md-5">
					<eqp-nebular-select 
						[size]="'small'" 
						[shape]="'rectangle'" 
						label="Configuração *"
						placeholder=""
						formControlName="eventoContabilConfig" 
						[dataSource]="configuracaoEventoContabilDados" 
						[displayExpr]="exibicaoPersonalizadaEventoContabil"
						valueExpr="uuid">
					</eqp-nebular-select>
				</div>
			</div>
			<ng-container formGroupName="eventoContabil">
				<nb-card>
					<nb-card-header>Débito</nb-card-header>
					<nb-card-body>
						<div class="row">
							<div class="col col-12 col-sm-6 col-md-3 col-xxl-2 mb-3" formGroupName="planoContabilDebito">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="codigoReduzido"
									label="Contábil"
									placeholder="reduzido"
									readonly="true"
								></eqp-nebular-input>
							</div>
							<div class="col col-12 col-md mb-3" formGroupName="planoContabilDebito">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="nome"
									label="Código - Descrição"
									placeholder=""
									readonly="true"
								></eqp-nebular-input>
							</div>
							<div class="col col-12 col-sm-6 col-md-3 col-xxl-2 mb-3" formGroupName="tipoFinanceiroPatrimonialDebito">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="nome"
									label="Superávit Financeiro"
									placeholder=""
									readonly="true"
								></eqp-nebular-input>
							</div>
						</div>
					</nb-card-body>
				</nb-card>
				<nb-card>
					<nb-card-header>Crédito</nb-card-header>
					<nb-card-body>
						<div class="row">
							<div class="col col-12 col-sm-6 col-md-3 col-xxl-2 mb-3" formGroupName="planoContabilCredito">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="codigoReduzido"
									label="Contábil"
									placeholder="reduzido"
									readonly="true"
								></eqp-nebular-input>
							</div>
							<div class="col col-12 col-md mb-3" formGroupName="planoContabilCredito">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="nome"
									label="Código - Descrição"
									placeholder=""
									readonly="true"
								></eqp-nebular-input>
							</div>
							<div class="col col-12 col-sm-6 col-md-3 col-xxl-2 mb-3" formGroupName="tipoFinanceiroPatrimonialCredito">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="nome"
									label="Superávit Financeiro"
									placeholder=""
									readonly="true"
								></eqp-nebular-input>
							</div>
						</div>
					</nb-card-body>
				</nb-card>
				<nb-card>
					<nb-card-body>
						<div class="row">
							<div class="col col-12 col-md-6 col-xxl-4 mb-3" formGroupName="tipoMovimentoContabil">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="nome"
									label="Tipo de movimento"
									placeholder=""
									readonly="true"
								></eqp-nebular-input>
							</div>
							<div class="col col-12 col-md-5 col-xxl-3 mb-3" formGroupName="tipoVariacaoQualitativa">
								<eqp-nebular-input
									[style]="'basic'"
									[size]="'small'"
									[shape]="'rectangle'"
									formControlName="nome"
									label="Variação patrimonial"
									placeholder=""
									readonly="true"
								></eqp-nebular-input>
							</div>
						</div>
					</nb-card-body>
				</nb-card>
			</ng-container>
			<div class="d-flex mt-2 justify-content-between" style="gap: 0.5rem">
				<button
					(click)="cancel()"
					class="btn btn-dark"
					type="button"
				>Voltar</button>
				<button
					(click)="confirm()"
					type="button"
					class="btn btn-success"
					[disabled]="model.invalid"
				>Confirmar</button>
			</div>
		</nb-tab>
		<nb-tab
			tabTitle="Contábil"
			[disabled]="!revenuePlanEvent?.uuid"
			[active]="!activeInitialTab"
		>
			<eqp-revenue-plan-accounting-event-list
				[parentUuid]="revenuePlanEvent?.uuid"
			></eqp-revenue-plan-accounting-event-list>
		</nb-tab>
	</nb-tabset>
</eqp-nebular-dialog>