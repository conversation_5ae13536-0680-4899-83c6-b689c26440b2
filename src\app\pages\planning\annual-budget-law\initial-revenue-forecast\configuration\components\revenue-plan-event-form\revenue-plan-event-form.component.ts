import { Component, Input, OnInit } from '@angular/core'
import { Form<PERSON>uilder, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { Observable, Subject } from 'rxjs'
import { distinctUntilChanged, filter, finalize, first, takeUntil, tap } from 'rxjs/operators'
import { RevenuePlanEventInterface } from '../../interfaces/revenue-plan-event'
import { RevenuePlanEventService } from '../../services/revenue-plan-event.service'
import { AccountingEventConfigSearchDialogComponent } from '../accounting-event-config-search-dialog/accounting-event-config-search-dialog.component'

@Component({
  selector: 'eqp-revenue-plan-event-form',
  templateUrl: './revenue-plan-event-form.component.html',
  styleUrls: ['./revenue-plan-event-form.component.scss'],
})
export class RevenuePlanEventFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Configuração Contábil da Receita'
  public loading = false
  public activeInitialTab = true

  public OperationEventOriginData: DataSource
  public configuracaoEventoContabilDados: DataSource = new DataSource([])

  @Input() parentUuid: string
  @Input() revenuePlanEvent: RevenuePlanEventInterface
  private cancelarObservables = new Subject()
  private habilitarSugestaoDeEvento: boolean = true

  constructor(
    public menuService: MenuService,
    public router: Router,
    private dialogRef: NbDialogRef<RevenuePlanEventFormComponent>,
    private dialog: NbDialogService,
    private builder: FormBuilder,
    private crudService: CrudService,
    private toastr: ToastrService,
    private service: RevenuePlanEventService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  ngOnInit(): void {
    this.model = this.getModelView()
    this.loadSelect()
    this.manipuladoresDeEventos()
    if (this.revenuePlanEvent) {
      this.habilitarSugestaoDeEvento = false
      this.loadModelView(this.revenuePlanEvent)
    }
  }

  public exibicaoPersonalizadaEventoContabil(data) {
    return data && `${data.numero} - Débito: ${data.planoContabilDebito.codigo} - Crédito: ${data.planoContabilCredito.codigo} `
  }

  private getModelView() {
    return this.builder.group({
      uuid: [],
      origemOperacaoEvento: this.builder.group({
        uuid: [undefined, [Validators.required]],
      }),
      eventoContabilConfig: [undefined, [Validators.required]],
      evento: [],
      eventoContabil: this.builder.group({
        planoContabilDebito: this.builder.group({
          codigoReduzido: [],
          nome: [],
        }),
        tipoFinanceiroPatrimonialDebito: this.builder.group({
          nome: [],
        }),
        planoContabilCredito: this.builder.group({
          codigoReduzido: [],
          nome: [],
        }),
        tipoFinanceiroPatrimonialCredito: this.builder.group({
          nome: [],
        }),
        tipoMovimentoContabil: this.builder.group({
          nome: [],
        }),
        tipoVariacaoQualitativa: this.builder.group({
          nome: [],
        }),
      }),
    })
  }

  private manipuladoresDeEventos() {
    this.model
      .get('eventoContabilConfig')
      .valueChanges.pipe(
        distinctUntilChanged(),
        takeUntil(this.cancelarObservables),
        filter((res) => res)
      )
      .subscribe(async (uuid: string) => {
        const eventoContabilConfig = await this.configuracaoEventoContabilDados.store().byKey(uuid)
        if (eventoContabilConfig) 
          this.preencherDadosConfiguracao(eventoContabilConfig)
      });
  }

  public buscarEventoContabilConfigPorDialogo() {
    this.dialog
      .open(AccountingEventConfigSearchDialogComponent, {
        context: {
          uri: 'previsao_inicial_receita/evento_contabil_config',
          filter: '["planoContabilDebito.codigo",">=","1000000000000000000"],"and",' +
          '["planoContabilCredito.codigo","<=","*******************"],"and",' +
          '["planoContabilCredito.codigo",">=","1000000000000000000"],"and",' +
          '["planoContabilCredito.codigo","<=","*******************"]]'
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe(res => {
        if (res) {
          this.model.get('evento').patchValue(res.eventoContabil)
          this.carregarDadosDaConfiguracao(res.eventoContabil?.numero, res.uuid)
        }
      })
  }

  private limpaCamposDeEvento() {
    this.model.get('eventoContabilConfig').reset();
    this.model.get('eventoContabil').reset();
    this.model.get('evento').reset()
  }

  public buscarEventoContabilConfigPorNumero(value: string) {
    if(value == '' || !value) {
      this.limpaCamposDeEvento()
      return
    }
    this.crudService
    .getDataSourceFiltroComposto(
      'uuid',
      'previsao_inicial_receita/evento_contabil_config',
      0,
      `[["eventoContabil.numero","=",${value}],"and",` +
      '["planoContabilDebito.codigo",">=","1000000000000000000"],"and",' +
      '["planoContabilCredito.codigo","<=","*******************"],"and",' +
      '["planoContabilCredito.codigo",">=","1000000000000000000"],"and",' +
      '["planoContabilCredito.codigo","<=","*******************"]]'
    )
    .load()
    .then(
      (res) => {
        this.loading = false
        if(res.length == 0){
          this.toastr.send({
            error: true,
            message: 'Evento contábil não encontrado.',
          });
          this.model.get('eventoContabilConfig').reset();
          this.model.get('eventoContabil').reset()
        } else {
          this.model.get('evento').patchValue(res[0].eventoContabil);
          this.carregarDadosDaConfiguracao(res[0].eventoContabil?.numero)
        }
        (err) => {
          this.loading = false;
          this.toastr.send({
            error: true,
            message: `${err}`,
          })
        }
      }
    )
  } 

  private async carregarDadosDaConfiguracao(numero: number, eventoContabilConfigUuid?: string) {
    this.configuracaoEventoContabilDados = new DataSource({
      store: this.crudService.getDataSourceFiltroComposto(
        'uuid',
        'previsao_inicial_receita/evento_contabil_config',
        0,
        `[["eventoContabil.numero","=",${numero}],"and",` +
        '["planoContabilDebito.codigo",">=","1000000000000000000"],"and",' +
        '["planoContabilCredito.codigo","<=","*******************"],"and",' +
        '["planoContabilCredito.codigo",">=","1000000000000000000"],"and",' +
        '["planoContabilCredito.codigo","<=","*******************"]]'
      ),
      paginate: true,
      pageSize: 10,
    });

    const eventoContabilConfig = await this.configuracaoEventoContabilDados.load()

    if(eventoContabilConfigUuid) {
      this.model.get('eventoContabilConfig').patchValue(eventoContabilConfig[0].uuid)
      return
    }

    if(eventoContabilConfig.length && this.habilitarSugestaoDeEvento) 
      this.model.get('eventoContabilConfig').patchValue(eventoContabilConfig[0].uuid)
  }

  private preencherDadosConfiguracao(data) {
    data.planoContabilDebito.nome = `${data.planoContabilDebito.codigo} - ${data.planoContabilDebito.nome}`
    data.planoContabilCredito.nome = `${data.planoContabilCredito.codigo} - ${data.planoContabilCredito.nome}`
    this.model.get('eventoContabil').patchValue(data, {emitEvent: false})
  }

  private loadSelect() {
    this.OperationEventOriginData = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'previsao_inicial_receita/origem_operacao_evento',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private loadModelView(dto: RevenuePlanEventInterface) {
    this.model.get('uuid').patchValue(dto.uuid)
    this.model.get('origemOperacaoEvento').patchValue(dto.origemOperacaoEvento)
    this.model.get('evento').patchValue(dto.eventoContabilConfig.eventoContabil)
    this.carregarDadosDaConfiguracao(dto.eventoContabilConfig.eventoContabil?.numero)
    this.model.get('eventoContabilConfig').patchValue(dto.eventoContabilConfig.uuid)
    dto.eventoContabilConfig.planoContabilDebito.nome = `${
      dto.eventoContabilConfig.planoContabilDebito.codigo
    } - ${
      dto.eventoContabilConfig.planoContabilDebito.nome
    }`
    dto.eventoContabilConfig.planoContabilCredito.nome = `${
      dto.eventoContabilConfig.planoContabilCredito.codigo
    } - ${
      dto.eventoContabilConfig.planoContabilCredito.nome
    }`
    this.model.get('eventoContabil').patchValue(dto.eventoContabilConfig)
    this.habilitarSugestaoDeEvento = true
  }

  private prepare(dto: any) {
    return {
      uuid: dto.uuid ? dto.uuid : null,
      origemOperacaoEvento: dto.origemOperacaoEvento,
      eventoContabilConfig: {
        uuid: dto.eventoContabilConfig,
      },
    }
  }

  public confirm() {
    this.loading = true
    let obs$: Observable<any>
    if (this.revenuePlanEvent) {
      obs$ = this.service.put(
        this.parentUuid,
        this.prepare(this.model.getRawValue()),
        this.revenuePlanEvent.uuid,
      )
    } else {
      obs$ = this.service.post(
        this.parentUuid,
        this.prepare(this.model.getRawValue()),
      )
    }
    obs$
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: 'Configuração contábil salva com sucesso.',
        })
        this.revenuePlanEvent = res?.body?.dados
        this.activeInitialTab = false
      })
  }

  public cancel() {
    this.dialogRef.close(null)
  }
}
