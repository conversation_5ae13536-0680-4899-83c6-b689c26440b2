<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update-initial-revenue-forecast-list'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="fetchGrid()"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonText]="'Voltar'"
  (bottomLeftButtonEmitter)="cancel()"
>
  <ng-container [formGroup]="model">
    <div class="row">
      <div class="col col-12 col-sm-6 col-md-5 col-xxl-3 mb-3">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="codigo"
          label="Conta"
          placeholder="Conta"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-7 col-xxl mb-3">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="nome"
          label="Nome"
          placeholder="Nome"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-sm-6 col-md-4 col-xxl-3 mb-3">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="operacao"
          label="Operação"
          placeholder="Operação"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </ng-container>
  <ng-container>
    <dx-data-grid
      id="fonteRecursoGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="false"
      [remoteOperations]="true"
      keyExpr="uuid"
      [remoteOperations]="true"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="origemOperacaoEvento.nome"
        caption="Origem da Operação"
      ></dxi-column>

      <dxi-column
        alignment="left"
        cellTemplate="eventoContabilConfigCodigoColuna"
        dataField="eventoContabilConfigUuid"
        caption="Evento contábil"
      >
        <dxo-lookup valueExpr="uuid" displayExpr="eventoContabil.numero" [dataSource]="eventoContabilConfigPorNumeroData"></dxo-lookup>
      </dxi-column>
      <div *dxTemplate="let data of 'eventoContabilConfigCodigoColuna'">
        {{eventoContabilConfigNumero(data.data)}}
      </div>

      <dxi-column
        alignment="left"
        cellTemplate="eventoContabilConfigNomeColuna"
        dataField="eventoContabilConfigUuid"
        caption=""
      >
        <dxo-lookup valueExpr="uuid" displayExpr="eventoContabil.nome" [dataSource]="eventoContabilConfigPorNomeData"></dxo-lookup>
      </dxi-column>
      <div *dxTemplate="let data of 'eventoContabilConfigNomeColuna'">
        {{eventoContabilConfigNome(data.data)}}
      </div>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="80"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          title="Alterar"
          (click)="edit(data.data)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        ></a>
        <a
          *ngIf="nivelPermissao === 'FULL'"
          title="Remover"
          (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        ></a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
