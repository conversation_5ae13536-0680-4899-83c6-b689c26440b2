import { Component, OnInit } from '@angular/core'
import { FormBuilder } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { InitialRevenueForecastInterface } from '../../../interfaces/initial-revenue-forecast'
import { InitialRevenueForecastService } from '../../../services/initial-revenue-forecast.service'
import { RevenuePlanEventInterface } from '../../interfaces/revenue-plan-event'
import { RevenuePlanEventService } from '../../services/revenue-plan-event.service'
import { RevenuePlanEventFormComponent } from '../revenue-plan-event-form/revenue-plan-event-form.component'

@Component({
  selector: 'eqp-revenue-plan-event-list',
  templateUrl: './revenue-plan-event-list.component.html',
  styleUrls: ['./revenue-plan-event-list.component.scss'],
})
export class RevenuePlanEventListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Configuração Contábil da Receita'
  public loading = false
  public dataSource: DataSource
  private uuid: string
  private initialRevenueForecast: InitialRevenueForecastInterface
  public eventoContabilConfigPorNomeData: any
  public eventoContabilConfigPorNumeroData: any

  constructor(
    public menuService: MenuService,
    public router: Router,
    private route: ActivatedRoute,
    private builder: FormBuilder,
    private dialog: NbDialogService,
    private initialRevenueForecastService: InitialRevenueForecastService,
    private crudService: CrudService,
    private service: RevenuePlanEventService,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  ngOnInit(): void {
    this.model = this.getModelView()
    const { uuid } = this.route.snapshot.params
    if (uuid) {
      this.uuid = uuid
      this.fetchGrid()
      this.loadPageData(uuid)
    }
  }

  public fetchGrid() {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        `previsao_inicial_receita/${this.uuid}/evento`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })

    this.eventoContabilConfigPorNomeData = {
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'evento_contabil_config/paginado',
        10
      ),
      paginate: true,
      pageSize: 10
    }

    this.eventoContabilConfigPorNumeroData = {
      store: this.crudService.getDataSource(
        'uuid',
        'evento_contabil_config/paginado',
        10
      ),
      paginate: true,
      pageSize: 10
    }
  }

  private getModelView() {
    return this.builder.group({
      uuid: [],
      codigo: [],
      nome: [],
      operacao: [],
      valorPrevisto: [],
    })
  }

  private loadPageData(uuid: string) {
    this.loading = true
    this.initialRevenueForecastService
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.initialRevenueForecast = res.dados
        this.loadFormView(res.dados)
      })
  }

  private loadFormView(
    initialRevenueForecast: InitialRevenueForecastInterface,
  ) {
    this.model.patchValue({
      codigo: initialRevenueForecast.planoReceita?.codigo,
      nome: initialRevenueForecast.planoReceita?.nome,
      operacao: initialRevenueForecast.tipoOperacaoReceita?.nome,
      valorPrevisto: initialRevenueForecast.valorPrevisto,
    })
  }

  public onToolbarPreparing(event) {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Configuração'
          item.options.hint = 'Nova configuração contábil da receita'
          item.options.onClick = () => this.new()
        }
      })
    }
  }

  private new() {
    this.dialog
      .open(RevenuePlanEventFormComponent, {
        context: {
          parentUuid: this.uuid,
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe(() => {
        this.fetchGrid()
      })
  }

  public edit(data: RevenuePlanEventInterface) {
    this.dialog
      .open(RevenuePlanEventFormComponent, {
        context: {
          parentUuid: this.uuid,
          revenuePlanEvent: data,
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe(() => {
        this.fetchGrid()
      })
  }

  public remove(uuid: string) {
    this.dialog
      .open(ConfirmationComponent, {
        context: {
          confirmationContent: {
            body: 'Confirma a exclusão da configuração contábil da receita?',
          },
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      .onClose.pipe(first())
      .subscribe(res => {
        if (res) {
          this.loading = true
          this.service
            .delete(this.uuid, uuid)
            .pipe(
              first(),
              finalize(() => (this.loading = false)),
            )
            .subscribe(() => {
              this.toastr.send({
                success: true,
                message:
                  'Configuração contábil da receita apagada com sucesso.',
              })
              this.fetchGrid()
            })
        }
      })
  }

  public cancel() {
    this.router.navigate([
      'lei-orcamentaria-anual',
      'receita',
      'previsao-inicial',
      'edit',
      this.initialRevenueForecast.planoReceita.uuid,
    ])
  }

  public eventoContabilConfigNumero(data) {
    return data.eventoContabilConfig && data.eventoContabilConfig.eventoContabil.numero
  }

  public eventoContabilConfigNome(data) {
    return data.eventoContabilConfig && data.eventoContabilConfig.eventoContabil.nome
  }
}
