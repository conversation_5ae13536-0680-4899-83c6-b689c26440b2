import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ConfigurationComponent } from './configuration.component';
import { RevenuePlanEventListComponent } from './components/revenue-plan-event-list/revenue-plan-event-list.component';

const routes: Routes = [
	{
		path: '',
		component: ConfigurationComponent,
		children: [
			{
				path: '',
				component: RevenuePlanEventListComponent,
			}
		]
	}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ConfigurationRoutingModule { }
