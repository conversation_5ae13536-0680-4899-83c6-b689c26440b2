import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ConfigurationRoutingModule } from './configuration-routing.module';
import { ConfigurationComponent } from './configuration.component';
import { RevenuePlanEventListComponent } from './components/revenue-plan-event-list/revenue-plan-event-list.component';
import { CommonToolsModule } from '@common/common-tools.module';
import { RevenuePlanEventFormComponent } from './components/revenue-plan-event-form/revenue-plan-event-form.component';
import { AccountingEventConfigSearchDialogComponent } from './components/accounting-event-config-search-dialog/accounting-event-config-search-dialog.component';
import { RevenuePlanAccountingEventListComponent } from './components/revenue-plan-accounting-event-list/revenue-plan-accounting-event-list.component';
import { RevenuePlanAccountingEventFormComponent } from './components/revenue-plan-accounting-event-form/revenue-plan-accounting-event-form.component';
import { AccountingPlanSearchDialogComponent } from './components/accounting-plan-search-dialog/accounting-plan-search-dialog.component';


@NgModule({
  declarations: [
    ConfigurationComponent,
    RevenuePlanEventListComponent,
    RevenuePlanEventFormComponent,
    AccountingEventConfigSearchDialogComponent,
    RevenuePlanAccountingEventListComponent,
    RevenuePlanAccountingEventFormComponent,
    AccountingPlanSearchDialogComponent
  ],
  imports: [
    CommonModule,
		CommonToolsModule,
    ConfigurationRoutingModule
  ]
})
export class ConfigurationModule { }
