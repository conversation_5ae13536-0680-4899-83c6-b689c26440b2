import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseChildService } from '@common/services/base/base-child.service';
import { RevenuePlanAccountingEventInterface } from '../interfaces/revenue-plan-accounting-event';

@Injectable({
  providedIn: 'root'
})
export class RevenuePlanAccountingEventService
	extends BaseChildService<RevenuePlanAccountingEventInterface>
{
  constructor(protected http: HttpClient) {
		super(http, 'previsao_inicial_receita_evento', 'contabil')
	}
}
