import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseChildService } from '@common/services/base/base-child.service';
import { RevenuePlanEventInterface } from '../interfaces/revenue-plan-event';

@Injectable({
  providedIn: 'root'
})
export class RevenuePlanEventService
	extends BaseChildService<RevenuePlanEventInterface>
{
  constructor(protected http: HttpClient) {
		super(http, 'previsao_inicial_receita', 'evento')
	}
}
