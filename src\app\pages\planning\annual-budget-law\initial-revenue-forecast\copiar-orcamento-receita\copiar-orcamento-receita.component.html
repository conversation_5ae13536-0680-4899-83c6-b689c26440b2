<eqp-nebular-dialog
  [dialogTitle]="'Copiar Orçamento de Receita'"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonId]="'voltar-copiar-orcamento'"
  [bottomLeftButtonTitle]="'Voltar'"
  (bottomLeftButtonEmitter)="cancelar()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonId]="'confirmar-copiar-orcamento'"
  [rightFirstButtonDisabled]="form.invalid"
  (rightFirstButtonEmitter)="confirmar()"
  [dialogSize]="'medium'"
>
  <form [formGroup]="form">
    <nb-card>
      <nb-card-header>Configurações</nb-card-header>
      <nb-card-body>
        <div class="row mb-3">
          <div class="col-md-6 col-sm-12">
            <eqp-nebular-checkbox
              [style]="'basic'"
              name="copiarExercicioAnterior"
              label="Copiar valor do exercício anterior"
              formControlName="exercicioAnterior"
            >
            </eqp-nebular-checkbox>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 col-sm-12">
            <eqp-nebular-input
              [style]="'currency'"
              [size]="'small'"
              [shape]="'rectangle'"
              [options]="{ prefix: '', thousands: '.', decimal: ',', suffix: ' %' }"
              name="correcao"
              label="Aplicar % de correção"
              placeholder=""
              formControlName="correcao"
            >
            </eqp-nebular-input>
            <small class="text-muted">Permitido de 0 a 100%</small>
          </div>
        </div>
      </nb-card-body>
    </nb-card>

    <nb-card>
      <nb-card-header>Conta de Receita</nb-card-header>
      <nb-card-body>
        <div class="row mb-3">
          <div class="col-md-12 col-sm-12">
            <eqp-search-field
              label="Conta de receita"
              [uri]="'plano_receita/paginado'"
              [searchColumnsType]="'revenuePlanColumns'"
              [dialogTitle]="'Conta de Receita'"
              formControlName="planoReceitaUuid"
              messageNotFound="Conta de receita não encontrada."
              [waitingTime]="2000"
              primaryMask="0.0.0.0.00.0.0.00.00.00.00.00"
            ></eqp-search-field>
          </div>
        </div>
        <div class="row mb-4 justify-content-center">
          <div class="col-md-3 col-sm-12 mx-2">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [type]="'number'"
              [shape]="'rectangle'"
              name="categoria"
              label="Categoria"
              placeholder=""
              formControlName="categoria"
            >
            </eqp-nebular-input>
          </div>
          <div class="col-md-3 col-sm-12 mx-2">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [type]="'number'"
              [shape]="'rectangle'"
              name="origem"
              label="Origem"
              placeholder=""
              formControlName="origem"
            >
            </eqp-nebular-input>
          </div>
          <div class="col-md-3 col-sm-12 mx-2">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [type]="'number'"
              [shape]="'rectangle'"
              name="especie"
              label="Espécie"
              placeholder=""
              formControlName="especie"
            >
            </eqp-nebular-input>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-12 col-sm-12">
            <eqp-search-field
              label="Fonte de recurso"
              [uri]="'fonte_recurso_combinacao/paginado'"
              [searchColumnsType]="'codeNameColumns'"
              [dialogTitle]="'Fontes de Recurso'"
              messageNotFound="Fonte de recurso não encontrada."
              formControlName="fonteRecursoUuid"
              waitingTime="1000"
            ></eqp-search-field>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </form>
</eqp-nebular-dialog>
