import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { Subject } from 'rxjs'
import { filter, finalize, takeUntil } from 'rxjs/operators'
import { CopiarOrcamentoReceitaService } from './copiar-orcamento-receita.service'

@Component({
  selector: 'eqp-copiar-orcamento-receita',
  templateUrl: './copiar-orcamento-receita.component.html',
  styleUrls: ['./copiar-orcamento-receita.component.scss'],
})
export class CopiarOrcamentoReceitaComponent implements OnInit, OnDestroy {
  public loading = false
  public form: FormGroup

  // Dados para os selects
  public categorias: any[] = []
  public origens: any[] = []
  public especies: any[] = []

  private destroy$ = new Subject<void>()

  constructor(
    private formBuilder: FormBuilder,
    private service: CopiarOrcamentoReceitaService,
    private toastr: ToastrService,
    protected dialogRef: NbDialogRef<CopiarOrcamentoReceitaComponent>,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    this.createForm()
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  private createForm(): void {
    this.form = this.formBuilder.group({
      exercicioAnterior: [false],
      correcao: [
        0,
        [Validators.min(0), Validators.max(100)],
      ],
      categoria: ['', [Validators.min(0), Validators.max(9)]],
      origem: ['', [Validators.min(0), Validators.max(9)]],
      especie: ['', [Validators.min(0), Validators.max(9)]],
      fonteRecursoUuid: [null],
      planoReceitaUuid: [null],
    })
  }

  public confirmar(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched()
      return
    }

    // Exibir mensagem de confirmação usando o ConfirmationComponent
    const dialogRef = this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          body: 'Atenção: os valores atuais do exercício serão atualizados. Deseja continuar?',
          cancelText: 'Não',
          cancelTitle: 'Não',
          confirmText: 'Sim',
          confirmTitle: 'Sim',
        },
        dialogSize: 'medium',
        exiberIcones: false,
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })

    dialogRef.onClose
      .pipe(
        filter(result => result === true),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.loading = true

        const dto = this.prepareDto()

        this.service
          .copiarOrcamentoReceita(dto)
          .pipe(
            takeUntil(this.destroy$),
            finalize(() => (this.loading = false)),
          )
          .subscribe({
            next: () => {
              this.toastr.send({
                success: true,
                message: 'Orçamento de receita copiado com sucesso.',
              })
              this.dialogRef.close(true)
            },
          })
      })
  }


  private prepareDto(): any {
    const formValue = this.form.getRawValue()

    return {
      ...formValue,
      exercicioAnterior: formValue.exercicioAnterior,
      correcao: formValue.correcao,
      categoria: formValue.categoria,
      origem: formValue.origem,
      especie: formValue.especie,
    }
  }

  public cancelar(): void {
    this.dialogRef.close(false)
  }
}
