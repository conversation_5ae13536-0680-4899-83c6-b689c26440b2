import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import DataSource from 'devextreme/data/data_source';
import CustomStore from 'devextreme/data/custom_store';

@Injectable({
  providedIn: 'root'
})
export class CopiarOrcamentoReceitaService {
  constructor(private http: HttpClient) { }

  // Métodos de obtenção de categorias, origens e espécies foram removidos
  // pois agora são campos numéricos

  /**
   * Copia o orçamento de receita
   * @param dto Dados para cópia do orçamento
   */
  public copiarOrcamentoReceita(dto: any): Observable<any> {
    return this.http.put<any>('previsao_inicial_receita/copiar_valores_orcamento', dto);
  }

}
