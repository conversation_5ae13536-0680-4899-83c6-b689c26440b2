<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col col-md-6">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo de operação da receita *"
          placeholder="Tipo de operação da receita"
          formControlName="tipoOperacaoReceita"
          [dataSource]="operationTypeData"
          [displayExpr]="'nome'"
          valueExpr="uuid"
          [disabled]="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col col-md-6">
        <eqp-nebular-input
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="valorPrevisto"
          label="Valor previsto*"
          placeholder="Valor previsto"
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [readonly]="exercicioStatusId !== 2"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="!model.valid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
