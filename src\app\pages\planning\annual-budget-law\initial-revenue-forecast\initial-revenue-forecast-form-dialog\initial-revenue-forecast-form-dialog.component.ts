import { Component, Input, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan'
import { OperationRevenueTypeService } from '@pages/planning/shared/services/operation-revenue-type.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, take } from 'rxjs/operators'
import { ProjectActivityService } from '../../expense/project-activity/project-activity.service'
import { InitialRevenueForecastInterface } from '../interfaces/initial-revenue-forecast'
import { OperationRevenueTypeInterface } from '../interfaces/operation-revenue-type'

@Component({
  selector: 'eqp-initial-revenue-forecast-form-dialog',
  templateUrl: './initial-revenue-forecast-form-dialog.component.html',
  styleUrls: ['./initial-revenue-forecast-form-dialog.component.scss'],
})
export class InitialRevenueForecastFormDialogComponent
  extends BaseTelasComponent
  implements OnInit
{
  pageTitle: string = 'Previsão inicial da receita - Operação'
  loading: boolean
  model: FormGroup
  operationTypeData: DataSource<OperationRevenueTypeInterface, string>
  exercicioStatusId: number

  @Input() initialData: InitialRevenueForecastInterface
  @Input() revenuePlan: RevenuePlanInterface

  constructor(
    public router: Router,
    public menuService: MenuService,
    private operationRevenueTypeService: OperationRevenueTypeService,
    private dialogRef: NbDialogRef<InitialRevenueForecastFormDialogComponent>,
    private builder: FormBuilder,
    private projectService: ProjectActivityService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  get uuid() {
    return this.model.get('uuid')?.value
  }

  ngOnInit(): void {
    this.initializeSelectData()
    this.loadProjectStatus()
    this.model = this.getNewModel()
    if (this.initialData) {
      this.loadForm(this.initialData)
    }
  }

  private loadProjectStatus() {
    this.loading = true
    this.projectService
      .getExercicioStatus()
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.exercicioStatusId = res?.dados?.exercicioStatus?.id
        },
      )
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      tipoOperacaoReceita: [undefined, [Validators.required]],
      valorPrevisto: [0],
      planoReceita: [],
    })
  }

  private initializeSelectData() {
    this.operationTypeData = new DataSource({
      store: this.operationRevenueTypeService.getDataSourceFiltro(
        'uuid',
        'tipo_operacao_receita/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())
      dto.planoReceita = this.revenuePlan
      this.dialogRef.close(dto)
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  private loadForm(initialData: InitialRevenueForecastInterface) {
    let dto = {
      ...initialData,
      tipoOperacaoReceita: initialData.tipoOperacaoReceita.uuid,
    }
    this.model.patchValue(dto)
  }

  private prepare(formData: any) {
    let dto: InitialRevenueForecastInterface = {
      ...formData,
      tipoOperacaoReceita: { uuid: formData.tipoOperacaoReceita },
    }
    return dto
  }
}
