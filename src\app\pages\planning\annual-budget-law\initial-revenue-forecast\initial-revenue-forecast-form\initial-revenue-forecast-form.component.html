<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancelar()"
  bottomLeftButtonId="initial-revenue-forecast-return-button"
>
  <ng-container>
    <div class="row" [formGroup]="model">
      <div class="col col-md-6">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="codigo"
          label="Código"
          placeholder="Código"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-md-6">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="nome"
          label="Nome"
          placeholder="Nome"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <dx-data-grid
          id="initialRevenueForecastOperationGrid"
          [dataSource]="data"
          [allowColumnResizing]="true"
          [columnAutoWidth]="true"
          [nbSpinner]="loading"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [rowAlternationEnabled]="true"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          [columnHidingEnabled]="false"
          [remoteOperations]="true"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparing($event)"
          class="mt-3"
        >
          <dxo-export
            [enabled]="true"
            [excelWrapTextEnabled]="true"
            [excelFilterEnabled]="true"
            [fileName]="pageTitle"
          ></dxo-export>

          <dxo-paging [pageSize]="10"></dxo-paging>
          <dxo-pager
            [showInfo]="true"
            [showNavigationButtons]="true"
            [showPageSizeSelector]="false"
          >
          </dxo-pager>

          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

          <dxo-group-panel
            [visible]="false"
            [emptyPanelText]="''"
          ></dxo-group-panel>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar tipo de operação"
          ></dxo-search-panel>

          <dxo-editing
            mode="cell"
            [allowUpdating]="true"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            dataField="tipoOperacaoReceita.codigo"
            [allowEditing]="false"
            caption="Código"
          ></dxi-column>

          <dxi-column
            dataField="tipoOperacaoReceita.nome"
            [allowEditing]="false"
            caption="Nome"
          ></dxi-column>

          <dxi-column
            dataField="valorPrevisto"
            [allowEditing]="false"
            caption="Valor previsto"
            [editorOptions]="{ placeholder: '00' }"
            [format]="currencyFormat"
          ></dxi-column>

          <dxi-column
            dataField="uuid"
            caption=""
            [width]="120"
            [allowFiltering]="false"
            [allowEditing]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <div class="w-100 d-flex justify-content-end">
              <a
                title="Dados relacionados"
                (click)="openRelationData(data.value)"
                class="dx-link dx-link-edit fas fa-folder dx-link-icon btn-icon-grid"
              >
              </a>
              <a
                title="Alterar"
                (click)="edit(data.value)"
                class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
              >
              </a>
              <a
                *ngIf="nivelPermissao === 'FULL'"
                title="Remover"
                (click)="remove(data.value)"
                class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
              >
              </a>
              <a
                title="Configuração Contábil"
                (click)="config(data.value)"
                class="dx-link dx-link-edit fas ion-gear-b dx-link-icon btn-icon-grid"
              >
              </a>
            </div>
          </div>
        </dx-data-grid>
      </div>
    </div>
    <div class="row flex-column mt-3">
      <div class="col text-right">
        <small
          ><strong>Receita:</strong>
          {{ totalReceita | currency : 'BRL' : true }}</small
        >
      </div>
      <div class="col text-right">
        <small
          ><strong>Outras deduções:</strong>
          {{ totalOutrasDeducoes | currency : 'BRL' : true }}</small
        >
      </div>
      <div class="col text-right">
        <small
          ><strong>Total previsto:</strong>
          {{
            totalReceita - totalOutrasDeducoes | currency : 'BRL' : true
          }}</small
        >
      </div>
    </div>
  </ng-container>
</eqp-standard-page>
