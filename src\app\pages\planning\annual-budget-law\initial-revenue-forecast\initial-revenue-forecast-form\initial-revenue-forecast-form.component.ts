import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import { Subscription, forkJoin } from 'rxjs'
import { filter, finalize, take } from 'rxjs/operators'
import { InitialRevenueForecastFormDialogComponent } from '../initial-revenue-forecast-form-dialog/initial-revenue-forecast-form-dialog.component'
import { InitialRevenueForecastInterface } from '../interfaces/initial-revenue-forecast'
import { InitialRevenueForecastService } from '../services/initial-revenue-forecast.service'
import { RevenuePlanInterface } from './../../../multi-year-plan/interfaces/revenue-plan'
import { RevenuePlanService } from './../../../multi-year-plan/services/revenue-plan.service'

@Component({
  selector: 'eqp-initial-revenue-forecast-form',
  templateUrl: './initial-revenue-forecast-form.component.html',
  styleUrls: ['./initial-revenue-forecast-form.component.scss'],
})
export class InitialRevenueForecastFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle: string = 'Previsão inicial da receita'
  public loading: boolean = false
  public columns: DxColumnInterface[] = []
  private subscription: Subscription

  model: FormGroup
  data: InitialRevenueForecastInterface[] = []
  parentUuid: string

  totalReceita: number = 0
  totalOutrasDeducoes: number = 0

  revenuePlan: RevenuePlanInterface

  currencyFormat = currencyFormat

  constructor(
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    private service: InitialRevenueForecastService,
    private revenuePlanService: RevenuePlanService,
    private route: ActivatedRoute,
    private builder: FormBuilder,
    public router: Router,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('ajustar')
  }

  ngOnInit(): void {
    const { uuid } = this.route.snapshot.params
    this.parentUuid = uuid
    this.model = this.getModelView()
    this.loadPageData()
  }

  loadPageData() {
    this.loading = true
    forkJoin([
      this.revenuePlanService.getInitialForecast(this.parentUuid),
      this.revenuePlanService.getIndividual(this.parentUuid),
    ])
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        ([revenueForecast, revenuePlan]) => {
          this.data = revenueForecast.dados
          this.revenuePlan = revenuePlan.dados
          if (this.revenuePlan.analitica !== 'S') {
            this.toastr.send({
              title: 'Erro',
              error: true,
              message: 'A conta deve ser analítica!',
            })
            this.router.navigate([
              'lei-orcamentaria-anual',
              'receita',
              'previsao-inicial',
            ])
          } else {
            this.calculateTotals(this.data)
            this.model.patchValue(this.revenuePlan)
          }
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  private getModelView(): FormGroup {
    return this.builder.group({
      codigo: [],
      nome: [],
    })
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Tipo de operação'
          item.options.hint = 'Tipo de operação'
          item.options.onClick = () => this.openRevenueOperationDialog()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Tipo de operação removido com sucesso',
              })
              const index = this.data.findIndex(item => item.uuid === uuid)
              if (index >= 0) this.data.splice(index, 1)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  private openRevenueOperationDialog(
    initialData?: InitialRevenueForecastInterface,
  ) {
    const dialogRef = this.dialogService.open(
      InitialRevenueForecastFormDialogComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.componentRef.instance.initialData = initialData
    dialogRef.componentRef.instance.revenuePlan = this.revenuePlan
    dialogRef.onClose.pipe(filter(res => res)).subscribe(res => {
      if (!res.uuid) {
        const index = this.data.findIndex(
          item => item.tipoOperacaoReceita.uuid == res.tipoOperacaoReceita.uuid,
        )
        if (index < 0) {
          this.loading = true
          this.service
            .post(res)
            .pipe(
              take(1),
              finalize(() => (this.loading = false)),
            )
            .subscribe(
              _ => {
                this.toastr.send({
                  title: 'Sucesso',
                  success: true,
                  message: 'Tipo de operação associado com sucesso',
                })
                this.loadPageData()
              },
              (resp: any) => this.toastr.bulkSend(resp.mensagens),
            )
        } else {
          this.toastr.send({
            title: 'Informação',
            info: true,
            message: 'Tipo de operação já associado',
          })
        }
      } else {
        this.loading = true
        this.service
          .put(res)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Tipo de operação atualizado com sucesso',
              })
              this.loadPageData()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  calculateTotals(items: InitialRevenueForecastInterface[]) {
    let receita = 0
    let outrasDeducoes = 0
    items.forEach(item => {
      if (item.tipoOperacaoReceita.codigo === 1) {
        receita += item.valorPrevisto
      } else {
        outrasDeducoes += item.valorPrevisto
      }
    })
    this.totalReceita = receita
    this.totalOutrasDeducoes = outrasDeducoes
  }

  edit(uuid: string) {
    const data = this.data.find(item => item.uuid === uuid)
    this.openRevenueOperationDialog(data)
  }

  config(uuid: string) {
    this.router.navigate([
      'lei-orcamentaria-anual',
      'receita',
      'previsao-inicial',
      'configuracao',
      uuid,
    ])
  }

  openRelationData(uuid: string) {
    this.router.navigate([
      'lei-orcamentaria-anual',
      'receita',
      'previsao-inicial',
      'operacao',
      uuid,
    ])
  }

  cancelar() {
    this.gravarParametros()
    this.router.navigate([
      'lei-orcamentaria-anual',
      'receita',
      'previsao-inicial',
    ])
  }
}
