import { Component, OnInit } from '@angular/core'
import { FormControl } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { MenuService } from '@pages/menu.service'
import { RevenuePlanService } from '@pages/planning/multi-year-plan/services/revenue-plan.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import { NbDialogService } from '@nebular/theme'
import { CopiarOrcamentoReceitaComponent } from '../copiar-orcamento-receita/copiar-orcamento-receita.component'
import DataSource from 'devextreme/data/data_source'
import { Subject, Subscription } from 'rxjs'
import { takeUntil } from 'rxjs/operators'

@Component({
  selector: 'eqp-initial-revenue-forecast-list',
  templateUrl: './initial-revenue-forecast-list.component.html',
  styleUrls: ['./initial-revenue-forecast-list.component.scss'],
})
export class InitialRevenueForecastListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle: string = 'Previsão inicial da receita '
  public loading: boolean = false
  public dataSource: DataSource
  public columns: DxColumnInterface[] = []
  private subscription: Subscription
  public toggleValorPrevisto = new FormControl('N')
  private destroy$ = new Subject<void>()

  currencyFormat = currencyFormat

  constructor(
    private service: RevenuePlanService,
    public router: Router,
    public menuService: MenuService,
    private dialogService: NbDialogService
  ) {
    super(menuService, router)
    this.permissao('/lei-orcamentaria-anual/receita/previsao-inicial')
  }

  public ngOnInit(): void {
    this.fetchGrid()
    this.somenteContasValorPrevisto()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
    this.destroy$.next()
    this.destroy$.complete()
  }

  private somenteContasValorPrevisto(): void {
    this.toggleValorPrevisto.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((valor) => {
        const filter = valor === 'S'
          ? '?valorPrevistoMaiorQueZero=true'
          : null;
        this.fetchGrid(filter)
      });
  }

  public fetchGrid(filtro: string = ''): void {
    let uri = 'previsao_inicial_receita/plano_receita'

    if(filtro)
      uri += filtro

    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        uri,
        10,
      ),
      filter: [['analitica', '=', 'S']],
      sort: [
        {
          selector: 'codigo',
          desc: false,
        },
      ],
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Conta'
          item.options.hint = 'Conta'
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public edit(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([
      `lei-orcamentaria-anual/receita/previsao-inicial/edit/${uuid}`,
    ])
  }

  /**
   * Abre o modal para copiar orçamento de receita
   */
  public abrirModalCopiarOrcamento(): void {
    const dialogRef = this.dialogService.open(CopiarOrcamentoReceitaComponent, {
      closeOnBackdropClick: false,
      closeOnEsc: false,
      hasScroll: true
    });

    dialogRef.onClose.subscribe(result => {
      if (result) {
        // Recarregar a grid após a cópia do orçamento
        this.fetchGrid();
      }
    });
  }
}
