import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan'
import { OperationRevenueTypeInterface } from './operation-revenue-type'
import { ResourceSourceInitialForecastInterface } from './resource-source-initial-forecast'

export interface InitialRevenueForecastInterface {
  uuid?: string
  tipoOperacaoReceita?: OperationRevenueTypeInterface //Não permitir duplicidade por plano
  valorPrevisto?: number //Valor para calculo
  fontesRecurso?: ResourceSourceInitialForecastInterface[] //[BACK] Gerar fontes de recurso com calculos conforme cadastro
  planoReceita: RevenuePlanInterface
}
