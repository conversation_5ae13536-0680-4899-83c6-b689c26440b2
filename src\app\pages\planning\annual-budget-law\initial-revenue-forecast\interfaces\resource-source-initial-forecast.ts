import { ResourceSourceCombinationInterface } from './../../../resource-source/interfaces/resource-source-combination';

export interface ResourceSourceInitialForecastInterface {
  uuid: string
  fonteRecurso: ResourceSourceCombinationInterface //[BACK] Trazer do relacionamento definido no plano da conta
  percentual: number //[BACK] Trazer do relacionamento definido na conta
  valorPrevisto: number //[BACK] se flagTemRateio calcular com base no percentual, caso contrário permitir qualquer valor
}
