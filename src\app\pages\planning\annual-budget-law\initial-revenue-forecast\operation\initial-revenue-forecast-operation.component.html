<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  bottomLeftButtonId="initial-revenue-forecast-return-button"
>
  <ng-container [formGroup]="model">
    <div class="row">
      <div class="col col-md-5">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="codigo"
          label="Conta"
          placeholder="Conta"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-md-7">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="nome"
          label="Nome"
          placeholder="Nome"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col col-md-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="operacao"
          label="Operação"
          placeholder="Operação"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-md-4">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="valorPrevisto"
          label="Valor previsto"
          placeholder="Valor previsto"
          [disabled]="true"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </ng-container>
  <div class="row mt-2">
    <div class="col">
      <nb-tabset (changeTab)="onChangeTab($event)">
        <nb-tab
          [disabled]="!uuid"
          tabId="resource-source"
          tabTitle="Fontes recurso"
          class="p-0"
        >
          <ng-container *ngIf="uuid && initialRevenueForecast">
            <eqp-initial-revenue-forecast-source-list
              [parentUuid]="uuid"
              [enablePercentage]="
                initialRevenueForecast?.planoReceita?.flagTemRateio == 'S'
              "
              [valorPrevisto]="initialRevenueForecast?.valorPrevisto"
            ></eqp-initial-revenue-forecast-source-list>
          </ng-container>
        </nb-tab>
        <nb-tab
          [disabled]="!uuid"
          tabId="financial-programing"
          tabTitle="Programação financeira"
          class="p-0"
        >
          <ng-container *ngIf="uuid && tabWasTouched('financial-programing')">
            <eqp-revenue-financial-programing-list
              [parentUuid]="uuid"
              [valorPrevisto]="initialRevenueForecast?.valorPrevisto"
            ></eqp-revenue-financial-programing-list>
          </ng-container>
        </nb-tab>
        <nb-tab
          [disabled]="!uuid"
          tabId="revenue-reestimation"
          tabTitle="Reestimativa"
        >
          <ng-container *ngIf="uuid && initialRevenueForecast && tabWasTouched('revenue-reestimation')">
            <eqp-revenue-reestimation-list
              [parentUuid]="uuid"
              [revenuePlanUuid]="initialRevenueForecast?.planoReceita?.uuid"
            ></eqp-revenue-reestimation-list>
          </ng-container>
        </nb-tab>
      </nb-tabset>
    </div>
  </div>
</eqp-standard-page>
