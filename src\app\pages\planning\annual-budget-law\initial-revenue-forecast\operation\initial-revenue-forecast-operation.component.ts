import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import { forkJoin } from 'rxjs'
import { finalize, take } from 'rxjs/operators'
import { InitialRevenueForecastService } from '../services/initial-revenue-forecast.service'
import { InitialRevenueForecastInterface } from './../interfaces/initial-revenue-forecast'
@Component({
  selector: 'eqp-initial-revenue-forecast-operation',
  templateUrl: './initial-revenue-forecast-operation.component.html',
  styleUrls: ['./initial-revenue-forecast-operation.component.scss'],
})
export class InitialRevenueForecastOperationComponent implements OnInit {
  public pageTitle: string = 'Previsão inicial da receita - Operação'
  public loading: boolean = false

  model: FormGroup
  uuid: string
  currencyFormat = currencyFormat

  initialRevenueForecast: InitialRevenueForecastInterface
  touchedTabs: Set<string> = new Set<string>([])

  constructor(
    private toastr: ToastrService,
    private service: InitialRevenueForecastService,
    private route: ActivatedRoute,
    private router: Router,
    private builder: FormBuilder,
  ) {}

  ngOnInit(): void {
    const { uuid } = this.route.snapshot.params
    this.uuid = uuid
    this.model = this.getModelView()
    this.loadPageData()
  }

  loadPageData() {
    this.loading = true
    forkJoin([this.service.getIndividual(this.uuid)])
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        ([revenuePlan]) => {
          this.initialRevenueForecast = revenuePlan.dados
          this.loadFormView(this.initialRevenueForecast)
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  private loadFormView(
    initialRevenueForecast: InitialRevenueForecastInterface,
  ) {
    this.model.patchValue({
      codigo: initialRevenueForecast.planoReceita?.codigo,
      nome: initialRevenueForecast.planoReceita?.nome,
      operacao: initialRevenueForecast.tipoOperacaoReceita?.nome,
      valorPrevisto: initialRevenueForecast.valorPrevisto,
    })
  }

  private getModelView(): FormGroup {
    return this.builder.group({
      codigo: [],
      nome: [],
      operacao: [],
      valorPrevisto: [],
    })
  }

  cancel() {
    this.router.navigate([
      'lei-orcamentaria-anual',
      'receita',
      'previsao-inicial',
      'edit',
      this.initialRevenueForecast.planoReceita.uuid,
    ])
  }

  onChangeTab(event: any) {
    const { tabId } = event
    this.touchedTabs.add(tabId)
  }

  tabWasTouched(tabId: string) {
    return this.touchedTabs.has(tabId)
  }
}
