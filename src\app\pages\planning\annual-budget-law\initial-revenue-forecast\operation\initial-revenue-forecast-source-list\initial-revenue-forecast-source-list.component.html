<div class="container">
  <section>
    <dx-data-grid
      id="initialRevenueForecastSourceGrid"
      [dataSource]="data"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [nbSpinner]="loading"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="false"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
      class="mt-3"
      (onRowUpdated)="onRowUpdated($event)"
      (onRowUpdating)="onRowUpdating($event)"
    >
      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-editing
        mode="cell"
        [allowUpdating]="true"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="fonteRecurso.codigo"
        [allowEditing]="false"
        caption="Código"
      ></dxi-column>

      <dxi-column
        dataField="fonteRecurso.nome"
        [allowEditing]="false"
        caption="Nome"
      ></dxi-column>

      <dxi-column
        dataField="percentual"
        caption="Percentual"
        [allowEditing]="faseValida"
        [editorOptions]="{ placeholder: '00' }"
        cellTemplate="percentageColumn"
      ></dxi-column>
      <div *dxTemplate="let data of 'percentageColumn'">
        {{ data.value / 100 | percent : '1.2-2' }}
      </div>

      <dxi-column
        dataField="valorPrevisto"
        caption="Valor Previsto"
        [allowEditing]="faseValida"
        [editorOptions]="{ placeholder: '00' }"
        [format]="currencyFormat"
      ></dxi-column>
    </dx-data-grid>
    <p class="text-right" style="padding-right: 11px; font-size: 13px">
      <strong>Total:</strong>
      {{ predictedValueTotal | currency : 'BRL' : true }}
    </p>
  </section>

  <div class="d-flex justify-content-end mt-4 mb-2">
    <button
      class="btn btn-success"
      (click)="confirm()"
      [disabled]="!isEditing || predictedValueTotal !== valorPrevisto || !faseValida"
    >
      Confirmar
    </button>
  </div>
</div>
