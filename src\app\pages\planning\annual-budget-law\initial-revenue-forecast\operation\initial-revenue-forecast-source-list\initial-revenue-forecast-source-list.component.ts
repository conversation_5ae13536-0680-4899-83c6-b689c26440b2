import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormControl, FormGroup } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import { SourceGroupInterface } from '@pages/planning/shared/interfaces/source-group'
import { ResourceSourceService } from '@pages/planning/shared/services/resource-source.service'
import { forkJoin } from 'rxjs'
import { finalize, first, take } from 'rxjs/operators'
import { InitialRevenueForecastSourceInterface } from '../../interfaces/initial-revenue-forecast-source'
import { InitialRevenueForecastSourceService } from '../../services/initial-revenue-forecast-source.service'

@Component({
  selector: 'eqp-initial-revenue-forecast-source-list',
  templateUrl: './initial-revenue-forecast-source-list.component.html',
  styleUrls: ['./initial-revenue-forecast-source-list.component.scss'],
})
export class InitialRevenueForecastSourceListComponent implements OnInit {
  public loading: boolean = false
  public currencyFormat = currencyFormat
  public faseValida = false

  model: FormGroup
  data: InitialRevenueForecastSourceInterface[] = []

  @Input() enablePercentage: boolean
  @Input() valorPrevisto: number
  @Input() parentUuid: string

  isEditing = false

  constructor(
    private toastr: ToastrService,
    private builder: FormBuilder,
    private service: InitialRevenueForecastSourceService,
    private resourceSourceService: ResourceSourceService,
  ) {}

  ngOnInit(): void {
    this.fetchGrid()
    this.service
      .getExercicioStatus()
      .pipe(first())
      .subscribe(({dados}) => {
        this.faseValida = dados.exercicioStatus.codigo === 2
      })
    this.model = this.builder.group({
      fontesRecurso: [],
      valorTotal: [this.valorPrevisto]
    })
  }

  get predictedValueTotal(): number {
    return (
      this.data?.reduce((total, data) => total + data.valorPrevisto, 0) || 0
    )
  }

  fetchGrid() {
    this.loading = true
    forkJoin([
      this.resourceSourceService.getSourceGroups(),
      this.service.get(this.parentUuid),
    ])
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(([sourceGroups, forecastSources]) => {
        if (sourceGroups.totalCount > 0 && forecastSources.dados.length == 0) {
          this.tryResourceSourceGeneration(sourceGroups.data)
        } else {
          this.data = forecastSources.dados
        }
      })
  }

  private tryResourceSourceGeneration(sourceGroups: SourceGroupInterface[]) {
    const sourceGroup = sourceGroups.find(
      sourceGroup => sourceGroup.codigo == 1,
    )
    if (sourceGroup) {
      this.loading = true
      this.service
        .initializeResourceSource(this.parentUuid, sourceGroup)
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => {
          this.data = res.body.dados
        }),
        (err: any) => this.toastr.bulkSend(err.mensagens)
    }
  }

  get totalPercentage(): number {
    return this.data.reduce((sum, data) => sum + Number(data.percentual), 0)
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Fonte de recurso'
          item.options.hint = 'Fonte de recurso'
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  onRowUpdated(event: any): void {
    this.isEditing = true
  }

  public onRowUpdating(event: any) {
    if (event.newData?.valorPrevisto) {
      let newPredictedValue = event.newData.valorPrevisto
      if (event.oldData?.valorPrevisto) {
        newPredictedValue = newPredictedValue - event.oldData.valorPrevisto
      }
      event.cancel = !this.newPredictedValueIsValid(newPredictedValue)
    }
  }

  private newPredictedValueIsValid(predictedValue: number) {
    let nextTotal = this.predictedValueTotal + Number(predictedValue)
    if (nextTotal > this.valorPrevisto) {
      this.toastr.send({
        title: 'Alerta',
        warning: true,
        message: 'O total de valores não pode ser superior ao valor previsto',
      })
      return false
    }
    return true
  }

  confirm() {
    if(this.totalPercentage !== 100) {
      this.toastr.send({
        warning: true,
        message:
          'O percentual total das fontes de recursos não pode diferente de 100%.',
      })
      return
    }
    if (
      (this.predictedValueTotal === this.valorPrevisto)
    ) {
      this.service
        .putBatchInitialRevenueForecastResourceSource(
          this.parentUuid,
          this.data,
        )
        .subscribe(
          res => {
            this.toastr.send({
              success: true,
              message: 'Fontes recurso da receita atualizados(as) com sucesso.',
            })
            this.data = res.body.dados
            this.isEditing = false
          },
          (err: any) => this.toastr.bulkSend(err.mensagens),
        )
    }
  }
}
