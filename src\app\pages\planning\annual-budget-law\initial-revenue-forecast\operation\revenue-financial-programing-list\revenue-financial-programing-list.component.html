<div class="container">
  <div class="row mt-3">
    <div class="col col-md-6">
      <dx-data-grid
        id="revenueFinancialProgramingMonthGrid"
        [dataSource]="data"
        [allowColumnResizing]="true"
        [columnAutoWidth]="true"
        [nbSpinner]="loading"
        [showColumnLines]="false"
        [showRowLines]="false"
        [showBorders]="false"
        [rowAlternationEnabled]="true"
        [wordWrapEnabled]="true"
        [loadPanel]="false"
        [columnHidingEnabled]="false"
        [remoteOperations]="true"
        keyExpr="uuid"
        (onRowUpdating)="onRowUpdating($event)"
        (onRowUpdated)="calculateValues()"
      >
        <dxo-header-filter [visible]="false"> </dxo-header-filter>

        <dxo-sorting mode="multiple"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxo-editing
          mode="cell"
          [allowUpdating]="true"
          [allowDeleting]="false"
          [allowAdding]="false"
          [useIcons]="true"
        >
        </dxo-editing>

        <dxi-column
          dataField="mes"
          [allowEditing]="false"
          caption="Mês"
          [allowSorting]="false"
        >
          <dxo-lookup
            [dataSource]="months"
            valueExpr="value"
            displayExpr="text"
          >
          </dxo-lookup>
        </dxi-column>

        <dxi-column
          dataField="percentual"
          caption="Percentual"
          [editorOptions]="{ placeholder: '00' }"
          dataType="number"
          [allowSorting]="false"
        ></dxi-column>

        <dxi-column
          dataField="valor"
          caption="Valor"
          [editorOptions]="{ placeholder: 'R$ 00.00' }"
          [format]="{
            style: 'currency',
            currency: 'BRL',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          }"
          [allowSorting]="false"
        ></dxi-column>
      </dx-data-grid>
    </div>
    <div
      class="col col-md-5 offset-md-1 d-flex flex-column"
      style="gap: 0.5rem"
      [formGroup]="calculatedModel"
    >
      <eqp-nebular-input
        [style]="'currency'"
        class="text-right"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="somaPercentuais"
        label="Soma dos percentuais"
        placeholder="Soma dos percentuais"
        [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
        [disabled]="true"
      >
      </eqp-nebular-input>
      <eqp-nebular-input
        [style]="'basic'"
        class="text-right"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="diferencaPercentual"
        label="Diferença de percentual"
        placeholder="Diferença de percentual"
        type="number"
        [disabled]="true"
      >
      </eqp-nebular-input>
      <eqp-nebular-input
        [style]="'currency'"
        class="text-right"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="somaValor"
        label="Soma de valor"
        placeholder="Soma de valor"
        [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
        [disabled]="true"
      >
      </eqp-nebular-input>
      <eqp-nebular-input
        [style]="'currency'"
        class="text-right"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="diferencaValor"
        label="Diferença de valor"
        placeholder="Diferença de valor"
        [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
        [disabled]="true"
      >
      </eqp-nebular-input>
      <div class="d-flex justify-content-end mt-3" style="gap: 0.8rem">
        <eqp-nebular-button
          [buttonVisible]="true"
          [buttonType]="'primary'"
          [buttonSize]="'small'"
          [buttonText]="'Limpar valores'"
          [buttonTitle]="'Limpar valores'"
          [buttonId]="'financial-programing-clear-values'"
          [buttonIconVisible]="true"
          (buttonEmitter)="clearValues()"
        >
        </eqp-nebular-button>
        <eqp-nebular-button
          [buttonVisible]="true"
          [buttonType]="'primary'"
          [buttonSize]="'small'"
          [buttonText]="'Ratear entre meses'"
          [buttonTitle]="'Ratear entre meses'"
          [buttonId]="'financial-programing-month-apportionment'"
          [buttonIconVisible]="true"
          (buttonEmitter)="monthApportionment()"
        >
        </eqp-nebular-button>
      </div>
    </div>
    <div class="w-100 d-flex justify-content-end mt-3 px-md-3 mb-2">
      <button class="btn btn-success" (click)="submit()" [disabled]="somaPercentuais > 100">
        Confirmar
      </button>
    </div>
  </div>
</div>
