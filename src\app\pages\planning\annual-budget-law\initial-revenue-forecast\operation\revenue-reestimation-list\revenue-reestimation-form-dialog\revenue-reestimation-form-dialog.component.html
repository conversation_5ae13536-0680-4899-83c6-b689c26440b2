<eqp-nebular-dialog
  [dialogTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonId]="'voltar-reestimativa-receita'"
  [bottomLeftButtonTitle]="'Voltar'"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonId]="'confirmar-reestimativa-receita'"
  [rightFirstButtonDisabled]="!model.valid"
  (rightFirstButtonEmitter)="confirm()"
>
  <form [formGroup]="model">
    <div class="row">
      <div class="col col-md-3">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="data"
          label="Data *"
          placeholder=""
          [errorMessage]="'É obrigatório preencher a data inicial'"
        >
        </eqp-nebular-input>
      </div>
      <div class="col">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="descricao"
          label="Descrição"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col col-md-3">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="valor"
          label="Valor *"
          placeholder=""
          [options]="{
            prefix: 'R$ ',
            thousands: '.',
            decimal: ',',
            allowNegative: false
          }"
        >
        </eqp-nebular-input>
      </div>
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Fonte recurso"
          label="Fonte recurso *"
          placeholder=""
          [dataSource]="revenuePlanSourceData"
          valueExpr="uuid"
          displayExpr="fonteRecurso.codigoEhNome"
          formControlName="planoReceitaFonte"
        ></eqp-nebular-select>
      </div>
    </div>
  </form>
</eqp-nebular-dialog>
