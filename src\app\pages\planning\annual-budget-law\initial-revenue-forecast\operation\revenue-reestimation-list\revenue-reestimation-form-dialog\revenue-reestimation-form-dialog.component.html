<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="data"
          label="Data*"
          placeholder="Data"
          [errorMessage]="'É obrigatório preencher a data inicial'"
        >
        </eqp-nebular-input>
      </div>
      <div class="col">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="descricao"
          label="Descrição"
          placeholder="Descrição"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="valor"
          label="Valor"
          placeholder="Valor"
          [options]="{
            prefix: 'R$ ',
            thousands: '.',
            decimal: ',',
            allowNegative: false
          }"
        >
        </eqp-nebular-input>
      </div>
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Fonte recurso"
          label="Fonte recurso"
          placeholder="Fonte recurso"
          [dataSource]="revenuePlanSourceData"
          valueExpr="uuid"
          displayExpr="fonteRecurso.nome"
          formControlName="planoReceitaFonte"
        ></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="!model.valid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
