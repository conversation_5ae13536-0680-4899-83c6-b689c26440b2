import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { getMomentDate, getParsedDate } from '@common/helpers/parsers'
import { NbDialogRef } from '@nebular/theme'
import { RevenuePlanSourceService } from '@pages/planning/multi-year-plan/services/revenue-plan-source.service'
import DataSource from 'devextreme/data/data_source'
import { RevenueReestimationInterface } from '../../../interfaces/revenue-reestimation'

@Component({
  selector: 'eqp-revenue-reestimation-form-dialog',
  templateUrl: './revenue-reestimation-form-dialog.component.html',
  styleUrls: ['./revenue-reestimation-form-dialog.component.scss'],
})
export class RevenueReestimationFormDialogComponent implements OnInit {
  pageTitle: string = 'Reestimativa da receita'
  loading: boolean
  model: FormGroup

  revenuePlanSourceData: DataSource

  @Input() initialData: RevenueReestimationInterface
  @Input() revenuePlanUuid: string

  constructor(
    private dialogRef: NbDialogRef<RevenueReestimationFormDialogComponent>,
    private builder: FormBuilder,
    private revenuePlanSourceService: RevenuePlanSourceService,
  ) {}

  get uuid() {
    return this.model.get('uuid')?.value
  }

  codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelects()
    if (this.initialData) {
      this.loadForm(this.initialData)
    }
  }

  private async loadSelects() {
    let revenuePlanSourceData = await this.revenuePlanSourceService
      .get(this.revenuePlanUuid)
      .toPromise()
    revenuePlanSourceData = revenuePlanSourceData.data || []
    this.revenuePlanSourceData = new DataSource({
      store: {
        data: revenuePlanSourceData,
        type: 'array',
        key: 'uuid',
      },
    })
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      data: [undefined, Validators.required],
      descricao: [''],
      valor: [undefined, Validators.required],
      planoReceitaFonte: [undefined, Validators.required],
    })
  }

  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())
      this.dialogRef.close(dto)
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  private loadForm(initialData: RevenueReestimationInterface) {
    const dto = {
      ...initialData,
      data: initialData.data ? getMomentDate(`${initialData.data}`) : null,
      planoReceitaFonte: initialData.planoReceitaFonte?.uuid,
    }
    this.model.patchValue(dto)
  }

  private prepare(formData: any) {
    let dto = {
      ...formData,
    }
    if (dto.data) {
      dto.data = getParsedDate(dto.data)
    }
    dto.planoReceitaFonte = { uuid: formData.planoReceitaFonte }
    return dto
  }
}
