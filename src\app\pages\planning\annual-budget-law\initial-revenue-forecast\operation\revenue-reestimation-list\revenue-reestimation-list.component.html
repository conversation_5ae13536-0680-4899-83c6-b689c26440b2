<dx-data-grid
  id="revenueReestimationGrid"
  [dataSource]="revenueReestimationData"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [nbSpinner]="loading"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="false"
  [remoteOperations]="true"
  keyExpr="uuid"
  (onToolbarPreparing)="onToolbarPreparing($event)"
  class="mt-3"
>
  <dxo-paging [pageSize]="10"></dxo-paging>
  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-editing
    mode="cell"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="nivelPermissao === 'FULL'"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column dataField="data" caption="Data" dataType="date"> </dxi-column>

  <dxi-column
    dataField="valor"
    caption="Valor"
    [editorOptions]="{ placeholder: 'R$ 00,00' }"
    [format]="currencyFormat"
  ></dxi-column>
  <dxi-column
    dataField="descricao"
    [allowEditing]="false"
    caption="Descrição"
  ></dxi-column>

  <dxi-column
    dataField="uuid"
    caption=""
    [width]="80"
    [allowFiltering]="false"
    [allowSorting]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>

  <div *dxTemplate="let data of 'acaoColumn'">
    <a
      title="Alterar"
      (click)="update(data.value)"
      class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
    >
    </a>
    <a
      *ngIf="nivelPermissao === 'FULL'"
      title="Remover"
      (click)="remove(data.value)"
      class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
    >
    </a>
  </div>
</dx-data-grid>
