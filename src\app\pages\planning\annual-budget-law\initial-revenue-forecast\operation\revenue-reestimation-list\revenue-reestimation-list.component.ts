import { Component, Input, OnDestroy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import { Subscription } from 'rxjs'
import { filter, finalize, first, take } from 'rxjs/operators'
import { RevenueReestimationInterface } from './../../interfaces/revenue-reestimation'
import { RevenueReestimationService } from './../../services/revenue-reestimation.service'
import { RevenueReestimationFormDialogComponent } from './revenue-reestimation-form-dialog/revenue-reestimation-form-dialog.component'

@Component({
  selector: 'eqp-revenue-reestimation-list',
  templateUrl: './revenue-reestimation-list.component.html',
  styleUrls: ['./revenue-reestimation-list.component.scss'],
})
export class RevenueReestimationListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Reestimativa da receita'
  public currencyFormat = currencyFormat

  public revenueReestimationData: RevenueReestimationInterface[] = []
  private subscription: Subscription

  @Input() parentUuid: string
  @Input() revenuePlanUuid: string

  constructor(
    private toastr: ToastrService,
    public router: Router,
    public menuService: MenuService,
    private dialogService: NbDialogService,
    private service: RevenueReestimationService,
  ) {
    super(menuService, router)
    this.permissao('/reestimativa-receita')
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private async fetchGrid() {
    let revenueReestimationData = await this.service
      .get(this.parentUuid)
      .toPromise()
    this.revenueReestimationData = revenueReestimationData.dados || []
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Reestimativa'
          item.options.hint = 'Novo(a) reestimativa'
          item.options.onClick = () => this.openReestimationFormDialog()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  private openReestimationFormDialog(
    initialData?: RevenueReestimationInterface,
  ) {
    const dialogRef = this.dialogService.open(
      RevenueReestimationFormDialogComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.componentRef.instance.initialData = initialData
    dialogRef.componentRef.instance.revenuePlanUuid = this.revenuePlanUuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(res => {
      if (!res.uuid) {
        this.loading = true
        this.service
          .post(this.parentUuid, res)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Reestimativa cadastrada com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      } else {
        this.loading = true
        this.service
          .put(this.parentUuid, res, res.uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Reestimativa atualizada com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public update(uuid: string): void {
    const reestimation = this.revenueReestimationData.find(
      item => item.uuid === uuid,
    )
    if (reestimation) {
      this.openReestimationFormDialog(reestimation)
    }
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.parentUuid, uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Reestimativa excluída com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
