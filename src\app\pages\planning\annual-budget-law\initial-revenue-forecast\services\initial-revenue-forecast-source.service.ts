import { SourceGroupInterface } from '@pages/planning/shared/interfaces/source-group';
import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseChildService } from '@common/services/base/base-child.service'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { InitialRevenueForecastSourceInterface } from '../interfaces/initial-revenue-forecast-source'
import { Observable } from 'rxjs';
import { UserDataService } from '@guards/services/user-data.service';

@Injectable({
  providedIn: 'root',
})
export class InitialRevenueForecastSourceService extends BaseChildService<InitialRevenueForecastSourceInterface> {
  constructor(protected httpClient: HttpClient, private userDataService: UserDataService) {
    super(httpClient, 'previsao_inicial_receita', 'fonte_recurso')
  }

  public initializeResourceSource(uuid: string, sourceGroup: SourceGroupInterface) {
    return this.http.post<ResponseDto<InitialRevenueForecastSourceInterface[]>>(
      `previsao_inicial_receita/${uuid}/gerar_fonte_recurso`,
      {
        grupoFonte: sourceGroup
      },
      {
        observe: 'response',
      },
    )
  }

  public putBatchInitialRevenueForecastResourceSource(
    initialRevenueForecastUuid: string,
    data: InitialRevenueForecastSourceInterface[],
  ) {
    return this.http.put<ResponseDto<InitialRevenueForecastSourceInterface[]>>(
      `previsao_inicial_receita/${initialRevenueForecastUuid}/fonte_recurso/lote`,
      data,
      {
        observe: 'response',
      },
    )
  }

  public getExercicioStatus(): Observable<any> {
    const userData = this.userDataService.userData
    return this.http.get<any>(`exercicio/${userData.exercicioUuid}`, {})
  }
}
