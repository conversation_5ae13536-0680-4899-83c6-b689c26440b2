import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { InitialRevenueForecastInterface } from '../interfaces/initial-revenue-forecast'

@Injectable({
  providedIn: 'root',
})
export class InitialRevenueForecastService extends BaseService<
  ResponseDto<InitialRevenueForecastInterface[]>,
  InitialRevenueForecastInterface
> {
  constructor(protected http: HttpClient) {
    super(http, 'previsao_inicial_receita')
  }
}
