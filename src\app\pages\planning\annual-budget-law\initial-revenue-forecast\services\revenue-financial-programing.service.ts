import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseChildService } from '@common/services/base/base-child.service'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { RevenueFinancialProgramingInterface } from '../interfaces/revenue-financial-programing'

@Injectable({
  providedIn: 'root',
})
export class RevenueFinancialProgramingService extends BaseChildService<RevenueFinancialProgramingInterface> {
  constructor(protected httpClient: HttpClient) {
    super(httpClient, 'previsao_inicial_receita', 'programacao_financeira')
  }

  public postBatch(
    initialRevenueForecastUuid: string,
    data: RevenueFinancialProgramingInterface[],
  ) {
    return this.http.post<ResponseDto<RevenueFinancialProgramingInterface[]>>(
      `previsao_inicial_receita/${initialRevenueForecastUuid}/programacao_financeira/lote`,
      data,
      {
        observe: 'response',
      },
    )
  }

  public putBatch(
    initialRevenueForecastUuid: string,
    data: RevenueFinancialProgramingInterface[],
  ) {
    return this.http.put<ResponseDto<RevenueFinancialProgramingInterface[]>>(
      `previsao_inicial_receita/${initialRevenueForecastUuid}/programacao_financeira/lote`,
      data,
      {
        observe: 'response',
      },
    )
  }
}
