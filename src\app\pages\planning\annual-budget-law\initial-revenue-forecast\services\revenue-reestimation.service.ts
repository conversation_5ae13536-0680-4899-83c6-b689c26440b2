import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseChildService } from '@common/services/base/base-child.service'
import { RevenueReestimationInterface } from '../interfaces/revenue-reestimation'

@Injectable({
  providedIn: 'root',
})
export class RevenueReestimationService extends BaseChildService<RevenueReestimationInterface> {
  constructor(protected httpClient: HttpClient) {
    super(httpClient, 'previsao_inicial_receita', 'reestimativa_receita')
  }
}
