<eqp-standard-page
  [mainTitle]="tituloPagina"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update-expense-evolution-list'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="carregarGrid(flagSomenteDespesasComPessoal.value)"
>
  <ng-container>
    <div class="row mb-3">
      <div class="col-md-12">
        <eqp-field-toggle
          [formControl]="flagSomenteDespesasComPessoal"
          name="flagSomenteDespesasComPessoal"
          label="Somente despesas com pessoal"
          (ngModelChange)="carregarGrid($event)"
        >
        </eqp-field-toggle>
      </div>
    </div>
    <dx-data-grid
      id="evolucaoDespesaGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      [remoteOperations]="true"
    >
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="tituloPagina"
      ></dxo-export>

      <dxo-scrolling mode="infinite"></dxo-scrolling>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar registro"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column alignment="left" dataField="tipo" caption="Tipo"></dxi-column>

      <dxi-column
        alignment="left"
        dataField="ordem"
        caption="Ordem"
      ></dxi-column>

      <dxi-column alignment="left" dataField="nome" caption="Nome"></dxi-column>

      <dxi-column
        dataField="valorAutorizado"
        caption="Valor autorizado"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        dataField="percentualOrcamentoCrianca"
        caption="%"
        alignment="center"
        cellTemplate="percentualOrcamentoCrianca"
      ></dxi-column>

      <div *dxTemplate="let data of 'percentualOrcamentoCrianca'">
        {{porcentagemDisplayFormat(data.value)}}
      </div>

      <dxi-column
        dataField="valorDestinado"
        caption="Valor destinado"
        format="currency"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        dataField="caraterExclusivoUuid.nome"
        caption="Caráter"
      ></dxi-column>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
