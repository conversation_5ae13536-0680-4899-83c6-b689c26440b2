import { Component, OnInit } from '@angular/core'
import { FormControl } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'
import { finalize } from 'rxjs/operators'
import { ConsultaOrcamentoCriancaInterface } from '../../interfaces/consulta-orcamento-crianca'

@Component({
  selector: 'eqp-orcamento-crianca-percentual-lista',
  templateUrl: './orcamento-crianca-percentual-lista.component.html',
  styleUrls: ['./orcamento-crianca-percentual-lista.component.scss'],
})
export class OrcamentoCriancaPercentualListaComponent implements OnInit {
  public tituloPagina = '% Orçamento Criança'
  public dataSource: ConsultaOrcamentoCriancaInterface[] = []
  public loading: boolean = false
  public currencyFormat = currencyFormat
  public flagSomenteDespesasComPessoal: FormControl = new FormControl()

  constructor(private crudService: CrudService) {}

  ngOnInit(): void {
    this.carregarGrid()
  }

  public porcentagemDisplayFormat(cellValue: number): string {
    if (cellValue === null || cellValue === undefined) {
      return ''
    }
    return cellValue + '%'
  }

  public carregarGrid(flag: 'S' | 'N' = 'N') {
    this.loading = true

    let url = 'projeto_atividade/orcamento_crianca?take=0'

    if (flag === 'S') {
      url += '&flagSomenteDespesasComPessoal=true'
    }

    this.crudService
      .getSingleData<ConsultaOrcamentoCriancaInterface[]>(url)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        this.dataSource = res.dados
      })
  }
}
