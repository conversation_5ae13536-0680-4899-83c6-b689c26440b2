import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { CrudService } from '@common/services/crud.service';
import { EntityInterface } from '@core/interfaces/entity';
import { Subject } from 'rxjs';
import { take, takeUntil, finalize } from 'rxjs/operators';
import { NbDialogService } from '@nebular/theme';
import { ReportPreviewComponent } from '@pages/planning/shared/reports/components/report-preview/report-preview.component';
import { ReportService } from '@pages/planning/shared/reports/services/report.service';
import { FONTE_RECURSO } from './helpers/fonteRecurso';
import { FUNCAO_SUBFUNCAO } from './helpers/funcaoSubfuncao';
import { ORGAO_UNIDADE } from './helpers/orgaoUnidade';
import { PROJETO_ATIVIDADE } from './helpers/projetoAtividade';

@Component({
  selector: 'eqp-relatorio-orcamento-crianca',
  templateUrl: './relatorio-orcamento-crianca.component.html',
  styleUrls: ['./relatorio-orcamento-crianca.component.scss']
})
export class RelatorioOrcamentoCriancaComponent implements OnInit, OnDestroy {
  public model: FormGroup
  public tituloPagina = 'Relatório Orçamento Criança'
  public entidades = []
  public camposFiltro: { chave: string; valor: string }[] = [
    { chave: 'orgaoUnidade', valor: 'Órgão e unidade' },
    { chave: 'fonteRecurso', valor: 'Fonte de recurso' },
    { chave: 'funcaoSubfuncao', valor: 'Função/Subfunção' },
    { chave: 'projetoAtividade', valor: 'Projeto/atividade' },
  ]

  public loading = false;
  private unsub$ = new Subject<null>();
  private uri = 'relatorio/orcamento_crianca';
  public reportTypeList: any[] = [];
  public downloadType = new FormControl('PDF');

  constructor(
    private builder: FormBuilder,
    private crudService: CrudService,
    private dialogService: NbDialogService,
    private reportService: ReportService
  ) { }

  ngOnInit(): void {
    this.model = this.builder.group({
      dataInicial: [''],
      dataFinal: [''],
      ordenacao: [''],
      entidadesUuid: [],
    })

    this.crudService
      .getSingleData<EntityInterface[]>(`entidade/paginado`)
      .pipe(take(1))
      .subscribe((res) => {
        this.entidades = res.data;
      });

    this.getReportTypes();
  }

  private getReportTypes(): void {
    this.crudService
      .getSingleData<any>(
        'transparencia/pre_carregamento_relatorio/tipo_relatorio',
      )
      .pipe(take(1))
      .subscribe(res => {
        this.reportTypeList = res.dados || [];
      });
  }

  ngOnDestroy(): void {
    this.unsub$.next();
    this.unsub$.complete();
  }

  public submit(): void {
    this.loading = true;
    const dto = this.prepare(this.model.getRawValue());

    // Simulando processamento
    setTimeout(() => {
      this.loading = false;

      console.log('Dados do formulário:', dto);
      console.log('Tipo de arquivo selecionado:', this.downloadType.value);

      if (this.downloadType.value === 'PDF') {
        console.log('Abrindo visualizador de PDF com base64 do relatório');

        // Abrindo o ReportPreviewComponent com dados simulados
        this.dialogService.open(ReportPreviewComponent, {
          context: {
            downloadName: this.tituloPagina,
            rInfo: {
              dto: dto,
              url: this.uri,
            },
            reportData: {
              tipo: {
                extension: 'PDF'
              },
              documento: this.getBase64ForReport(dto.ordenacao),
            },
          },
          closeOnBackdropClick: false,
          closeOnEsc: false,
        });
      } else {
        console.log('Iniciando download direto do arquivo');
        this.downloadReport(dto);
      }
    }, 800); // Simulando um pequeno delay para mostrar o loading
  }

  public downloadReport(data: any): void {
    this.loading = true;

    // Simulando uma requisição com um timeout
    setTimeout(() => {
      // Criando dados fake para simular a resposta da API
      const reportData = {
        documento: this.getBase64ForReport(data.ordenacao),
        tipo: {
          extension: this.downloadType.value || 'PDF',
        },
      };

      console.log('Simulando download com os dados:', {
        reportData: reportData,
        downloadName: this.tituloPagina,
        tipoArquivo: this.downloadType.value
      });

      // Chamando o serviço de download com os dados simulados
      this.reportService.donwloadReport(
        reportData,
        this.tituloPagina,
      );

      // Desativando o loading após o "processamento"
      this.loading = false;
    }, 1500); // Simulando um delay de 1.5 segundos para parecer uma requisição real
  }

  private prepare(data: any): any {
    return { ...data };
  }

  private getBase64ForReport(ordenacao: string): string {
    switch (ordenacao) {
      case 'orgaoUnidade':
        return ORGAO_UNIDADE;
      case 'fonteRecurso':
        return FONTE_RECURSO;
      case 'funcaoSubfuncao':
        return FUNCAO_SUBFUNCAO;
      case 'projetoAtividade':
        return PROJETO_ATIVIDADE;
      default:
        return ORGAO_UNIDADE; // Default to ORGAO_UNIDADE if no selection
    }
  }

}
