<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [rightApproveButtonText]="'Salvar'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancelar(null)"
  [rightApproveButtonVisible]="false"
  [formGroup]="model"
>
  <div class="row">
    <div class="col">
      <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="revenuePlan"
        name="Conta de receita"
        label="Conta de receita"
        [readonly]="true"
      >
      </eqp-nebular-input>
    </div>
  </div>
  <br />
  <ng-container *ngIf="dataSource">
    <dx-data-grid
      id="fonteRecursoGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      [remoteOperations]="true"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel [visible]="false" placeholder=""></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="tipoOperacaoReceita.nome"
        caption="Tipo de operação"
      >
      </dxi-column>

      <dxi-column caption="Receita arrecadada" alignment="center">
        <dxi-column
          [format]="currencyFormat"
          dataField="vlrExercicio1"
          [caption]="exercicioLogado - 4"
          alignment="left"
        >
        </dxi-column>

        <dxi-column
          [format]="currencyFormat"
          dataField="vlrExercicio2"
          [caption]="exercicioLogado - 3"
          alignment="left"
        >
        </dxi-column>

        <dxi-column
          [format]="currencyFormat"
          dataField="vlrExercicio3"
          [caption]="exercicioLogado - 2"
          alignment="left"
        >
        </dxi-column>
      </dxi-column>

      <dxi-column caption="Receita prevista" alignment="center">
        <dxi-column
          [format]="currencyFormat"
          dataField="vlrExercicio4"
          [caption]="exercicioLogado - 1"
          alignment="left"
        >
        </dxi-column>

        <dxi-column
          [format]="currencyFormat"
          dataField="vlrExercicio5"
          [caption]="exercicioLogado"
          alignment="left"
        >
        </dxi-column>
      </dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="80"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          title="Alterar"
          (click)="alterar(data.data)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          *ngIf="nivelPermissao === 'FULL'"
          title="Remover"
          (click)="remover(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
