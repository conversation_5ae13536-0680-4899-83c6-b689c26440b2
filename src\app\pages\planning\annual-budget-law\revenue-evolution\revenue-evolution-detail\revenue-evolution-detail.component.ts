import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first, pluck } from 'rxjs/operators'
import { RevenueEvolutionFormComponent } from '../revenue-evolution-form/revenue-evolution-form.component'
import { RevenueEvolutionOperationTypeService } from '../services/revenue-evolution-operation-type.service'
import { RevenueEvolutionService } from '../services/revenue-evolution.service'

@Component({
  selector: 'eqp-revenue-evolution-detail',
  templateUrl: './revenue-evolution-detail.component.html',
  styleUrls: ['./revenue-evolution-detail.component.scss'],
})
export class RevenueEvolutionDetailComponent
  extends BaseTelasComponent
  implements OnInit
{
  private revenuePlanUuid: string

  public revenueEvolutionUuid: string
  public loading: boolean = false
  public pageTitle: string = 'Evolução da Receita'
  public dataSource: DataSource
  public exercicioLogado: number
  public model: FormGroup
  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<RevenueEvolutionDetailComponent>

  constructor(
    private formBuilder: FormBuilder,
    private revenueEvolutionService: RevenueEvolutionService,
    private revenueEvolutionOperationTypeService: RevenueEvolutionOperationTypeService,
    private userService: UserDataService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('plano-receita')
  }

  ngOnInit(): void {
    this.model = this.formBuilder.group({
      revenuePlan: [undefined],
    })
    this.carregarTela()
    this.exercicioLogado = +this.userService.userData.exercicio
  }

  private carregarTela() {
    this.route.params.pipe(first()).subscribe(params => {
      this.revenueEvolutionUuid = params['uuid']
      this.buscarPlanoReceita(this.revenueEvolutionUuid)
      this.fetchGrid()
    })
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.revenueEvolutionOperationTypeService.getDataSourceFiltro(
        'uuid',
        'evolucao_receita_tipo_operacao/paginado',
        10,
        'revenueEvolution',
        this.revenueEvolutionUuid,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private buscarPlanoReceita(revenuEvolutionUuid: string): void {
    this.loading = true
    this.revenueEvolutionService
      .getIndividual(revenuEvolutionUuid)
      .pipe(
        first(),
        pluck('dados'),
        finalize(() => (this.loading = false)),
      )
      .subscribe(data => {
        this.revenuePlanUuid = data.planoReceita.uuid
        this.model
          .get('revenuePlan')
          .patchValue(`${data.planoReceita.codigo} - ${data.planoReceita.nome}`)
      })
  }

  public cancelar(retorno): void {
    this.router.navigate([`lei-orcamentaria-anual/receita/evolucao`])
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Operação'
          item.options.hint = 'Nova operação'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public novoRegistro() {
    const dialogRef = this.dialogService.open(RevenueEvolutionFormComponent, {
      context: {
        data: {
          exercicioLogado: this.exercicioLogado,
          evolucaoReceitaUuid: this.revenueEvolutionUuid,
          planoReceitaUuid: this.revenuePlanUuid,
        },
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        this.fetchGrid()
      }
    })
  }

  public alterar(value): void {
    const dialogRef = this.dialogService.open(RevenueEvolutionFormComponent, {
      context: {
        data: {
          revenueEvolutionOperationType: value,
          exercicioLogado: this.exercicioLogado,
          evolucaoReceitaUuid: this.revenueEvolutionUuid,
        },
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.loading = true
        this.fetchGrid()
        this.loading = false
      }
    })
  }

  public remover(value) {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno == 'S') {
        this.loading = true
        this.revenueEvolutionOperationTypeService
          .delete(value)
          .pipe(
            first(),
            finalize(() => {
              this.fetchGrid(), (this.loading = false)
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Operação de receita excluída com sucesso.',
              })
            },
            resp => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
