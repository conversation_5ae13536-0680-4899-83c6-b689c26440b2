<
<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo de operacao*"
          placeholder="Tipo de operacao"
          formControlName="tipoOperacaoReceitaUuid"
          [dataSource]="tipoOperacaoData"
          displayExpr="nome"
          valueExpr="uuid"
          [disabled]="data.revenueEvolutionOperationType"
        ></eqp-nebular-select>
      </div>
    </div>
    <br />
    <h6>Receita arrecadada</h6>
    <div class="row">
      <div class="col col-12 col-md-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio1"
          name="vlrExercicio1"
          [label]="+data.exercicioLogado - 4"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio2"
          name="vlrExercicio2"
          [label]="+data.exercicioLogado - 3"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio3"
          name="vlrExercicio3"
          [label]="+data.exercicioLogado - 2"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
    </div>
    <br />
    <h6>Receita prevista</h6>
    <div class="row">
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio4"
          name="vlrExercicio4"
          [label]="+data.exercicioLogado - 1"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrExercicio5"
          name="vlrExercicio5"
          [label]="+data.exercicioLogado"
          [readonly]="true"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="model.invalid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
