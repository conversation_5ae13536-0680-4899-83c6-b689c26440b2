import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import { MenuService } from '@pages/menu.service';
import DataSource from 'devextreme/data/data_source';
import { finalize, first, take, takeUntil } from 'rxjs/operators';
import { RevenueEvolutionOperationTypeInterface } from '../interfaces/revenue-evolution-operation-type';
import { RevenueEvolutionOperationTypeService } from '../services/revenue-evolution-operation-type.service';
import { RevenueOperationTypeService } from '../services/revenue-operation-type.service';
import { Observable, Subject } from 'rxjs';
import { CrudService } from '@common/services/crud.service';

@Component({
  selector: 'eqp-revenue-evolution-form',
  templateUrl: './revenue-evolution-form.component.html',
  styleUrls: ['./revenue-evolution-form.component.scss']
})
export class RevenueEvolutionFormComponent
	extends BaseTelasComponent implements OnInit, OnDestroy
{
	@Input() public data: {
		revenueEvolutionOperationType?: RevenueEvolutionOperationTypeInterface,
		exercicioLogado: number,
		evolucaoReceitaUuid: string
		planoReceitaUuid?: string
	}
	public pageTitle = 'Cadastrar operação de receita'
	public loading: boolean
	public model: FormGroup
	public tipoOperacaoData: DataSource

	private unsub$ = new Subject<null>()

  constructor(
		public router: Router,
		public menuService: MenuService,
		private toastr: ToastrService,
		private revenueOperationTypeService: RevenueOperationTypeService,
		private revenueEvolutionOperationTypeService: RevenueEvolutionOperationTypeService,
		private dialogRef: NbDialogRef<RevenueEvolutionFormComponent>,
		private formBuilder: FormBuilder,
		private crudService: CrudService,
	) {
		super(menuService, router)
		this.permissao('evolucao-receita')
	}

  ngOnInit(): void {
		this.model = this.getNovoFormulario()
		this.model.get('evolucaoReceitaUuid').patchValue(this.data.evolucaoReceitaUuid)
		if (!this.data.revenueEvolutionOperationType) {
			this.loadSelect()
			this.loadHandler()
		} else {
			this.loadForm()
		}
  }

	ngOnDestroy(): void {
		this.unsub$.next()
		this.unsub$.complete()
	}

	private loadForm() {
		this.tipoOperacaoData = new DataSource(
			[this.data.revenueEvolutionOperationType.tipoOperacaoReceita]
		)
		this.model.patchValue({
			uuid: this.data.revenueEvolutionOperationType.uuid,
			tipoOperacaoReceitaUuid: this.data.revenueEvolutionOperationType?.tipoOperacaoReceita?.uuid,
			vlrExercicio1: this.data.revenueEvolutionOperationType?.vlrExercicio1,
			vlrExercicio2: this.data.revenueEvolutionOperationType?.vlrExercicio2,
			vlrExercicio3: this.data.revenueEvolutionOperationType?.vlrExercicio3,
			vlrExercicio4: this.data.revenueEvolutionOperationType?.vlrExercicio4,
			vlrExercicio5: this.data.revenueEvolutionOperationType?.vlrExercicio5,
		})
	}

	private loadHandler() {
		this.model.get('tipoOperacaoReceitaUuid').valueChanges
			.pipe(takeUntil(this.unsub$))
			.subscribe(value => {
				this.loading = true
				this.crudService.customGetSingleData<any>(
					'evolucao_receita_tipo_operacao/receita_prevista_por_tipo_operacao',
					{
						planoReceitaUuid: this.data.planoReceitaUuid,
						tipoOperacaoReceitaUuid: value
					}
				)
					.pipe(take(1), finalize(() => this.loading = false))
					.subscribe((res: any) => this.model.get('vlrExercicio5').patchValue(res.dados?.vlrExercicio5))
			})
	}

	public getNovoFormulario() {
		return this.formBuilder.group({
			uuid: [],
			evolucaoReceitaUuid: [],
			tipoOperacaoReceitaUuid: [undefined, Validators.required],
			vlrExercicio1: [0, Validators.required],
			vlrExercicio2: [0, Validators.required],
			vlrExercicio3: [0, Validators.required],
			vlrExercicio4: [0, Validators.required],
			vlrExercicio5: [],
		})
	}

	public cancel() {
		this.dialogRef.close(null)
	}

	public confirm() {
		this.loading = true
		let obs$: Observable<any>
		if (this.data.revenueEvolutionOperationType) {
			obs$ = this.revenueEvolutionOperationTypeService.put(this.model.getRawValue())
		} else {
			obs$ = this.revenueEvolutionOperationTypeService.post(this.model.getRawValue())
		}
		obs$
		.pipe(first(), finalize(() => this.loading = false))
		.subscribe({
			next: res => {
				this.toastr.send({
					success: true,
					message: `Operação de receita ${this.data.revenueEvolutionOperationType ? 'atualizada' : 'criada'} com sucesso.`
				})
				this.dialogRef.close(res)
			},
			error: () => {
				this.dialogRef.close(null)
			}
		})
	}

	async loadSelect() {
    this.tipoOperacaoData = new DataSource({
      store: this.revenueOperationTypeService.getDataSourceFiltro(
				'uuid',
				'tipo_operacao_receita/paginado',
				10
			),
      paginate: true,
      pageSize: 10,
    })
  }
}
