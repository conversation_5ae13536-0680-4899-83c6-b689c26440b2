<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update-revenue-evolution-list'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="fetchGrid()"
  [rightApproveButtonVisible]="true"
  [rightApproveButtonIconVisible]="true"
  [rightApproveButtonText]="'Importar valores'"
  [rightApproveButtonIcon]="'fas fa-file-import'"
  [rightApproveButtonId]="'import-revenue-plan'"
  (rightApproveButtonEmitter)="modalAvisoImportar()"
>
  <ng-container *ngIf="dataSource">
    <dx-data-grid
      id="evolucaoReceitaGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar evolução de receita"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column dataField="planoReceita.codigo" [caption]="'Código'">
      </dxi-column>

      <dxi-column dataField="planoReceita.nome" [caption]="'Nome'">
      </dxi-column>

      <dxi-column caption="Receita arrecadada" alignment="center">
        <dxi-column
          dataField="vlrExercicio1"
          [caption]="+userData.exercicio - 4"
          [format]="currencyFormat"
          alignment="left"
        >
        </dxi-column>
  
        <dxi-column
          dataField="vlrExercicio2"
          [caption]="+userData.exercicio - 3"
          [format]="currencyFormat"
          alignment="left"
        >
        </dxi-column>
  
        <dxi-column
          dataField="vlrExercicio3"
          [caption]="+userData.exercicio - 2"
          [format]="currencyFormat"
          alignment="left"
        >
        </dxi-column>
      </dxi-column>
      <dxi-column caption="Receita prevista" alignment="center">
        <dxi-column
          dataField="vlrExercicio4"
          [caption]="+userData.exercicio - 1"
          [format]="currencyFormat"
          alignment="left"
        >
        </dxi-column>
  
        <dxi-column
          dataField="vlrExercicio5"
          [caption]="+userData.exercicio"
          [format]="currencyFormat"
          alignment="left"
        >
        </dxi-column>
      </dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="40"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          title="Alterar"
          (click)="alterar(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
