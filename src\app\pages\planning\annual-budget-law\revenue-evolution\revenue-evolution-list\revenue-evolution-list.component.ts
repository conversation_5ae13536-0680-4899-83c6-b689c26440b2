import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { RevenuePlanSearchComponent } from '../revenue-plan-search/revenue-plan-search.component'
import { RevenueEvolutionService } from '../services/revenue-evolution.service'
import { filter, finalize, first, take } from 'rxjs/operators'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'

@Component({
  selector: 'eqp-revenue-evolution-list',
  templateUrl: './revenue-evolution-list.component.html',
  styleUrls: ['./revenue-evolution-list.component.scss'],
})
export class RevenueEvolutionListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle: string = 'Evolução da Receita'

  public dataSource: DataSource

  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  public userData: {
    exercicio: string
    exercicioUuid: string
  }

  constructor(
    private revenueEvolutionservice: RevenueEvolutionService,
    private userService: UserDataService,
    private dialogService: NbDialogService,
    private toastrService: ToastrService,
    public router: Router,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/lei-orcamentaria-anual/receita/evolucao')
  }

  ngOnInit(): void {
    const { exercicio, exercicioUuid } = this.userService.userData
    this.userData = {
      exercicio,
      exercicioUuid,
    }
    this.fetchGrid()
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.revenueEvolutionservice.getDataSourceFiltro(
        'uuid',
        'evolucao_receita/paginado',
        10,
        'exerciseUuid',
        this.userData.exercicioUuid,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Evolução da receita'
          item.options.hint = 'Nova Evolução da receita'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public alterar(value) {
    this.router.navigate([
      `lei-orcamentaria-anual/receita/evolucao/edit/${value}`,
    ])
  }

  public novoRegistro(): void {
    const dialogRef = this.dialogService.open(RevenuePlanSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.fetchGrid()
      }
    })
  }

  public modalAvisoImportar() {
    const dialogRef = this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          confirmType: 'success',
          cancelText: 'Não',
          cancelTitle: 'Não',
          confirmText: 'Sim',
          confirmTitle: 'Sim',
          body:
            'Este processo realizará uma busca automática da receita realizada' +
            ' e autorizada, para todos os exercícios possíveis.\n\nConfirma o processamento?',
        },
        dialogSize: 'medium',
        exiberIcones: false,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose
      .pipe(
        first(),
        filter(res => res),
      )
      .subscribe(res => {
        this.importarValores()
      })
  }

  private importarValores() {
    this.loading = true
    this.revenueEvolutionservice
      .importar()
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(_ => {
        this.toastrService.send({
          success: true,
          message: 'Importação realizada com sucesso.',
        })
        this.fetchGrid()
      })
  }
}
