<eqp-nebular-dialog
  [dialogTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [topRightButtonVisible]="isMultiple"
  [topRightButtonIconVisible]="true"
  [topRightButtonTitle]="'Importar todas as contas de receita'"
  [topRightButtonIcon]="'fas fa-file-import'"
  [topRightButtonId]="'submit-all-revenue-plan'"
  (topRightButtonEmitter)="import()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonId]="'submit-revenue-plan-selection'"
  [rightFirstButtonDisabled]="selectedRowKeys.length == 0"
  (rightFirstButtonEmitter)="getObject ? getObjects() : confirm()"
>
  <ng-container>
    <dx-data-grid
      id="planoReceitaGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      [(selectedRowKeys)]="selectedRowKeys"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>

      <dxo-selection
        selectAllMode="page"
        showCheckBoxesMode="onClick"
        [mode]="isMultiple ? 'multiple' : 'single'"
      ></dxo-selection>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar plano de receita"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column dataField="codigo" caption="Código"> </dxi-column>

      <dxi-column dataField="nome" caption="Nome"> </dxi-column>
    </dx-data-grid>
  </ng-container>
</eqp-nebular-dialog>
