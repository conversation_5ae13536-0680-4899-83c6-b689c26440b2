import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import { MenuService } from '@pages/menu.service';
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan';
import { RevenuePlanService } from '@pages/planning/multi-year-plan/services/revenue-plan.service';
import DataSource from 'devextreme/data/data_source';
import { finalize, first } from 'rxjs/operators';
import { RevenueEvolutionService } from '../services/revenue-evolution.service';

@Component({
  selector: 'eqp-revenue-plan-search',
  templateUrl: './revenue-plan-search.component.html',
  styleUrls: ['./revenue-plan-search.component.scss']
})
export class RevenuePlanSearchComponent
	extends BaseTelasComponent
	implements OnInit
{
  @Input() isMultiple = true
  @Input() getObject = false
  @Input() uri: string = 'plano_receita'

	pageTitle: string = 'Selecionar Receita'
  loading: boolean

  selectedRowKeys: string[] = []

  dataSource: DataSource<RevenuePlanInterface, string>

  constructor(
		public router: Router,
		public menuService: MenuService,
		private service: RevenuePlanService,
		private revenueEvolutionService: RevenueEvolutionService,
		private toastrService: ToastrService,
		private dialogRef: NbDialogRef<RevenuePlanSearchComponent>
	) {
		super(menuService, router)
		this.permissao('evolucao-receita')
	}

  ngOnInit(): void {
    this.fetchGrid()
  }

  fetchGrid() {
    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltroComposto(
				'uuid',
				this.uri,
				10,
				'["nivel","=","2"],"or",["nivel","=","1"]'
			),
      paginate: true,
      pageSize: 10,
    })
  }

  confirm() {
		this.loading = true
		this.revenueEvolutionService.postBatch(
			this.selectedRowKeys.map(value => ({
				planoReceitaUuid: value
			}))
		).pipe(
			first(),
			finalize(() => {
				this.loading = false
			})
		).subscribe(res => {
			this.toastrService.send({
				success: true,
				message: 'Planos de receita selecionados foram importados.'
			})
			this.dialogRef.close(res.status)
		})
  }

  async getObjects() {
    if (this.isMultiple) {
      const items = [];
      this.selectedRowKeys.forEach((key) => {
        const item = this.dataSource.items().find((item) => item.uuid === key);
        items.push(item);
      });
      this.dialogRef.close(items);
    } else {
      const item = this.dataSource.items().find((item) => item.uuid === this.selectedRowKeys[0]);
      this.dialogRef.close(item)
    }
  }

	import() {
		this.loading = true
		this.revenueEvolutionService.importar()
			.pipe(
				first(),
				finalize(() => {
					this.loading = false
				})
			).subscribe(res => {
				this.toastrService.send({
					success: true,
					message: 'Todos os planos de receita foram importados.'
				})
				this.dialogRef.close(res.status)
		})
	}

  cancel() {
    this.dialogRef.close(null)
  }
}
