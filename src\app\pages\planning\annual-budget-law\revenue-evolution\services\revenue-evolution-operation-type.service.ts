import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { Observable } from 'rxjs';
import { RevenueEvolutionOperationTypeInterface } from '../interfaces/revenue-evolution-operation-type';

@Injectable({
  providedIn: 'root'
})
export class RevenueEvolutionOperationTypeService extends BaseService<
	ResponseDto<RevenueEvolutionOperationTypeInterface[]>,
	RevenueEvolutionOperationTypeInterface
> {

  constructor(protected http: HttpClient) {
		super(http, 'evolucao_receita_tipo_operacao')
	}

	public getPaginado(filters?: any) {
		const headers = new HttpHeaders()
		let params = new HttpParams()

		if (filters) {
			Object.keys(filters).forEach(p => (params = params.append(p, filters[p])))
		}

		return this.http.get<any>(
			`evolucao_receita_tipo_operacao/paginado`,
			{
				headers,
				params,
			},
		)
	}

	public import() {
		const headers = new HttpHeaders()
    return this.http.post<null>('evolucao_receita_tipo_operacao/importar', null, {
      headers,
      observe: 'response',
    })
  }
}
