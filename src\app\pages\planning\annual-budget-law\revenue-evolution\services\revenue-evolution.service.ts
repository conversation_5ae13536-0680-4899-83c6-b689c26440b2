import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { RevenueEvolutionInterface } from '../interfaces/revenue-evolution';

@Injectable({
  providedIn: 'root'
})
export class RevenueEvolutionService extends BaseService<
	ResponseDto<RevenueEvolutionInterface[]>,
	RevenueEvolutionInterface
> {
  constructor(protected http: HttpClient) {
		super(http, 'evolucao_receita')
	}

	public getPaginado(filters?: any) {
    const headers = new HttpHeaders()
    let params = new HttpParams()

    if (filters) {
      Object.keys(filters).forEach(p => (params = params.append(p, filters[p])))
    }

    return this.http.get<any>(
      `evolucao_receita/paginado`,
      {
        headers,
        params,
      },
    )
  }

	public importar() {
		const headers = new HttpHeaders()
		return this.http.post<null>(
			'evolucao_receita/importar', null, {
				headers,
				observe: 'response'
			}
		)
	}
}
