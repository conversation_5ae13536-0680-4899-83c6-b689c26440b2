import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { OperationRevenueTypeInterface } from '../../initial-revenue-forecast/interfaces/operation-revenue-type';

@Injectable({
  providedIn: 'root'
})
export class RevenueOperationTypeService extends BaseService<
	ResponseDto<OperationRevenueTypeInterface>,
	OperationRevenueTypeInterface
> {

  constructor(protected http: HttpClient) {
		super(http, 'tipo_operacao_receita')
	}
}