<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
	[topRightButtonVisible]="true"
	[topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update-revenue-from-to-data'"
  (topRightButtonEmitter)="carregarTela()"
  [topRightButtonTitle]="'Atualizar'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [rightApproveButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancelar(null)"
  [rightApproveButtonIconVisible]="true"
  [rightApproveButtonIcon]="'fas fa-save'"
  [rightApproveButtonId]="'submit-revenue-from-to-data'"
  [rightApproveButtonDisabled]="!selectedRowKeysOrigem.length || !selectedRowKeysDestino.length"
  (rightApproveButtonEmitter)="gravar()"
>
<ng-container *ngIf="contasDeOrigemData && contasDeDestinoData">
	<div class="row">
		<div class="col col-12 col-md-6">
			<dx-data-grid
				id="planoReceitaGrid"
				[dataSource]="contasDeOrigemData"
				[allowColumnResizing]="true"
				[columnAutoWidth]="true"
				[showColumnLines]="false"
				[showRowLines]="false"
				[showBorders]="false"
				[rowAlternationEnabled]="true"
				[wordWrapEnabled]="true"
				[loadPanel]="false"
				[columnHidingEnabled]="true"
				[remoteOperations]="true"
				keyExpr="uuid"
				[selectedRowKeys]="selectedRowKeysOrigem"
				(onSelectionChanged)="onSelectionChangedOrigem($event)"
			>
				<dxo-state-storing
					[enabled]="true"
					type="custom"
					[customLoad]="loadState"
					[customSave]="saveState"
					savingTimeout="100"
				></dxo-state-storing>

				<dxo-selection
					selectAllMode="allPages"
					showCheckBoxesMode="onClick"
					mode="single"
				></dxo-selection>

				<dxo-paging [pageSize]="10"></dxo-paging>
				<dxo-pager
					[showInfo]="true"
					[showNavigationButtons]="true"
					[showPageSizeSelector]="false"
				>
				</dxo-pager>

				<dxo-header-filter [visible]="false"> </dxo-header-filter>
				<dxo-filter-row [visible]="true"></dxo-filter-row>

				<dxo-sorting mode="multiple"></dxo-sorting>

				<dxo-column-chooser [enabled]="false"></dxo-column-chooser>

				<dxo-group-panel
					[visible]="false"
					[emptyPanelText]="''"
				></dxo-group-panel>

				<dxo-search-panel
					[visible]="true"
					placeholder="Buscar receita"
				></dxo-search-panel>

				<dxo-editing
					mode="form"
					[allowUpdating]="false"
					[allowDeleting]="false"
					[allowAdding]="false"
					[useIcons]="true"
				>
				</dxo-editing>

				<dxi-column alignment="center" caption="Origem">
					<dxi-column dataField="codigo" caption="Código">
					</dxi-column>
					<dxi-column dataField="nome" caption="Nome"> </dxi-column>
				</dxi-column>
			</dx-data-grid>
		</div>
		<!-- --------------------------------------- -->
		<div class="col col-12 col-md-6">
			<dx-data-grid
				id="planoReceitaGrid"
				[dataSource]="contasDeDestinoData"
				[allowColumnResizing]="true"
				[columnAutoWidth]="true"
				[showColumnLines]="false"
				[showRowLines]="false"
				[showBorders]="false"
				[rowAlternationEnabled]="true"
				[wordWrapEnabled]="true"
				[loadPanel]="false"
				[columnHidingEnabled]="true"
				[remoteOperations]="true"
				keyExpr="uuid"
				[selectedRowKeys]="selectedRowKeysDestino"
				(onSelectionChanged)="onSelectionChangedDestino($event)"
			>
				<dxo-state-storing
					[enabled]="true"
					type="custom"
					[customLoad]="loadState"
					[customSave]="saveState"
					savingTimeout="100"
				></dxo-state-storing>

				<dxo-selection
					selectAllMode="allPages"
					showCheckBoxesMode="onClick"
					mode="single"
				></dxo-selection>

				<dxo-paging [pageSize]="10"></dxo-paging>
				<dxo-pager
					[showInfo]="true"
					[showNavigationButtons]="true"
					[showPageSizeSelector]="false"
				>
				</dxo-pager>

				<dxo-header-filter [visible]="false"> </dxo-header-filter>
				<dxo-filter-row [visible]="true"></dxo-filter-row>

				<dxo-sorting mode="multiple"></dxo-sorting>

				<dxo-column-chooser [enabled]="false"></dxo-column-chooser>

				<dxo-group-panel
					[visible]="false"
					[emptyPanelText]="''"
				></dxo-group-panel>

				<dxo-search-panel
					[visible]="true"
					placeholder="Buscar receita"
				></dxo-search-panel>

				<dxo-editing
					mode="form"
					[allowUpdating]="false"
					[allowDeleting]="false"
					[allowAdding]="false"
					[useIcons]="true"
				>
				</dxo-editing>

				<dxi-column alignment="center" caption="Destino">
					<dxi-column dataField="codigo" caption="Código">
					</dxi-column>
					<dxi-column dataField="nome" caption="Nome"> </dxi-column>
				</dxi-column>

			</dx-data-grid>
		</div>
	</div>
</ng-container>

</eqp-standard-page>
