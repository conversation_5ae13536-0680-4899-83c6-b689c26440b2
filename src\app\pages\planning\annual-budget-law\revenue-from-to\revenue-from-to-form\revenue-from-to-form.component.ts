import { Component, Input, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { MenuService } from '@pages/menu.service'
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan'
import { RevenuePlanService } from '@pages/planning/multi-year-plan/services/revenue-plan.service'
import DataSource from 'devextreme/data/data_source'
import {
  finalize, first,
} from 'rxjs/operators'
import { RevenueFromToService } from '../services/revenue-from-to.service'

@Component({
  selector: 'eqp-revenue-from-to-form',
  templateUrl: './revenue-from-to-form.component.html',
  styleUrls: ['./revenue-from-to-form.component.scss']
})

export class RevenueFromToFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle: string = 'De/para das contas de receita | Novo'
	public contasOrigem: RevenuePlanInterface[]
	public contasDestino: RevenuePlanInterface[]
  public contasDeOrigemData: DataSource
  public contasDeDestinoData: DataSource
	public selectedRowKeysOrigem: string[] = []
	public selectedRowKeysDestino: string[] = []

  constructor(
    private revenueFromToservice: RevenueFromToService,
		private revenueService: RevenuePlanService,
    public router: Router,
    private toastr: ToastrService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('de-para')
  }

  ngOnInit(): void {
		this.carregarTela()
  }

	public carregarTela() {
		this.loading = true
		this.revenueFromToservice.getRevenuePlanFilter()
			.pipe(
				first(),
				finalize(() => this.loading = false)
			)
			.subscribe(res => {
				this.fetchGrids(res.body.dados)
			})
	}

  async fetchGrids({origem, destino}) {
    this.contasDeOrigemData = new DataSource({
      store: {
				type: 'array',
				data: origem,
				key: 'uuid'
			},
      paginate: true,
      pageSize: 10,
  	})

    this.contasDeDestinoData = new DataSource({
      store: {
				type: 'array',
				data: destino,
				key: 'uuid'
			},
      paginate: true,
      pageSize: 10,
    })
  }

  public cancelar(retorno): void {
    this.router.navigate([`lei-orcamentaria-anual/receita/de-para`])
  }
	
  public gravar(): void{
		this.loading = true
		const dto = {
			origem: {
				uuid: this.selectedRowKeysOrigem[0]
			},
			destino:{
				uuid: this.selectedRowKeysDestino[0]
			}
		}
		this.revenueFromToservice
			.post(dto)
			.pipe(finalize(() => (this.loading = false)))
			.subscribe(res => {
					this.toastr.send({
						success: true,
						message: 'Cadastro feito com sucesso.',
					})
					this.router.navigate([`lei-orcamentaria-anual/receita/de-para`])
				},
			)
  }

	public onSelectionChangedOrigem(event) {
		this.selectedRowKeysOrigem = event['currentSelectedRowKeys']
	}

	public onSelectionChangedDestino(event) {
		this.selectedRowKeysDestino = event['currentSelectedRowKeys']
	}
}

