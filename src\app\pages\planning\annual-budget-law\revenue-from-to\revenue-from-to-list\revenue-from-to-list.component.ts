import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { RevenueFromToInterface } from '../interfaces/revenue-from-to'
import { RevenueFromToService } from '../services/revenue-from-to.service'

@Component({
  selector: 'eqp-revenue-from-to-list',
  templateUrl: './revenue-from-to-list.component.html',
  styleUrls: ['./revenue-from-to-list.component.scss'],
})
export class RevenueFromToListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'De/para das contas de receita'
  public dataSource: DataSource<RevenueFromToInterface, string>
  public activeData: any

  private subscription: Subscription

  constructor(
    private service: RevenueFromToService,
    private toastrService: ToastrService,
    private dialogService: NbDialogService,
    public router: Router,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/lei-orcamentaria-anual/receita/de-para')
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'plano_receita_de_para',
        10,
      ),
    })
  }

  public novoRegistro(): void {
    this.router.navigate([`lei-orcamentaria-anual/receita/de-para/novo`])
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'De/Para Conta De Receita'
          item.options.hint = 'Nova migração'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public remove(value) {
    const ref = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose.pipe(first()).subscribe(res => {
      if (res == 'S') {
        this.loading = true
        this.service
          .delete(value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(() => {
            this.toastrService.send({
              success: true,
              message: 'Deletado com sucesso.',
            })
            this.fetchGrid()
          })
      }
    })
  }
}
