import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { Observable } from 'rxjs';
import { RevenueFromToInterface } from '../interfaces/revenue-from-to';

@Injectable({
  providedIn: 'root'
})
export class RevenueFromToService extends BaseService<
	ResponseDto<RevenueFromToInterface[]>,
	RevenueFromToInterface
> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'plano_receita_de_para')
  }

	public getRevenuePlanFilter() {
    const headers = new HttpHeaders()
    let params = new HttpParams()

    return this.http.get<
		ResponseDto<{
			origem: RevenuePlanInterface[],
			destino: RevenuePlanInterface[]
		}>
		>('plano_receita/origem_destino', {
      headers,
      params,
			observe: 'response'
    })
  }
}
