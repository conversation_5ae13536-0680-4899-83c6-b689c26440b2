<eqp-standard-page [mainTitle]="pageTitle">
  <div class="container" [formGroup]="model">
    <eqp-loading *ngIf="loading"></eqp-loading>
    <div class="row">
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Código *"
          placeholder=""
          formControlName="codigo"
          type="number"
          [disabled]="disableInputs"
        >
        </eqp-nebular-input>
        <small class="d-flex justify-content-end mt-1 text-danger">
          {{
            model.getError('codeInvalid')
              ? '*O código não pode ultrapassar os 10 dígitos.'
              : ''
          }}
        </small>
      </div>
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Nome *"
          placeholder=""
          formControlName="nome"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo *"
          placeholder=""
          formControlName="tipoGrupoCalculo"
          [dataSource]="typeData"
          displayExpr="name"
          valueExpr="uuid"
        >
        </eqp-nebular-select>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Meses Ex. Corrente *"
          placeholder=""
          primaryMask="99"
          formControlName="mesExercicioCorrente"
        >
        </eqp-nebular-input>
        <small class="d-flex justify-content-end mt-1 text-danger">
          {{
            model.getError('monthInvalid')
              ? '*Somente meses entre 1 e 12 são válidos.'
              : ''
          }}
        </small>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [label]="loggedExercise"
          placeholder="00,00 %"
          required="true"
          formControlName="percentualExercicioCorrente"
          [style]="'currency'"
          [options]="{
            prefix: ' ',
            suffix: ' %',
            thousands: '.',
            decimal: ','
          }"
          [disabled]="yearsFieldsDisabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [label]="loggedExercise + 1"
          placeholder="00,00 %"
          required="true"
          formControlName="percentualExercicio1"
          [style]="'currency'"
          [options]="{
            prefix: ' ',
            suffix: ' %',
            thousands: '.',
            decimal: ','
          }"
          [disabled]="yearsFieldsDisabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [label]="loggedExercise + 2"
          placeholder="00,00 %"
          required="true"
          formControlName="percentualExercicio2"
          [style]="'currency'"
          [options]="{
            prefix: ' ',
            suffix: ' %',
            thousands: '.',
            decimal: ','
          }"
          [disabled]="yearsFieldsDisabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [label]="loggedExercise + 3"
          placeholder="00,00 %"
          required="true"
          formControlName="percentualExercicio3"
          [style]="'currency'"
          [options]="{
            prefix: ' ',
            suffix: ' %',
            thousands: '.',
            decimal: ','
          }"
          [disabled]="yearsFieldsDisabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [label]="loggedExercise + 4"
          placeholder="00,00 %"
          required="true"
          formControlName="percentualExercicio4"
          [style]="'currency'"
          [options]="{
            prefix: ' ',
            suffix: ' %',
            thousands: '.',
            decimal: ','
          }"
          [disabled]="yearsFieldsDisabled"
        >
        </eqp-nebular-input>
      </div>
    </div>

    <div class="d-flex mt-4 justify-content-end mt-3" style="gap: 0.5rem">
      <eqp-nebular-button
        [buttonShape]="'rectangle'"
        [buttonText]="'Voltar'"
        (buttonEmitter)="cancel()"
        [buttonType]="'primary'"
        [buttonAppearance]="'outline'"
        [buttonIcon]="'fas fa-undo-alt'"
        [buttonIconVisible]="true"
        [buttonVisible]="true"
      ></eqp-nebular-button>
      <eqp-nebular-button
        [buttonShape]="'rectangle'"
        [buttonText]="'Salvar'"
        (buttonEmitter)="confirm()"
        [buttonType]="'success'"
        [buttonIcon]="'fas fa-save'"
        [buttonIconVisible]="true"
        [buttonVisible]="true"
        [buttonDisabled]="!model.valid || model.pristine"
      ></eqp-nebular-button>
    </div>
  </div>
</eqp-standard-page>
