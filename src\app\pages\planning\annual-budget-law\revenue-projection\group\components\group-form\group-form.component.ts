import { Component, OnInit } from '@angular/core'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import DataSource from 'devextreme/data/data_source'
import { Observable, Subscription } from 'rxjs'
import { distinctUntilChanged, finalize, first, take } from 'rxjs/operators'
import { RevenueProjectionCalculationGroupInterface } from '../../interfaces/revenue-projection-calculation-group'
import { GroupService } from '../../services/group.service'

@Component({
  selector: 'eqp-group-form',
  templateUrl: './group-form.component.html',
  styleUrls: ['./group-form.component.scss'],
})
export class GroupFormComponent implements OnInit {

  public loading = false

  pageTitle = 'Grupo Cálculo'

  typeData: DataSource

  typeSubscription: Subscription
  yearsFieldsDisabled: boolean = false

  loggedExercise: number
  uuid: string

  model: FormGroup

  disableInputs: boolean = false

  constructor(
    private builder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private service: GroupService,
    private toastrService: ToastrService,
    private crudService: CrudService
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.service.getExerciseInfo().subscribe(exerciseInfo => {
      this.loggedExercise = exerciseInfo.dados.exercicio
    })
    this.loadSelects()
    this.enableYearsFields()

    const { uuid } = this.route.snapshot.params
    this.uuid = uuid

    if (uuid) {
      this.fetchPageData(uuid)
      this.disableInputs = true
    }
  }

  fetchPageData(uuid: string) {
    this.loading = true
    this.service
      .getOne(uuid)
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.loadForm(res)
      })
  }

  public getNewModel() {
    return this.builder.group(
      {
        uuid: [],
        codigo: ['', Validators.required],
        nome: ['', Validators.required],
        tipoGrupoCalculo: ['', Validators.required],
        mesExercicioCorrente: [null, Validators.required],
        percentualExercicioCorrente: [0],
        percentualExercicio1: [0],
        percentualExercicio2: [0],
        percentualExercicio3: [0],
        percentualExercicio4: [0],
      },
      {
        validator: Validators.compose([this.validateCode, this.validateMonths])
      },
    )
  }

  validateCode(control: AbstractControl){
    const code = control.get('codigo').value
    if(String(code).length > 10){
      return { codeInvalid: true }
    }

    return null
  }

  validateMonths(control: AbstractControl) {
    const month = control.get('mesExercicioCorrente').value
    if ((month < 1 || month > 12) && month) {
      return { monthInvalid: true }
    }

    return null
  }

  enableYearsFields() {
    this.typeSubscription = this.model
      .get('tipoGrupoCalculo')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe((typeUuid: string) => {
        this.typeData.store().byKey(typeUuid).then(res => {
          if(res.codigo == 1){
            this.yearsFieldsDisabled = true
          } else {
            this.yearsFieldsDisabled = false
          }
        })
      })
  }

  private loadForm(data: RevenueProjectionCalculationGroupInterface) {
    const dto = {
      ...data,
      tipoGrupoCalculo: data.tipoGrupoCalculo.uuid ? data.tipoGrupoCalculo.uuid : null,
    }
    this.model.patchValue(dto)
  }

  prepare(formData: any) {
    const dto = {
      ...formData,
      tipoGrupoCalculo: formData.tipoGrupoCalculo ? {uuid: formData.tipoGrupoCalculo} : null,
      codigo: formData.codigo ? Number(formData.codigo) : null,
      mesExercicioCorrente: formData.mesExercicioCorrente ? Number(formData.mesExercicioCorrente) : null,
      exercicio: this.loggedExercise ? this.loggedExercise : null
    }

    return dto
  }

  loadSelects(){
    this.typeData = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'grupo_calculo/tipo_grupo_calculo',
        10
      ),
      paginate: true,
      pageSize: 10
    })
  }

  cancel() {
    this.router.navigate([
      'lei-orcamentaria-anual/receita/projecao-receita/grupo-calculo',
    ])
  }

  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue())

      let req: Observable<any>
      if (this.uuid) {
        req = this.service.put(dto)
      } else {
        req = this.service.post(dto)
      }
      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe(res => {
          this.toastrService.send({
            title: 'Sucesso',
            message: `${this.pageTitle}
                  ${this.uuid ? 'atualizado' : 'cadastrado'} com sucesso!`,
            success: true,
          })
          this.model.markAsPristine()
          this.router.navigate([`lei-orcamentaria-anual/receita/projecao-receita/grupo-calculo/edit/${res.body.dados.uuid}`])
        })
    }
  }
}
