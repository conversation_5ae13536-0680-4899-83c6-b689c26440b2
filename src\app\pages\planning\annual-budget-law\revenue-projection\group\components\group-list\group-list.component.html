<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonTitle]="'Atualizar'"
  [topRightButtonId]="'groupList'"
  (topRightButtonEmitter)="fetchGrid()"
>
  <ng-container>
    <dx-data-grid
      id="groupListGrid"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [dataSource]="dataSource"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>

      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar registro"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        alignment="left"
        dataField="codigo"
        caption="Código"
      ></dxi-column>

      <dxi-column alignment="left" dataField="nome" caption="Nome"></dxi-column>

      <dxi-column
        alignment="left"
        dataField="mesExercicioCorrente"
        caption="Mês"
      ></dxi-column>

      <dxi-column
        alignment="left"
        dataField="percentualExercicioCorrente"
        [caption]="+loggedExercise"
        [allowEditing]="false"
        [editorOptions]="{ placeholder: '00' }"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        alignment="left"
        dataField="percentualExercicio1"
        [caption]="+loggedExercise + 1"
        [allowEditing]="false"
        [editorOptions]="{ placeholder: '00' }"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        alignment="left"
        dataField="percentualExercicio2"
        [caption]="+loggedExercise + 2"
        [allowEditing]="false"
        [editorOptions]="{ placeholder: '00' }"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        alignment="left"
        dataField="percentualExercicio3"
        [caption]="+loggedExercise + 3"
        [allowEditing]="false"
        [editorOptions]="{ placeholder: '00' }"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        alignment="left"
        dataField="percentualExercicio4"
        [caption]="+loggedExercise + 4"
        [allowEditing]="false"
        [editorOptions]="{ placeholder: '00' }"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          title="Alterar"
          (click)="edit(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          *ngIf="nivelPermissao === 'FULL'"
          title="Remover"
          (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
