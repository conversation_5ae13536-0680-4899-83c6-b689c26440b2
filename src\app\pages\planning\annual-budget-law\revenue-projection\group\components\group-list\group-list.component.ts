import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, take } from 'rxjs/operators'
import { GroupService } from '../../services/group.service'

@Component({
  selector: 'eqp-group-list',
  templateUrl: './group-list.component.html',
  styleUrls: ['./group-list.component.scss'],
})
export class GroupListComponent extends BaseTelasComponent implements OnInit {
  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  public loading: boolean = false
  public pageTitle = 'Grupo Cálculo'

  loggedExercise: number

  dataSource: DataSource

  constructor(
    public menuService: MenuService,
    public router: Router,
    public toastr: ToastrService,
    private crudService: CrudService,
    private service: GroupService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-orcamentaria-anual/receita/projecao-receita/grupo-calculo',
    )
  }

  ngOnInit(): void {
    this.fetchGrid()
    this.loading = true
    this.service
      .getExerciseInfo()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(exerciseInfo => {
        this.loggedExercise = exerciseInfo.dados.exercicio
      })
  }

  customDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro('uuid', `grupo_calculo`, 10),
      paginate: true,
      pageSize: 10,
    })
  }

  public new(): void {
    this.router.navigate([
      `lei-orcamentaria-anual/receita/projecao-receita/grupo-calculo/novo`,
    ])
  }

  public edit(value) {
    this.router.navigate([
      `lei-orcamentaria-anual/receita/projecao-receita/grupo-calculo/edit/${value}`,
    ])
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Grupo cálculo'
          item.options.hint = 'Cadastrar grupo cálculo'
          item.options.onClick = () => this.new()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(res => {
      this.loading = true
      if (res === 'S') {
        this.service
          .delete(uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Grupo cálculo removido(a) com sucesso',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
