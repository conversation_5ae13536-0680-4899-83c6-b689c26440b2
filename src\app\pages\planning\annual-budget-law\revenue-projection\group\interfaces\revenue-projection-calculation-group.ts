export interface RevenueProjectionCalculationGroupInterface {
  uuid:string
  codigo: number
  nome: string
  tipoGrupoCalculo: CalculationGroupTypeInterface
  mesExercicioCorrente: number
  percentualExercicioCorrente: number
  percentualExercicio1: number
  percentualExercicio2: number
  percentualExercicio3: number
  percentualExercicio4: number
}

export interface CalculationGroupTypeInterface{
	uuid: string
	codigo: number
	nome: string
}
