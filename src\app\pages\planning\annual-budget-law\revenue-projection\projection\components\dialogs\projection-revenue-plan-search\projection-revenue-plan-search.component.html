<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <dx-data-grid
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="false"
      [remoteOperations]="true"
      keyExpr="uuid"
      [remoteOperations]="true"
      (onSelectionChanged)="onSelectionChanged($event)"
    >
      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-selection
        [mode]="isMultiple ? 'multiple' : 'single'"
      ></dxo-selection>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column dataField="codigo" caption="Código"></dxi-column>

      <dxi-column dataField="nome" caption="Nome"></dxi-column>

      <dxi-column dataField="analitica" caption="Analítica">
        <dxo-lookup [dataSource]="flagDados" displayExpr="text" valueExpr="value"></dxo-lookup>
      </dxi-column>

      <dxi-column dataField="flagTemRateio" caption="Tem rateio">
        <dxo-lookup [dataSource]="flagDados" displayExpr="text" valueExpr="value"></dxo-lookup>
      </dxi-column>

      <dxi-column
        dataField="tipoDiarioArrecadacao.nome"
        caption="Tipo de arrecadação"
      ></dxi-column>
    </dx-data-grid>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="!selected"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
