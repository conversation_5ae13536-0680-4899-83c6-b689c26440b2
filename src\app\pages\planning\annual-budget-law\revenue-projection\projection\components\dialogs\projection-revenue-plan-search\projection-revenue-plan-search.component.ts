import { Component, Input, OnInit } from '@angular/core';
import { CrudService } from '@common/services/crud.service';
import { NbDialogRef } from '@nebular/theme';
import DataSource from 'devextreme/data/data_source';
import { Subscription } from 'rxjs';

@Component({
  selector: 'eqp-projection-revenue-plan-search',
  templateUrl: './projection-revenue-plan-search.component.html',
  styleUrls: ['./projection-revenue-plan-search.component.scss']
})
export class ProjectionRevenuePlanSearchComponent implements OnInit {

  public loading: boolean = false

  @Input() public pageTitle = 'Plano receita | Busca'

  @Input() public isMultiple = false;
  @Input() public filter: any;
  @Input() public uri =
    'projecao_receita/plano_receita';

  public selected: any
  public dataSource: DataSource
  private subscription: Subscription
  public flagDados = [
    {
      text: 'Sim',
      value: 'S',
    },
    {
      text: 'Não',
      value: 'N',
    },
  ]

  constructor(
    protected dialogRef: NbDialogRef<ProjectionRevenuePlanSearchComponent>,
    private crudService: CrudService,
  ) {}

  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  private async fetchGrid() {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro('uuid', `${this.uri}`, 10),
      paginate: true,
      pageSize: 10,
    })
  }

  public onSelectionChanged(event: any) {
    this.selected = event.selectedRowsData
  }

  public async confirm() {
    if (this.selected) {
      this.dialogRef.close(this.selected)
    }
  }

  public cancel(): void {
    this.dialogRef.close(false)
  }
}
