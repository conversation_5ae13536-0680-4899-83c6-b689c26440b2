export const mock: any[] = [
  {
    uuid: 123,
    valor: 778,
    exercicioProjecao: 2024,
    tipoOperacaoReceita: {
      codigo: 5,
      nome: 'Deduções de Receita para a formação do FUNDED',
    },
  },
  {
    uuid: 123,
    valor: 325,
    exercicioProjecao: 2021,
    tipoOperacaoReceita: { codigo: 99, nome: 'Outras deduções' },
  },
  {
    uuid: 123,
    valor: 666,
    exercicioProjecao: 2020,
    tipoOperacaoReceita: { codigo: 99, nome: 'Outras deduções' },
  },
  {
    uuid: 123,
    valor: 7778,
    exercicioProjecao: 2025,
    tipoOperacaoReceita: { codigo: 99, nome: 'Outras deduções' },
  },
  {
    uuid: 123,
    valor: 889,
    exercicioProjecao: 2023,
    tipoOperacaoReceita: { codigo: 99, nome: 'Outras deduções' },
  },
  {
    uuid: 123,
    valor: 6556,
    exercicioProjecao: 2024,
    tipoOperacaoReceita: { codigo: 99, nome: 'Outras deduções' },
  },
  {
    uuid: 123,
    valor: 34677,
    exercicioProjecao: 2022,
    tipoOperacaoReceita: { codigo: 99, nome: 'Outras deduções' },
  },
  {
    uuid: 123,
    valor: 667,
    exercicioProjecao: 2020,
    tipoOperacaoReceita: { codigo: 3, nome: 'Restituições' },
  },
  {
    uuid: 123,
    valor: 12,
    exercicioProjecao: 2021,
    tipoOperacaoReceita: { codigo: 3, nome: 'Restituições' },
  },
  {
    uuid: 123,
    valor: 45,
    exercicioProjecao: 2022,
    tipoOperacaoReceita: { codigo: 3, nome: 'Restituições' },
  },
  {
    uuid: 123,
    valor: 123,
    exercicioProjecao: 2023,
    tipoOperacaoReceita: { codigo: 3, nome: 'Restituições' },
  },
  {
    uuid: 123,
    valor: 4126,
    exercicioProjecao: 2024,
    tipoOperacaoReceita: { codigo: 3, nome: 'Restituições' },
  },
  {
    uuid: 123,
    valor: 34566,
    exercicioProjecao: 2025,
    tipoOperacaoReceita: { codigo: 3, nome: 'Restituições' },
  },
  {
    uuid: 123,
    valor: 88,
    exercicioProjecao: 2020,
    tipoOperacaoReceita: { codigo: 2, nome: 'Renúncia' },
  },
  {
    uuid: 123,
    valor: 2143,
    exercicioProjecao: 2021,
    tipoOperacaoReceita: { codigo: 2, nome: 'Renúncia' },
  },
  {
    uuid: 123,
    valor: 43,
    exercicioProjecao: 2022,
    tipoOperacaoReceita: { codigo: 2, nome: 'Renúncia' },
  },
  {
    uuid: 123,
    valor: 5456,
    exercicioProjecao: 2023,
    tipoOperacaoReceita: { codigo: 2, nome: 'Renúncia' },
  },
  {
    exercicioProjecao: 2024,
    uuid: 123,
    valor: 88131,
    tipoOperacaoReceita: { codigo: 2, nome: 'Renúncia' },
  },
  {
    uuid: 123,
    valor: 1235,
    exercicioProjecao: 2025,
    tipoOperacaoReceita: { codigo: 2, nome: 'Renúncia' },
  },
  {
    uuid: 123,
    valor: 100,
    exercicioProjecao: 2020,
    tipoOperacaoReceita: { codigo: 1, nome: 'Receita' },
  },
  {
    uuid: 123,
    valor: 23,
    exercicioProjecao: 2021,
    tipoOperacaoReceita: { codigo: 1, nome: 'Receita' },
  },
  {
    uuid: 123,
    valor: 44,
    exercicioProjecao: 2022,
    tipoOperacaoReceita: { codigo: 1, nome: 'Receita' },
  },
  {
    uuid: 123,
    valor: 77,
    exercicioProjecao: 2023,
    tipoOperacaoReceita: { codigo: 1, nome: 'Receita' },
  },
  {
    uuid: 123,
    valor: 88,
    exercicioProjecao: 2024,
    tipoOperacaoReceita: { codigo: 1, nome: 'Receita' },
  },
  {
    uuid: 123,
    valor: 667,
    tipoOperacaoReceita: { codigo: 1, nome: 'Receita' },
    exercicioProjecao: 2025,
  },
  {
    uuid: 123,
    valor: 11,
    exercicioProjecao: 2020,
    tipoOperacaoReceita: { codigo: 4, nome: 'Descontos concedidos' },
  },
  {
    uuid: 123,
    valor: 223,
    exercicioProjecao: 2021,
    tipoOperacaoReceita: { codigo: 4, nome: 'Descontos concedidos' },
  },
  {
    uuid: 123,
    valor: 5423,
    exercicioProjecao: 2022,
    tipoOperacaoReceita: { codigo: 4, nome: 'Descontos concedidos' },
  },
  {
    uuid: 123,
    valor: 677,
    exercicioProjecao: 2023,
    tipoOperacaoReceita: { codigo: 4, nome: 'Descontos concedidos' },
  },
  {
    uuid: 123,
    valor: 88,
    exercicioProjecao: 2024,
    tipoOperacaoReceita: { codigo: 4, nome: 'Descontos concedidos' },
  },
  {
    uuid: 123,
    valor: 78,
    exercicioProjecao: 2025,
    tipoOperacaoReceita: { codigo: 4, nome: 'Descontos concedidos' },
  },
  {
    uuid: 123,
    valor: 45457,
    exercicioProjecao: 2020,
    tipoOperacaoReceita: {
      codigo: 5,
      nome: 'Deduções de Receita para a formação do FUNDED',
    },
  },
  {
    uuid: 123,
    valor: 6798,
    exercicioProjecao: 2022,
    tipoOperacaoReceita: {
      codigo: 5,
      nome: 'Deduções de Receita para a formação do FUNDED',
    },
  },
  {
    uuid: 123,
    valor: 67787,
    exercicioProjecao: 2021,
    tipoOperacaoReceita: {
      codigo: 5,
      nome: 'Deduções de Receita para a formação do FUNDED',
    },
  },
  {
    uuid: 123,
    valor: 6556,
    exercicioProjecao: 2023,
    tipoOperacaoReceita: {
      codigo: 5,
      nome: 'Deduções de Receita para a formação do FUNDED',
    },
  },
  {
    uuid: 123,
    exercicioProjecao: 2025,
    valor: 34677,
    tipoOperacaoReceita: {
      codigo: 5,
      nome: 'Deduções de Receita para a formação do FUNDED',
    },
  },
]
