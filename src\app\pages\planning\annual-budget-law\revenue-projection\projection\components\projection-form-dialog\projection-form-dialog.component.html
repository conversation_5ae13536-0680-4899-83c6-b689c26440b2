<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row mt-3">
      <div class="col col-12 col-md-3" formGroupName="planoReceita">
        <ng-container class="d-flex flex-row">
          <label class="label">Conta de receita *</label>
          <input
            nbInput
            [fullWidth]="true"
            [fieldSize]="'small'"
            [shape]="'rectangle'"
            label="Conta de receita *"
            maxlength="29"
            appRevenuePlanMask
            placeholder="Código"
            formControlName="codigo"
            [readonly]="disableInputs"
          />
          <button
            class="button"
            [size]="'small'"
            [title]="'Buscar'"
            nbSuffix
            nbButton
            (click)="onRevenuePlanSearchDialog()"
            ghost
            [disabled]="disableInputs"
          >
            <nb-icon icon="search" pack="eva"> </nb-icon>
          </button>
        </ng-container>
      </div>
      <div class="col col-12 col-md-5" formGroupName="planoReceita">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Nome *"
          placeholder="Nome"
          formControlName="nome"
          readonly="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-4">
        <eqp-nebular-select
          [disabled]="disableInputs"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Grupo de cálculo da Projeção da receita *"
          placeholder=""
          [dataSource]="calculationGroupData"
          [displayExpr]="customDisplay"
          valueExpr="uuid"
          formControlName="projecaoReceitaGrupoCalculo"
        >
        </eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="model.pristine || !model.valid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
