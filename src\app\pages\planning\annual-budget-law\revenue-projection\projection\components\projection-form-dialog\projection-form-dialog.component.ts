import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan'
import DataSource from 'devextreme/data/data_source'
import { Subject } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  finalize,
  first,
  take,
  takeUntil,
} from 'rxjs/operators'
import { RevenueProjectionInterface } from '../../interfaces/revenue-projection'
import { ProjectionService } from '../../services/projection.service'
import { ProjectionRevenuePlanSearchComponent } from '../dialogs/projection-revenue-plan-search/projection-revenue-plan-search.component'

@Component({
  selector: 'eqp-projection-form-dialog',
  templateUrl: './projection-form-dialog.component.html',
  styleUrls: ['./projection-form-dialog.component.scss'],
})
export class ProjectionFormDialogComponent implements OnInit {
  pageTitle = 'Projeção da receita - LOA'

  loading: boolean = false

  model: FormGroup

  calculationGroupData: DataSource
  revenuePlanData: DataSource

  disableInputs: boolean = false

  private unsub$ = new Subject<null>()

  @Input() uuid: string

  constructor(
    private service: ProjectionService,
    private crudService: CrudService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<ProjectionFormDialogComponent>,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {}

  customDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.loadSelects()
    this.initializeHandlers()

    if (this.uuid) {
      this.loadPageData(this.uuid)
      this.disableInputs = true
    }
  }

  loadPageData(uuid: string) {
    this.loading = true
    this.service
      .getOne(uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.loadForm(res)
      })
  }

  loadForm(data: RevenueProjectionInterface) {
    const dto = {
      ...data,
      planoReceita: {
        codigo: data.planoReceitaCodigo ? data.planoReceitaCodigo : null,
        nome: data.planoReceitaNome ? data.planoReceitaNome : null,
      },
      projecaoReceitaGrupoCalculo: data.projecaoReceitaGrupoCalculo.uuid
        ? data.projecaoReceitaGrupoCalculo.uuid
        : null,
    }
    this.model.patchValue(dto, { emitEvent: false })
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      planoReceita: this.builder.group({
        uuid: ['', Validators.required],
        codigo: [],
        nome: [],
      }),
      projecaoReceitaGrupoCalculo: [undefined, Validators.required],
    })
  }

  initializeHandlers() {
    this.model
      .get('planoReceita.codigo')
      .valueChanges.pipe(
        takeUntil(this.unsub$),
        debounceTime(2000),
        distinctUntilChanged(),
      )
      .subscribe(res => {
        this.onRevenuePlanSearchInput(res)
      })
  }

  onRevenuePlanSearchInput(code) {
    if (!code || code == '') {
      this.model.get('planoReceita').reset()
      return
    }

    this.loading = true
    this.revenuePlanData = new DataSource({
      store: this.crudService.getDataSourceFiltroComposto(
        'uuid',
        `projecao_receita/plano_receita`,
        10,
        `["codigo","startswith",${code}]`
      ),
      paginate: true,
      pageSize: 10,
    })
    this.revenuePlanData.load().then(res => {
      if (res.length == 0) {
        this.toastr.send({
          error: true,
          message: 'Conta de receita não encontrada.',
        })
        this.model.get('planoReceita').reset()
        this.loading = false
      } else {
        this.setRevenuePlanFields(res[0])
        this.loading = false
      }
    })
  }

  onRevenuePlanSearchDialog() {
    const ref = this.dialogService.open(ProjectionRevenuePlanSearchComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose.pipe(first()).subscribe(res => {
      if (res) {
        this.setRevenuePlanFields(res[0])
      }
    })
  }

  loadSelects() {
    this.calculationGroupData = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'projecao_receita/grupo_calculo',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  setRevenuePlanFields(data: RevenuePlanInterface) {
    this.model.get('planoReceita').setValue(
      {
        uuid: data.uuid,
        codigo: data.codigo,
        nome: data.nome,
      },
      { emitEvent: false },
    )
  }

  prepare(formData: any) {
    const dto = {
      planoReceita: formData.planoReceita.uuid
        ? { uuid: formData.planoReceita.uuid }
        : null,
      projecaoReceitaGrupoCalculo: formData.projecaoReceitaGrupoCalculo
        ? { uuid: formData.projecaoReceitaGrupoCalculo }
        : null,
    }

    return dto
  }

  cancel() {
    this.dialogRef.close(null)
  }

  confirm() {
    this.loading = true
    this.service
      .post(this.prepare(this.model.getRawValue()))
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          this.toastr.send({
            title: 'Sucesso',
            success: true,
            message: this.pageTitle + ' cadastrado(a) com sucesso!',
          })
          this.dialogRef.close(res.body.dados)
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }
}
