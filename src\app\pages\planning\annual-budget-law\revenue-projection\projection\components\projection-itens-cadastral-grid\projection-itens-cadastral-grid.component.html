<eqp-standard-page
  [mainTitle]="pageTitle"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-undo'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
>
  <div class="container" [formGroup]="model">
    <div class="col col-12">
      <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        label="Conta de receita"
        placeholder=""
        formControlName="planoReceita"
        readonly="true"
      >
      </eqp-nebular-input>
    </div>
    <div class="row mt-5">
      <div class="col col-12">
        <dx-data-grid
          id="projectionItensGrid"
          [dataSource]="data"
          [allowColumnResizing]="true"
          [columnAutoWidth]="true"
          [nbSpinner]="loading"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [rowAlternationEnabled]="true"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          [columnHidingEnabled]="false"
          [remoteOperations]="true"
          keyExpr="uuid"
          (onRowUpdating)="onRowUpdating($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

          <dxo-editing
            mode="cell"
            [allowUpdating]="true"
            [allowDeleting]="false"
            [allowAdding]="false"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            *ngFor="let column of columnsTemplate"
            [dataField]="column?.dataField"
            [caption]="column?.caption"
            [hidingPriority]="column?.hidingPriority"
            [dataType]="column?.dataType"
            [sortOrder]="column?.sortOrder"
            [visible]="column?.visible"
            [alignment]="left"
            [allowEditing]="column?.allowEditing"
            [allowExporting]="column?.allowExporting"
            [allowFiltering]="column?.allowFiltering"
            [allowHiding]="column?.allowHiding"
            [allowResizing]="column?.allowResizing"
            [allowSorting]="column?.allowSorting"
            [allowSearch]="column?.allowSearch"
            [allowGrouping]="column?.allowGrouping"
            [allowHeaderFiltering]="column?.allowHeaderFiltering"
            [allowReordering]="column?.allowReordering"
            [width]="column?.width"
            [type]="column?.type"
            [editorOptions]="{ placeholder: '00' }"
            [format]="currencyFormat"
          ></dxi-column>
        </dx-data-grid>
      </div>
    </div>
    <div class="d-flex mt-4 justify-content-end" style="gap: 0.5rem">
      <eqp-nebular-button
        [buttonShape]="'rectangle'"
        [buttonText]="'Salvar'"
        (buttonEmitter)="confirm()"
        [buttonType]="'success'"
        [buttonIcon]="'fas fa-save'"
        [buttonIconVisible]="true"
        [buttonVisible]="true"
        [buttonDisabled]="!data"
      ></eqp-nebular-button>
    </div>
  </div>
</eqp-standard-page>
