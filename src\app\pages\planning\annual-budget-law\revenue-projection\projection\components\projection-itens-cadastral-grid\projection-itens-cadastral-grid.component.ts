import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { finalize, take } from 'rxjs/operators'
import { RevenueProjectionInterface } from '../../interfaces/revenue-projection'
import { ProjectionService } from '../../services/projection.service'
import * as _ from 'lodash'
import { ProjectionItensService } from '../../services/projection-itens.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { CrudService } from '@common/services/crud.service'

@Component({
  selector: 'eqp-projection-itens-cadastral-grid',
  templateUrl: './projection-itens-cadastral-grid.component.html',
  styleUrls: ['./projection-itens-cadastral-grid.component.scss'],
})
export class ProjectionItensCadastralGridComponent implements OnInit {
  loading = false

  pageTitle = 'Projeção da Receita - LOA'

  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  public columnsTemplate: DxColumnInterface[] = []
  data: any
  itensData: any[] = []
  dataField: any[] = []
  valuesData: any[] = []

  loggedExercise: number
  parentUuid: string
  model: FormGroup

  constructor(
    private builder: FormBuilder,
    private route: ActivatedRoute,
    private projectionService: ProjectionService,
    private service: ProjectionItensService,
    private toastr: ToastrService,
    private router: Router,
    private userService: UserDataService,
    private crudService: CrudService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.columnsTemplate = this.getColumnsTemplate()
    this.projectionService.getExerciseInfo().subscribe(exerciseInfo => {
      this.loggedExercise = exerciseInfo.dados.exercicio
    })
    this.getDataFieldAndCaption(2, 3, true);

    const { uuid } = this.route.snapshot.params
    this.parentUuid = uuid

    if (this.parentUuid) {
      this.loadProjectionData(this.parentUuid)
      this.crudService
        .getDataSourceFiltro('uuid', `projecao_receita/${this.parentUuid}/item`, 0)
        .load()
        .then(res => {
          this.getGridData(res)
        })
    }
  }

  getDataFieldAndCaption(
    prevExercise?: number,
    postExercise?: number,
    flagLoggedExercise?: boolean,
  ) {
    let loggedExercise = this.userService.userData.exercicio

    if (flagLoggedExercise) {
      var totalExercises = prevExercise + postExercise + 1
    } else {
      var totalExercises = prevExercise + postExercise
    }

    let previousExercise = loggedExercise - prevExercise
    let posteriorExercise = loggedExercise + postExercise
    let caption = []
    let dataField = []
    for (let year = previousExercise, x = 0; year <= posteriorExercise; year++) {
      caption.push(year)
    }
    for (let x = 0; x < totalExercises; x++) {
      dataField.push(`coluna${x}.valor`)
    }
    this.dataField = dataField
    if (!flagLoggedExercise) {
      caption.splice(caption.indexOf(loggedExercise), 1)
    }

    for (let i = 0; i < totalExercises; i++) {
      this.columnsTemplate.push({
        dataField: dataField[i],
        caption: caption[i],
      })
    }
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        dataField: 'tipoOperacao',
        caption: 'Tipo de operação',
        allowEditing: false,
      },
    ]
    return template
  }

  getGridData(data: any[]) {
    let auxData = data

    const agrupadoPorTipoOperacao = auxData.reduce((acumulador, operacao) => {
      const { tipoOperacaoReceita } = operacao
      const indice = Object.keys(
        acumulador[tipoOperacaoReceita.nome] || {},
      ).length
      acumulador[tipoOperacaoReceita.nome] = {
        ...acumulador[tipoOperacaoReceita.nome],
        [`coluna${indice}`]: {
          ...operacao,
        },
      }

      return acumulador
    }, {})

    let operacoesPorTipo: any[] = Object.values(agrupadoPorTipoOperacao)

    operacoesPorTipo = operacoesPorTipo.map(operacoes => {
      return Object.entries(operacoes)
        .reduce((acc, [key, operacao]) => {
          if (key.startsWith('coluna')) {
            acc.push(operacao)
          }
          return acc
        }, [] as any[])
        .sort((a, b) => a.exercicioProjecao - b.exercicioProjecao)
    })

    for (const operacoes of operacoesPorTipo) {
      for (let i = 0; i < operacoes.length; i++) {
        const tipoOperacao = operacoes[i].tipoOperacaoReceita.nome
        operacoes[i].tipoOperacao = tipoOperacao
      }
    }

    const agrupadoPorTipoOperacaoOrdenado = operacoesPorTipo.reduce(
      (acumulador, operacoes, i) => {
        const tipoOperacao = Object.keys(acumulador)[i]
        operacoes.forEach((operacao, j) => {
          acumulador[tipoOperacao][`coluna${j}`] = operacao
        })
        return acumulador
      },
      agrupadoPorTipoOperacao,
    )

    this.data = Object.values(agrupadoPorTipoOperacaoOrdenado).reduce(
      (acc: any[], val) => acc.concat(val),
      [],
    )

    this.data.forEach((operacao, i) => {
      operacao['uuid'] = `uuid-${Math.floor(Math.random() * 1000)}`
    })

    this.data.forEach(item => {
      let tipoOperacaoEncontrado = false
      for (let i = 0; i < Object.keys(item).length; i++) {
        const key = Object.keys(item)[i]
        if (key.startsWith('coluna')) {
          const tipoOperacao = item[key].tipoOperacao
          if (tipoOperacao) {
            item.tipoOperacao = tipoOperacao
            tipoOperacaoEncontrado = true
            break
          }
        }
      }
      if (!tipoOperacaoEncontrado) {
        item.tipoOperacao = 'tipoOperacaoPadrao'
      }
    })

    this.data.sort((a, b) => {
      const codigoA = a.coluna0.tipoOperacaoReceita.codigo
      const codigoB = b.coluna0.tipoOperacaoReceita.codigo
      return codigoA - codigoB
    })
  }

  loadProjectionData(uuid: string) {
    this.loading = true
    this.projectionService
      .getOne(uuid)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.getProjectionData(res)
      })
  }

  getProjectionData(data: RevenueProjectionInterface) {
    this.model
      .get('planoReceita')
      .patchValue(`${data.planoReceitaCodigo} - ${data.planoReceitaNome}`)
  }

  getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      planoReceita: [],
    })
  }

  onRowUpdating(event: any) {
    let valorColuna0: number = event.oldData.coluna0.valor
    let valorColuna1: number = event.oldData.coluna1.valor
    let valorColuna2: number = event.oldData.coluna2.valor
    let valorColuna3: number = event.oldData.coluna3.valor
    let valorColuna4: number = event.oldData.coluna4.valor
    let valorColuna5: number = event.oldData.coluna5.valor

    if (event.newData?.coluna0?.valor) {
      valorColuna0 = _.round(Number(event.newData.coluna0.valor), 2)
    } else if (event.newData?.coluna1?.valor) {
      valorColuna1 = _.round(Number(event.newData.coluna1.valor), 2)
    } else if (event.newData?.coluna2?.valor) {
      valorColuna2 = _.round(Number(event.newData.coluna2.valor), 2)
    } else if (event.newData?.coluna3?.valor) {
      valorColuna3 = _.round(Number(event.newData.coluna3.valor), 2)
    } else if (event.newData?.coluna4?.valor) {
      valorColuna4 = _.round(Number(event.newData.coluna4.valor), 2)
    } else if (event.newData?.coluna5?.valor) {
      valorColuna5 = _.round(Number(event.newData.coluna5.valor), 2)
    }
    event.newData = {
      ...event.newData,
      uuid: event.oldData.uuid,
      tipoOperacao: event.oldData.tipoOperacao,
      coluna0: {
        valor: valorColuna0,
      },
      coluna1: {
        valor: valorColuna1,
      },
      coluna2: {
        valor: valorColuna2,
      },
      coluna3: {
        valor: valorColuna3,
      },
      coluna4: {
        valor: valorColuna4,
      },
      coluna5: {
        valor: valorColuna5,
      },
    }
  }

  cancel() {
    this.router.navigate([
      'lei-orcamentaria-anual/receita/projecao-receita/projecao',
    ])
  }

  filterObjects(arr) {
    return arr.reduce((acc, item) => {
      Object.values(item).forEach(value => {
        if (typeof value === 'object' && value !== null) {
          acc.push(value)
        }
      })
      return acc
    }, [])
  }

  confirm() {
    const allValues = this.filterObjects(this.data)
    this.loading = true
    this.service
      .putBatch(this.parentUuid, allValues)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: 'Itens da projeção da receita atualizados com sucesso.',
        })
      })
  }
}
