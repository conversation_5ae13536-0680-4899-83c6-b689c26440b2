import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { filter, finalize, first, take } from 'rxjs/operators'
import { ProjectionService } from '../../services/projection.service'
import { ProjectionFormDialogComponent } from '../projection-form-dialog/projection-form-dialog.component'
import { RevenueProjectionInterface } from '../../interfaces/revenue-projection'
import { RevenueProjectionItemInterface } from '../../interfaces/revenue-projection-item'

@Component({
  selector: 'eqp-projection-list',
  templateUrl: './projection-list.component.html',
  styleUrls: ['./projection-list.component.scss'],
})
export class ProjectionListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  public columnsTemplate: DxColumnInterface[] = []

  public loading: boolean = false
  public pageTitle = 'Projeção da Receita - LOA'

  loggedExercise: number = 0

  dataSource: DataSource

  constructor(
    public menuService: MenuService,
    public router: Router,
    public toastr: ToastrService,
    private crudService: CrudService,
    private service: ProjectionService,
    private dialogService: NbDialogService,
    private userService: UserDataService,
  ) {
    super(menuService, router)
    this.permissao('projecao-receita-loa')
  }

  ngOnInit(): void {
    this.columnsTemplate = this.getColumnsTemplate()
    this.fetchGrid()
    this.loading = true
    this.service
      .getExerciseInfo()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(exerciseInfo => {
        this.loggedExercise = exerciseInfo.dados.exercicio
      })
    this.getDataFieldAndCaption(2, 3, true)
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        dataField: 'planoReceitaCodigo',
        caption: 'Código',
      },
      {
        dataField: 'planoReceitaNome',
        caption: 'Nome',
      },
    ]
    return template
  }


  getDataFieldAndCaption(
    prevExercise?: number,
    postExercise?: number,
    flagLoggedExercise?: boolean,
  ) {
    let loggedExercise = this.userService.userData.exercicio

    let totalExercises = 0
    if (flagLoggedExercise) {
      totalExercises = prevExercise + postExercise + 1
    } else {
      totalExercises = prevExercise + postExercise
    }

    let previousExercise = loggedExercise - prevExercise
    let posteriorExercise = loggedExercise + postExercise
    let caption = []

    for (let i = previousExercise; i <= posteriorExercise; i++) {
      caption.push(i)
    }

    if (!flagLoggedExercise) {
      caption.splice(caption.indexOf(loggedExercise), 1)
    }

    for (let i = 0; i < totalExercises; i++) {
      this.columnsTemplate.push({
        dataField: `coluna${i}`,
        caption: caption[i],
        alignment: 'left',
        allowResizing: true,
        format: this.currencyFormat
      })
    }
  }

  customDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`
  }

  calculateValues(data: RevenueProjectionItemInterface[] = []) {
    const agrupadoPorTipoOperacao = data.reduce((acc, curr) => {
      const { tipoOperacaoReceita } = curr
      const indice = Object.keys(acc[tipoOperacaoReceita.nome] || {}).length
      acc[tipoOperacaoReceita.nome] = {
        ...acc[tipoOperacaoReceita.nome],
        [`coluna${indice}`]: {
          ...curr,
        },
      }
      return acc
    }, {})

    let operacoesPorTipo: any[] = Object.values(agrupadoPorTipoOperacao)

    operacoesPorTipo = operacoesPorTipo.map(operacoes => {
      return Object.entries(operacoes)
        .reduce((acc, [key, operacao]) => {
          if (key.startsWith('coluna')) {
            acc.push(operacao)
          }
          return acc
        }, [] as any[])
        .sort((a, b) => a.exercicioProjecao - b.exercicioProjecao)
    })

    const agrupadoPorTipoOperacaoOrdenado = operacoesPorTipo.reduce((acc, operacoes, i) => {
      const tipoOperacao = Object.keys(acc)[i]
      operacoes.forEach((operacao, j) => {
        acc[tipoOperacao][`coluna${j}`] = operacao
      })
      return acc
    }, agrupadoPorTipoOperacao)

    const arrayReduzido: any = Object.values(agrupadoPorTipoOperacaoOrdenado).reduce(
      (acc: any[], val) => acc.concat(val),
      [],
    )

    let listaDeValores = []
    arrayReduzido.forEach((item: RevenueProjectionItemInterface) => {
      for (const [key, value] of Object.entries(item)) {
        if (key.startsWith('coluna')) {
          listaDeValores.push({
            [key]: value.valor,
            tipoOperacaoReceita: value.tipoOperacaoReceita.codigo,
          })
        }
      }
    })

    const saldoPorOperacao = listaDeValores.reduce((acc, curr) => {
      Object.keys(curr).forEach(key => {
        if (key.startsWith('coluna')) {
          if (curr['tipoOperacaoReceita'] !== 1) {
            acc[key] = acc[key] - curr[key];
          } else {
            acc[key] = curr[key];
          }
        }
      });

      return acc;
    }, {});

    return saldoPorOperacao
  }


  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'projecao_receita',
        10,
      ),
      map: (data: RevenueProjectionInterface) => {
        const saldoColunasReceita = this.calculateValues(data.itens)
        return {
          ...data,
          ...saldoColunasReceita
        }
      },
      paginate: true,
      pageSize: 10,
    })
  }

  public new(): void {
    const ref = this.dialogService.open(ProjectionFormDialogComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose
      .pipe(
        filter(res => res),
        first(),
      )
      .subscribe(res => {
        if (res) {
          this.fetchGrid()
        }
      })
  }

  public edit(uuid?: string) {
    const dialogRef = this.dialogService.open(ProjectionFormDialogComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.componentRef.instance.uuid = uuid
    dialogRef.onClose.pipe(filter(res => res)).subscribe(_ => {
      this.fetchGrid()
    })
  }

  public openRelationData(value) {
    this.router.navigate([
      `lei-orcamentaria-anual/receita/projecao-receita/projecao/edit/${value}/itens`,
    ])
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Projeção da receita'
          item.options.hint = 'Cadastrar projeção da receita'
          item.options.onClick = () => this.new()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(res => {
      this.loading = true
      if (res === 'S') {
        this.service
          .delete(uuid)
          .pipe(
            take(1),
            finalize(() => (this.loading = false)),
          )
          .subscribe(
            _ => {
              this.toastr.send({
                title: 'Sucesso',
                success: true,
                message: 'Projeção da receita removido(a) com sucesso',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
