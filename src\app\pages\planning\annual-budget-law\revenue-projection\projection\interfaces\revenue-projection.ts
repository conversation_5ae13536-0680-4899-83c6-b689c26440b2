import { RevenueProjectionCalculationGroupInterface } from "../../group/interfaces/revenue-projection-calculation-group"
import { RevenueProjectionItemInterface } from "./revenue-projection-item"

export interface RevenueProjectionInterface {
  uuid: string
  planoReceitaCodigo: string
  planoReceitaNome: string
  projecaoReceitaGrupoCalculo: RevenueProjectionCalculationGroupInterface
  planoReceitaUuid?: string
  itens: RevenueProjectionItemInterface[]

	//readonly
	sldExercicioLogadoMenosDois: number
	sldExercicioLogadoMenosUm: number
	sldExercicioLogado: number
	sldExercicioLogadoMaisUm: number
	sldExercicioLogadoMaisDois: number
	sldExercicioLogadoMaisTres: number
}
