import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProjectionItensCadastralGridComponent } from './components/projection-itens-cadastral-grid/projection-itens-cadastral-grid.component';
import { ProjectionListComponent } from './components/projection-list/projection-list.component';
import { ProjectionComponent } from './projection.component';

const routes: Routes = [
  {
    path: '',
    component: ProjectionComponent,
    children: [
      {
				path: '',
				component: ProjectionListComponent,
			},
      {
				path: 'edit/:uuid/itens',
				component: ProjectionItensCadastralGridComponent,
			},
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ProjectionRoutingModule { }
