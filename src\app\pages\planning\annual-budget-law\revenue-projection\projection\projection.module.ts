import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ProjectionRoutingModule } from './projection-routing.module';
import { ProjectionComponent } from './projection.component';
import { CommonToolsModule } from '@common/common-tools.module';
import { ProjectionListComponent } from './components/projection-list/projection-list.component';
import { ProjectionFormDialogComponent } from './components/projection-form-dialog/projection-form-dialog.component';
import { ProjectionRevenuePlanSearchComponent } from './components/dialogs/projection-revenue-plan-search/projection-revenue-plan-search.component';
import { ProjectionItensCadastralGridComponent } from './components/projection-itens-cadastral-grid/projection-itens-cadastral-grid.component';
import { RevenuePlanMaskDirective } from '@pages/planning/shared/helpers/directives/revenue-plan-mask.directive';


@NgModule({
  declarations: [ProjectionComponent, ProjectionListComponent, ProjectionFormDialogComponent, ProjectionRevenuePlanSearchComponent, ProjectionItensCadastralGridComponent, RevenuePlanMaskDirective],
  imports: [
    CommonModule,
    CommonToolsModule,
    ProjectionRoutingModule,
  ]
})
export class ProjectionModule { }
