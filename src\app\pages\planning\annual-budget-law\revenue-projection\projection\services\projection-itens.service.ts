import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core'
import { BaseChildService } from '@common/services/base/base-child.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { RevenueProjectionItemInterface } from '../interfaces/revenue-projection-item';

@Injectable({
  providedIn: 'root',
})
export class ProjectionItensService  extends BaseChildService<RevenueProjectionItemInterface> {
  constructor(protected http: HttpClient) {
    super(http, 'projecao_receita', 'item')
  }

  public putBatch(
    revenuePlanUuid: string,
    data: any[],
  ) {
    return this.http.put<ResponseDto<any[]>>(
      `projecao_receita/${revenuePlanUuid}/item/lote`,
      data,
      {
        observe: 'response',
      },
    )
  }
}
