import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { IPagedList } from '@design-tools/wrapped-dx-grid/models';
import { UserDataService } from '@guards/services/user-data.service';
import { Observable } from 'rxjs';
import { RevenueProjectionInterface } from '../interfaces/revenue-projection';

@Injectable({
  providedIn: 'root'
})
export class ProjectionService  extends BaseService<
IPagedList<RevenueProjectionInterface>,
RevenueProjectionInterface
> {
  constructor(protected http: HttpClient, private userService: UserDataService) {
    super(http, 'projecao_receita')
   }

  public getExerciseInfo(): Observable<any> {
    const userData = this.userService.userData
    return this.http.get<any>(`exercicio/${userData.exercicioUuid}`, {})
  }
}
