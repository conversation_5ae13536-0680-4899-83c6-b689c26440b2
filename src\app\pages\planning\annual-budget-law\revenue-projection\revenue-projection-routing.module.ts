import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RevenueProjectionComponent } from './revenue-projection.component';

const routes: Routes = [
  {
    path: '',
    component: RevenueProjectionComponent,
    children: [
      {
        path: 'grupo-calculo',
        loadChildren: () =>
          import('./group/group.module').then(
            (m) => m.GroupModule,
          ),
      },
      {
        path: 'projecao',
        loadChildren: () =>
          import('./projection/projection.module').then(
            (m) => m.ProjectionModule,
          ),
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RevenueProjectionRoutingModule { }
