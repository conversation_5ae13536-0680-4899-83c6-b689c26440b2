import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { ActuarialProjectionInterface } from '../../interfaces/actuarial-projection.model'
import { ActuarialProjectionService } from '../../services/actuarial-projection.service'
import { ActurialProjectionFormComponent } from '../acturial-projection-form/acturial-projection-form.component'

@Component({
  selector: 'eqp-actuarial-projection-list',
  templateUrl: './actuarial-projection-list.component.html',
  styleUrls: ['./actuarial-projection-list.component.scss'],
})
export class ActuarialProjectionListComponent
  extends BaseTelasComponent
  implements OnInit
{
  @Input() acturialProjection: ActuarialProjectionInterface
  public pageTitle = 'Projeção atuarial'
  public loading: boolean
  public model: FormGroup
  public tipoPrevidenciaData: DataSource<ActuarialProjectionInterface, string>
  public exercicioReferenciaUuid: string
  public dataSource: any
  public pensionType: any
  public tipoPrevidenciaUuid: any
  private subscription: Subscription
  public publicacao: any

  primaryItem = false

  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  constructor(
    public menuService: MenuService,
    public router: Router,
    private formBuilder: FormBuilder,
    private service: ActuarialProjectionService,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('/lei-diretrizes-orcamentarias/projecao-atuarial')
  }

  ngOnInit(): void {
    this.model = this.getNovoFormulario()
    this.loadSelect()
    this.changePensionType()
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
  }

  public getNovoFormulario() {
    return this.formBuilder.group({
      uuid: [''],
      tipoPrevidenciaUuid: [''],
      vlrDespesaPrevidenciaria: [''],
      vlrReceitaPrevidenciaria: [''],
      publicacao: [],
    })
  }

  public fetchGrid() {
    this.loading = true
    this.service
      .getList(this.tipoPrevidenciaUuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe((res: any) => {
        if (res.data.length) {
          this.dataSource = res.data.map(item => {
            return {
              ...item,
              calculate:
                item.vlrReceitaPrevidenciaria - item.vlrDespesaPrevidenciaria,
            }
          })
          const {data} = res
          this.model.get('publicacao').patchValue(data[0]?.publicacao)
        } else {
          this.criar()
        }
      })
  }

  reload() {
    this.dataSource = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        'projecao_atuarial_rpps/paginado?sort=exercicioReferencia",false',
        10,
        'tipoPrevidencia',
        this.tipoPrevidenciaUuid,
      ),
      map: item => {
        return {
          ...item,
          calculate:
            item.vlrReceitaPrevidenciaria - item.vlrDespesaPrevidenciaria,
        }
      },
    }
  }

  loadSelect() {
    this.pensionType = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        'planejamento/tipo_previdencia/paginado',
        10,
      ),
    }
  }

  changePensionType() {
    this.model.get('tipoPrevidenciaUuid').valueChanges.subscribe(item => {
      this.tipoPrevidenciaUuid = item
      this.fetchGrid()
    })
  }

  private criar() {
    this.loading = true
    this.service
      .postBatch({ tipoPrevidenciaUuid: this.tipoPrevidenciaUuid } as any)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Projeção atuarial criado com sucesso.',
        })
      })
    this.reload()
  }

  public edit(uuid: string): void {
    if (this.dataSource[0].uuid === uuid) {
      this.primaryItem = true
    } else {
      this.primaryItem = false
    }
    const dialogRef = this.dialogService.open(ActurialProjectionFormComponent, {
      context: {
        acturialProjection: this.tipoPrevidenciaUuid,
        uuid,
        item: this.dataSource,
        primaryItem: this.primaryItem,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.fetchGrid()
      }
    })
  }

  public save({ value, valid }: { value: any; valid: boolean }): void {
    this.loading = true
    if (!valid) {
      return
    }

    value.tipoPrevidenciaUuid = this.tipoPrevidenciaUuid
    value.uuid = this.dataSource[1].uuid

    this.subscription = this.service
      .put(value)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Projeção atuarial atualizado com sucesso.',
        })
      })
  }
}
