<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="form">
    <div class="row" *ngIf="!primaryItem">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: '', thousands: '.', decimal: ',' }"
          [size]="'small'"
          name="Receitas previdenciarias"
          label="Receitas previdenciarias"
          placeholder="Receitas previdenciarias"
          formControlName="vlrReceitaPrevidenciaria"
        ></eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: '', thousands: '.', decimal: ',' }"
          [size]="'small'"
          name="Despesas previdenciárias"
          label="Despesas previdenciárias"
          placeholder="Despesas previdenciárias"
          formControlName="vlrDespesaPrevidenciaria"
        ></eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [disabled]="true"
          name="Saldo financeiro do exercício"
          label="Saldo financeiro do exercício"
          placeholder=""
          formControlName="vlrSaldoFinanceiro"
        ></eqp-nebular-input>
      </div>
    </div>
    <div class="row" *ngIf="primaryItem">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [disabled]="true"
          name="Receitas previdenciarias"
          label="Receitas previdenciarias"
          placeholder="Receitas previdenciarias"
          formControlName="vlrReceitaPrevidenciaria"
        ></eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [disabled]="true"
          name="Despesas previdenciárias"
          label="Despesas previdenciárias"
          placeholder="Despesas previdenciárias"
          formControlName="vlrDespesaPrevidenciaria"
        ></eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          name="Saldo financeiro do exercício"
          label="Saldo financeiro do exercício"
          placeholder=""
          formControlName="vlrSaldoFinanceiro"
        ></eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="dismiss()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="save(form)"
          [disabled]="form.invalid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
