import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { NbDialogRef } from '@nebular/theme'
import { Subscription } from 'rxjs'
import { finalize } from 'rxjs/operators'
import { ActuarialProjectionInterface } from '../../interfaces/actuarial-projection.model'
import { ActuarialProjectionService } from '../../services/actuarial-projection.service'

@Component({
  selector: 'eqp-acturial-projection-form',
  templateUrl: './acturial-projection-form.component.html',
  styleUrls: ['./acturial-projection-form.component.scss'],
})
export class ActurialProjectionFormComponent implements OnInit {
  @Input() public uuid: string
  @Input() public acturialProjection: ActuarialProjectionInterface
  @Input() public item: any
  @Input() public primaryItem

  public loading: boolean = false
  public pageTitle: string = 'Projeção atuarial'
  public form: FormGroup

  public subscription: Subscription

  constructor(
    protected ref: NbDialogRef<ActurialProjectionFormComponent>,
    private service: ActuarialProjectionService,
    private fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.form = this.fb.group({
      uuid: [''],
      tipoPrevidenciaUuid: [''],
      vlrDespesaPrevidenciaria: [''],
      vlrReceitaPrevidenciaria: [''],
      vlrSaldoFinanceiro: [''],
    })
    this.searchById()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public searchById() {
    if (!this.uuid) return

    this.service.getOne(this.uuid).subscribe(response => {
      this.form.patchValue(response)
    })
  }

  public dismiss(): void {
    this.ref.close(null)
  }

  public save({ value, valid }: { value: any; valid: boolean }): void {
    this.loading = true
    if (!valid) {
      return
    }

    value.tipoPrevidenciaUuid = this.acturialProjection

    this.subscription = this.service
      .put(value)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(() => {
        this.ref.close('S')
      })
  }
}
