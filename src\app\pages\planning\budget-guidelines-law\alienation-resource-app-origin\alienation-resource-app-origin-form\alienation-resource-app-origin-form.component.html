<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [rightApproveButtonText]="'Salvar'"
  [rightApproveButtonVisible]="true"
  (rightApproveButtonEmitter)="update()"
  [rightApproveButtonDisabled]="
    !enableEditing || (model1.pristine && model2.pristine && model3.pristine && publicacao.pristine)
  "
  rightApproveButtonId="alienation-resource-app-origin-submit-button"
>
  <ng-container [formGroup]="model">
    <div class="container">
      <div class="mb-3">
        <eqp-publication-note-field
          [formControl]="publicacao"
        ></eqp-publication-note-field>
      </div>
      <div class="table-container">
        <table
          class="dx-datagrid-table dx-datagrid-table-fixed"
          role="presentation"
        >
          <tbody>
            <tr>
              <th class="text-uppercase">Receitas Realizadas</th>
              <th>{{ exercise_1 }} (a)</th>
              <th>{{ exercise_2 }} (b)</th>
              <th>{{ exercise_3 }} (c)</th>
            </tr>
            <tr></tr>
            <tr>
              <td>Receitas de Capital - Alienação de Ativos (I)</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  status="primary"
                  disabled
                  class="text-right"
                  [value]="calcAlienacaoAtivosI_1 | currency: 'BRL':true"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  disabled
                  class="text-right"
                  [value]="calcAlienacaoAtivosI_2 | currency: 'BRL':true"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  disabled
                  class="text-right"
                  [value]="calcAlienacaoAtivosI_3 | currency: 'BRL':true"
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">Alienação de Bens Móveis</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrRecAlienBensMoveis')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrRecAlienBensMoveis')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrRecAlienBensMoveis')"
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">Alienação de Bens Imóveis</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrRecAlienBensImoveis')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrRecAlienBensImoveis')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrRecAlienBensImoveis')"
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">Alienação de Bens Intangíveis</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrRecAlienBensIntangiveis')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrRecAlienBensIntangiveis')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrRecAlienBensIntangiveis')"
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">
                Rendimentos de Aplicações Financeiras
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrRecRendAplicFinanceira')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrRecRendAplicFinanceira')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrRecRendAplicFinanceira')"
                />
              </td>
            </tr>
            <tr>
              <th class="text-uppercase">Despesas Executadas</th>
              <th>{{ exercise_1 }} (d)</th>
              <th>{{ exercise_2 }} (e)</th>
              <th>{{ exercise_3 }} (f)</th>
            </tr>
            <tr>
              <td>Aplicação dos recursos da alienação de ativos (II)</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcAlienacaoAtivosII_1 | currency: 'BRL':true"
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcAlienacaoAtivosII_2 | currency: 'BRL':true"
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcAlienacaoAtivosII_3 | currency: 'BRL':true"
                  disabled
                />
              </td>
            </tr>
            <tr>
              <td>Despesas de capital</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcDespesasCapital_1 | currency: 'BRL':true"
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcDespesasCapital_2 | currency: 'BRL':true"
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcDespesasCapital_3 | currency: 'BRL':true"
                  disabled
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">Investimentos</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrDespInvestimentos')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrDespInvestimentos')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrDespInvestimentos')"
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">Inversões financeiras</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrDespInversoesFinan')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrDespInversoesFinan')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrDespInversoesFinan')"
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">Amortização de Dívida</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrDespAmortizacaoDivida')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrDespAmortizacaoDivida')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrDespAmortizacaoDivida')"
                />
              </td>
            </tr>
            <tr>
              <td>Despesas correntes dos regimes de previdência</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="
                    calcDespesasRecorrentesPrevidencia_1 | currency: 'BRL':true
                  "
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="
                    calcDespesasRecorrentesPrevidencia_2 | currency: 'BRL':true
                  "
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="
                    calcDespesasRecorrentesPrevidencia_3 | currency: 'BRL':true
                  "
                  disabled
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">Regime geral de previdência social</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrDespRegimeGeralPrevSocial')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrDespRegimeGeralPrevSocial')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrDespRegimeGeralPrevSocial')"
                />
              </td>
            </tr>
            <tr>
              <td class="pl-2 pl-md-4">
                Regime próprio de previdência dos servidores
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model1.get('vlrDespRegProprioPrev')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model2.get('vlrDespRegProprioPrev')"
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  currencyMask
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                  placeholder="00,00"
                  [formControl]="model3.get('vlrDespRegProprioPrev')"
                />
              </td>
            </tr>
            <tr>
              <th class="text-uppercase">Saldo financeiro</th>
              <th>{{ exercise_1 }} (g) = (Ia – IId) + IIIh</th>
              <th>{{ exercise_2 }} (h) = (Ib – IIe) + IIIi</th>
              <th>{{ exercise_3 }} (i) = Ic – IIf</th>
            </tr>
            <tr>
              <td>Valor (III)</td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcValor_1 | currency: 'BRL':true"
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcValor_2 | currency: 'BRL':true"
                  disabled
                />
              </td>
              <td>
                <input
                  type="text"
                  nbInput
                  fullWidth
                  fieldSize="tiny"
                  placeholder="00,00"
                  class="text-right"
                  [value]="calcValor_3 | currency: 'BRL':true"
                  disabled
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-container>
</eqp-standard-page>
