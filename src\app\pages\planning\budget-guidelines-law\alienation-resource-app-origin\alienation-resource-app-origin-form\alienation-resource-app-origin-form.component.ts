import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { FormBuilder, FormControl, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { finalize, first, take } from 'rxjs/operators'
import { AlienationResourceApplicationOriginInterface } from '../../interfaces/alienation-resource-app-origin'
import { AlienationResourceAppOriginService } from '../../services/alienation-resource-app-origin.service'
@Component({
  selector: 'eqp-alienation-resource-app-origin-form',
  templateUrl: './alienation-resource-app-origin-form.component.html',
  styleUrls: ['./alienation-resource-app-origin-form.component.scss'],
})
export class AlienationResourceAppOriginFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string =
    'Origem e aplicação de recursos obtidos com alienação de bens'

  enableEditing = false

  model1: FormGroup
  model2: FormGroup
  model3: FormGroup

  changeModel1Sub: Subscription
  changeModel2Sub: Subscription
  changeModel3Sub: Subscription
  changeTogglePublicationSub: Subscription

  calcAlienacaoAtivosI_1: number = 0
  calcAlienacaoAtivosII_1: number = 0
  calcDespesasCapital_1: number = 0
  calcDespesasRecorrentesPrevidencia_1: number = 0
  calcValor_1: number = 0

  calcAlienacaoAtivosI_2: number = 0
  calcAlienacaoAtivosII_2: number = 0
  calcDespesasCapital_2: number = 0
  calcDespesasRecorrentesPrevidencia_2: number = 0
  calcValor_2: number = 0

  calcAlienacaoAtivosI_3: number = 0
  calcAlienacaoAtivosII_3: number = 0
  calcDespesasCapital_3: number = 0
  calcDespesasRecorrentesPrevidencia_3: number = 0
  calcValor_3: number = 0

  loggedExercise: number

  publicacao = new FormControl()

  constructor(
    private service: AlienationResourceAppOriginService,
    private toastr: ToastrService,
    public router: Router,
    public route: ActivatedRoute,
    public menuService: MenuService,
    private builder: FormBuilder,
    private userService: UserDataService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/origem-aplicacao-recurso-alienacao-bem',
    )
  }

  get exercise_1() {
    return this.model1.get('exercicioReferencia').value || 0
  }

  get exercise_2() {
    return this.model2.get('exercicioReferencia').value || 0
  }

  get exercise_3() {
    return this.model3.get('exercicioReferencia').value || 0
  }

  ngOnInit(): void {
    this.loggedExercise = Number(this.userService.userData?.exercicio)
    this.model1 = this.getNewModel()
    this.model2 = this.getNewModel()
    this.model3 = this.getNewModel()
    this.loadCalcHandlers()
    this.loadPageData()
  }

  ngOnDestroy(): void {
    if (this.changeModel1Sub) this.changeModel1Sub.unsubscribe()
    if (this.changeModel2Sub) this.changeModel2Sub.unsubscribe()
    if (this.changeModel3Sub) this.changeModel3Sub.unsubscribe()
    if (this.changeTogglePublicationSub)
      this.changeTogglePublicationSub.unsubscribe()
  }

  private loadCalcHandlers() {
    this.changeModel1Sub = this.model1.valueChanges.subscribe(val => {
      this.calcAlienacaoAtivosI_1 = this.calcAlienacaoAtivos(val)
      this.calcDespesasCapital_1 = this.calcDespesasCapital(val)
      this.calcDespesasRecorrentesPrevidencia_1 =
        this.calcDespesasRecorrentesPrevidencia(val)
      this.calcAlienacaoAtivosII_1 =
        this.calcDespesasCapital_1 + this.calcDespesasRecorrentesPrevidencia_1
      this.calcValores()
    })

    this.changeModel2Sub = this.model2.valueChanges.subscribe(val => {
      this.calcAlienacaoAtivosI_2 = this.calcAlienacaoAtivos(val)
      this.calcDespesasCapital_2 = this.calcDespesasCapital(val)
      this.calcDespesasRecorrentesPrevidencia_2 =
        this.calcDespesasRecorrentesPrevidencia(val)
      this.calcAlienacaoAtivosII_2 =
        this.calcDespesasCapital_2 + this.calcDespesasRecorrentesPrevidencia_2
      this.calcValores()
    })

    this.changeModel3Sub = this.model3.valueChanges.subscribe(val => {
      this.calcAlienacaoAtivosI_3 = this.calcAlienacaoAtivos(val)
      this.calcDespesasCapital_3 = this.calcDespesasCapital(val)
      this.calcDespesasRecorrentesPrevidencia_3 =
        this.calcDespesasRecorrentesPrevidencia(val)
      this.calcAlienacaoAtivosII_3 =
        this.calcDespesasCapital_3 + this.calcDespesasRecorrentesPrevidencia_3
      this.calcValores()
    })

    // this.changeTogglePublicationSub =
    //   this.togglePublication.valueChanges.subscribe(val => {
    //     this.model1.get('flagPublicar').patchValue(val)
    //     this.model2.get('flagPublicar').patchValue(val)
    //     this.model3.get('flagPublicar').patchValue(val)
    //   })
  }

  get uuid() {
    return this.model.get('uuid').value
  }

  loadPageData() {
    this.loading = true
    this.service
      .get()
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          if (res.data.length > 0) {
            this.loadForm(res.data)
          } else {
            this.service
              .postBatch([])
              .pipe(take(1))
              .subscribe(
                res => {
                  this.loadForm(res.body.data)
                },
                (resp: any) => this.toastr.bulkSend(resp.mensagens),
              )
          }
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [],
      exercicioReferencia: [],
      vlrRecAlienBensMoveis: [0],
      vlrRecAlienBensImoveis: [0],
      vlrRecAlienBensIntangiveis: [0],
      vlrRecRendAplicFinanceira: [0],
      vlrDespInvestimentos: [0],
      vlrDespInversoesFinan: [0],
      vlrDespAmortizacaoDivida: [0],
      vlrDespRegimeGeralPrevSocial: [0],
      vlrDespRegProprioPrev: [0],
      publicacao: [],
    })
  }

  calcValores() {
    this.calcValor_3 =
      this.calcAlienacaoAtivosI_3 - this.calcAlienacaoAtivosII_3

    this.calcValor_2 =
      this.calcAlienacaoAtivosI_2 -
      this.calcAlienacaoAtivosII_2 +
      this.calcValor_3

    this.calcValor_1 =
      this.calcAlienacaoAtivosI_1 -
      this.calcAlienacaoAtivosII_1 +
      this.calcValor_2
  }

  calcAlienacaoAtivos(formData) {
    const {
      vlrRecAlienBensMoveis,
      vlrRecAlienBensImoveis,
      vlrRecAlienBensIntangiveis,
      vlrRecRendAplicFinanceira,
    } = formData
    const calc =
      Number(vlrRecAlienBensMoveis) +
      Number(vlrRecAlienBensImoveis) +
      Number(vlrRecAlienBensIntangiveis) +
      Number(vlrRecRendAplicFinanceira)
    return calc
  }

  private calcDespesasCapital(formData) {
    const {
      vlrDespInvestimentos,
      vlrDespInversoesFinan,
      vlrDespAmortizacaoDivida,
    } = formData
    const calc =
      Number(vlrDespInvestimentos) +
      Number(vlrDespInversoesFinan) +
      Number(vlrDespAmortizacaoDivida)
    return calc
  }

  private calcDespesasRecorrentesPrevidencia(formData) {
    const { vlrDespRegimeGeralPrevSocial, vlrDespRegProprioPrev } = formData
    const calc =
      Number(vlrDespRegimeGeralPrevSocial) + Number(vlrDespRegProprioPrev)
    return calc
  }

  private loadForm(data: AlienationResourceApplicationOriginInterface[]) {
    this.publicacao.patchValue(data[0].publicacao)

    data.sort((a, b) => a.exercicioReferencia - b.exercicioReferencia).reverse()
    const [alienation1, alienation2, alienation3] = data
    this.model1.patchValue(alienation1)
    this.model2.patchValue(alienation2)
    this.model3.patchValue(alienation3)

    this.enableEditing = true
  }

  private prepare(): any {
    let data1 = {...this.model1.getRawValue(), publicacao: this.publicacao.value}
    let data2 = {...this.model2.getRawValue(), publicacao: this.publicacao.value}
    let data3 = {...this.model3.getRawValue(), publicacao: this.publicacao.value}
    return [data1, data2, data3]
  }

  update(): void {
    this.loading = true
    this.service
      .putBatch(this.prepare())
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(_ => {
        this.toastr.send({
          success: true,
          message: 'Atualizado(a) com sucesso.',
        })
      })
  }
}
