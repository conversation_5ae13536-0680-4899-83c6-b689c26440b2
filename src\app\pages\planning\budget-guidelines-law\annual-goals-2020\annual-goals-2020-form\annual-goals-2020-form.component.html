<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>

    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">

    <div class="row" formGroupName="annualGoals2020">
      <div class="col-xl-3 col-lg-3 mb-3">
        <eqp-nebular-input [size]="'small'" [showSuffixIcon]="true" [primarySuffixIcon]="true"
          [firstSuffixIcon]="'search'" formControlName="lei" name="lei" label="Lei/Ato" placeholder="Lei/Ato" disabled
          (click)="openDecreeSearch()" required></eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mt-4 ml-3 pt-2'">
        <eqp-nebular-checkbox [style]="'basic'" name="Publicar na Internet" label="Publicar na Internet"
          formControlName="flagPublicar">
        </eqp-nebular-checkbox>
      </div>
      <div>
        <button type="button" class="btn btn-success mt-4 float-md-left" (click)="abrirEditorDocumentoFonte()">
          Fonte
        </button>
        <button type="button" class="btn btn-success ml-3 mt-4 float-md-left"
          (click)="abrirEditorDocumentoNotaExplicativa()">
          Notas Explicativas
        </button>
      </div>
    </div>

  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" [disabled]="formulario.invalid || formulario.get('annualGoals2020.uuid')?.value" class="btn btn-success float-md-right"
          (click)="gravar()">Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>

<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-body [formGroup]="formulario">
    <ng-container>
      <div formGroupName="annualGoals2020" class="table-container">
        <table>
          <tbody>
            <tr style="border-left: hidden;">
              <th class="especificacao" style="border-bottom: hidden;"> <p>Epecificação</p></th>
              <th class="Ano" colspan="3">{{ ano0 || "-" }}</th>
              <th class="Ano" colspan="3">{{ ano1 || "-" }}</th>
              <th class="Ano" colspan="3">{{ ano2 || "-" }}</th>
              <th style="border-bottom: hidden;" colspan="1"></th>
            </tr>
            <tr class="header">
              <th class="especificacao"></th>
              <th class="DadosTabela" id="ano1">Corrente</th>
              <th class="DadosTabela">Constante</th>
              <th class="DadosTabela">% PIB</th>

              <th class="DadosTabela" id="ano2">Corrente</th>
              <th class="DadosTabela">Constante</th>
              <th class="DadosTabela">% PIB</th>

              <th class="DadosTabela" id="ano3">Corrente</th>
              <th class="DadosTabela">Constante</th>
              <th class="DadosTabela">% PIB</th>

              <th style="border-bottom: hidden;"></th>
            </tr>


            <tr>
              <td class="title">Receita Total</td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalCorrente" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center font-size:500px"
                  formControlName="vlrReceitaInput0" ></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalConstante" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalPercentualPib" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalCorrente" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalConstante" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalPercentualPib" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalCorrente" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalConstante" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="valorReceitaTotalPercentualPib" placeholder="-"
                  [style]="'currency' " [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr class="onRowPrepared">
              <td class="title">Receita Primarias (I)</td>
              <td class="onRowPrepared">
                <eqp-nebular-input ng [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency'"
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput0"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrPercentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrPercentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrPercentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaPrimariaInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr>
              <td class="title">Despesas Total</td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput0"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaTotalInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr class="onRowPrepared">
              <td class="title">Despesas Primarias (II)</td>
              <td class="onRowPrepared">
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } "
                  class="text-center background-color: #cccccc;" formControlName="vlrDespesaPrimariaInput0">
                </eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: ' ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr class="onRowPrepared">
              <td class="title">Resultado Nominal - (VI) = (III + (IV - v))</td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput0"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrResultadoNominalInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr>
              <td class="title">Divida Pública Consolidada</td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput0"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaPublicaInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr>
              <td class="title">Divida Consolidada Liquida</td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput0"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDividaConsolidadaLiquidaInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr>
              <td class="title">Receitas Primarias advindas de PPP (VII)</td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput0"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaPPPInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr>
              <td class="title">Despesas Primárias geradas por PPP (VIII)</td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput0"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput1"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput2"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput3" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput4" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput5" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrCorrente" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput6" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput7" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="percentualPib" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: '', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrDespesaPrimariaGeradaPPPInput8" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>


            <tr>
              <td class="title">Receitas Correntes Líquidas Projetada</td>
              <td>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaCorrenteLiquidaPInput0"></eqp-nebular-input>
              </td>
              <td>
              </td>
              <td>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaCorrenteLiquidaPInput1" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
              </td>
              <td>
              </td>
              <td>
                <eqp-nebular-input [size]="'small'" name="vlrConstante" placeholder="-" [style]="'currency' "
                  [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-center"
                  formControlName="vlrReceitaCorrenteLiquidaPInput2" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"></eqp-nebular-input>
              </td>
              <td>
              </td>
              <td class="borderColumn">
                <button type="button" class="btn btn-success float-md-right" [disabled]="!formulario.get('annualGoals2020.uuid')?.value"
                  (click)="gravarLinha()">Gravar
                </button>
              </td>
            </tr>
            <tr>
            </tr>
            <tr>
          </tbody>
        </table>
      </div>

    </ng-container>
  </nb-card-body>
</nb-card>