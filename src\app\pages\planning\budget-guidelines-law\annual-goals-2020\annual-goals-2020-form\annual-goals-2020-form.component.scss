
.table-container{

    tbody tr th:nth-child(1){
      border: solid 1px #c5cee0;
    }
    tbody tr th:nth-child(2){
      border: solid 1px #c5cee0;
    }
    tbody tr th:nth-child(3){
      border: solid 1px #c5cee0;
    }
    tbody tr th:nth-child(4){
      border: solid 1px #c5cee0;
    }
    .header{
       border: solid 1px #c5cee0;
       border-left: hidden;
       border-right: hidden;
    }


    table{
      th, td{
        padding: 10px 11px
      }

      .Ano{
        text-align: center;
        font-weight: 500;
        font-size: 13px;
        padding-top: 12px;
        padding-bottom: 12px;
      }
      .DadosTabela{
        text-align: center; 
        border: solid 1px #c5cee0;
        font-weight: 500;
        font-size: 13px;
        padding-top: 12px;
        padding-bottom: 12px;
      }

      .title{
        font-size: 13px
      }
      
      .especificacao{
        width: 320px;
        font-weight: 500;
      }

      p{
        margin-top: 10px;
        margin-bottom: -3rem;
      }
      
      .borderColumn{
        border-bottom: hidden;
        
        
      }    

      .onRowPrepared td:nth-child(1){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(2){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(3){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(4){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(5){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(6){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(7){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(8){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(9){
      background-color: #adadad;
      }
      .onRowPrepared td:nth-child(10){
      background-color: #adadad;
      }
    }
  }