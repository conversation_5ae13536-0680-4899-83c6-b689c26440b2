import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import {
  ModalConfirmarComponent,
} from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { Subscription } from 'rxjs'

import {
  ToastrService,
} from '../../../../../@common/services/toastr/toastr.service'
import { MenuService } from '../../../../menu.service'
import { AnnualGoals2020Service } from '../annual-goals-2020.service'
import { EditorDocComponent } from '../../../../../@common/dialogs/editor-doc/editor-doc.component'
import { DecreeSearchAnnualGoals2020Component } from '@common/dialogs/decree-search-annual-goals-2020/decree-search-annual-goals-2020.component'
import { UserDataService } from '@guards/services/user-data.service'


@Component({
  selector: 'eqp-annual-goals-2020-form',
  templateUrl: './annual-goals-2020-form.component.html',
  styleUrls: ['./annual-goals-2020-form.component.scss'],
})
export class AnnualGoals2020FormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy {
  public loading: boolean = false
  public pageTitle: string = 'Metas anuais 2013 - 2020'
  public ano0: string
  public ano1: string
  public ano2: string
  public formulario: FormGroup
  public nomeData: any
  public riscoFiscalTceData: any
  public subtotalcolunasTabela: number
  public colunasRegra: any
  public dtoDados: any
  public dadosDto: any
  public isCreated: boolean = false
  private subscription: Subscription
  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<AnnualGoals2020FormComponent>


  constructor(
    private formBuilder: FormBuilder,
    private service: AnnualGoals2020Service,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
    private userService: UserDataService
  ) {
    super(menuService, router)
    this.permissao('/metas-anuais-2013-2020')
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
    this.fetchGrid()
  }

  public ngOnDestroy(): void { }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      annualGoals2020: this.formBuilder.group({
        uuid: [''],
        lei: [''],
        leiUuid: ['test-uuid'],
        exercicioReferencia: [null, Validators.required],
        flagPublicar: [null],
        valorReceitaTotalCorrente: [null],
        valorReceitaTotalConstante: [null],
        valorReceitaTotalPercentualPib: [null],
        valorReceitaPrimariaCorrente: [null],
        valorReceitaPrimariaConstante: [null],
        valorReceitaPrimariaPercentualPib: [null],
        valorDespesaTotalCorrente: [null],
        valorDespesaTotalPercentualPib: [null],
        valorDespesaPrimariaCorrente: [null],
        valorDespesaPrimariaConstante: [null],
        valorDespesaPrimariaPercentualPib: [null],
        valorResidualNominalCorrente: [null],
        valorResidualNominalConstante: [null],
        valorResidualNominalPercentualPib: [null],
        valorDividaPublicaCorrente: [null],
        valorDividaPublicaConstante: [null],
        valorDividaPublicaPercentualPib: [null],
        valorDividaConsolidadaLiquidaCorrente: [null],
        valorDividaConstanteLiquidaConstante: [null],
        valorDividaConstanteLiquidaPercentualPib: [null],
        valorReceitaPrimariaPppCorrente: [null],
        valorReceitaPrimariaPppConstante: [null],
        valorReceitaPrimariaPppPercentualPib: [null],
        valorDespesaPrimariaPppCorrente: [null],
        valorDespesaPrimariaPppConstante: [null],
        valorDespesaPrimariaPppPercentualPib: [null],
        valorDespesaTotalConstante: [null],
        valorProjetadoReceitaCorrenteLiquida: [null],

        vlrReceitaInput0: [null],
        vlrReceitaInput1: [null],
        vlrReceitaInput2: [null],
        vlrReceitaInput3: [null],
        vlrReceitaInput4: [null],
        vlrReceitaInput5: [null],
        vlrReceitaInput6: [null],
        vlrReceitaInput7: [null],
        vlrReceitaInput8: [null],

        vlrReceitaPrimariaInput0: [null],
        vlrReceitaPrimariaInput1: [null],
        vlrReceitaPrimariaInput2: [null],
        vlrReceitaPrimariaInput3: [null],
        vlrReceitaPrimariaInput4: [null],
        vlrReceitaPrimariaInput5: [null],
        vlrReceitaPrimariaInput6: [null],
        vlrReceitaPrimariaInput7: [null],
        vlrReceitaPrimariaInput8: [null],

        vlrDespesaTotalInput0: [null],
        vlrDespesaTotalInput1: [null],
        vlrDespesaTotalInput2: [null],
        vlrDespesaTotalInput3: [null],
        vlrDespesaTotalInput4: [null],
        vlrDespesaTotalInput5: [null],
        vlrDespesaTotalInput6: [null],
        vlrDespesaTotalInput7: [null],
        vlrDespesaTotalInput8: [null],

        vlrDespesaPrimariaInput0: [null],
        vlrDespesaPrimariaInput1: [null],
        vlrDespesaPrimariaInput2: [null],
        vlrDespesaPrimariaInput3: [null],
        vlrDespesaPrimariaInput4: [null],
        vlrDespesaPrimariaInput5: [null],
        vlrDespesaPrimariaInput6: [null],
        vlrDespesaPrimariaInput7: [null],
        vlrDespesaPrimariaInput8: [null],

        vlrResultadoNominalInput0: [null],
        vlrResultadoNominalInput1: [null],
        vlrResultadoNominalInput2: [null],
        vlrResultadoNominalInput3: [null],
        vlrResultadoNominalInput4: [null],
        vlrResultadoNominalInput5: [null],
        vlrResultadoNominalInput6: [null],
        vlrResultadoNominalInput7: [null],
        vlrResultadoNominalInput8: [null],

        vlrDividaPublicaInput0: [null],
        vlrDividaPublicaInput1: [null],
        vlrDividaPublicaInput2: [null],
        vlrDividaPublicaInput3: [null],
        vlrDividaPublicaInput4: [null],
        vlrDividaPublicaInput5: [null],
        vlrDividaPublicaInput6: [null],
        vlrDividaPublicaInput7: [null],
        vlrDividaPublicaInput8: [null],

        vlrDividaConsolidadaLiquidaInput0: [null],
        vlrDividaConsolidadaLiquidaInput1: [null],
        vlrDividaConsolidadaLiquidaInput2: [null],
        vlrDividaConsolidadaLiquidaInput3: [null],
        vlrDividaConsolidadaLiquidaInput4: [null],
        vlrDividaConsolidadaLiquidaInput5: [null],
        vlrDividaConsolidadaLiquidaInput6: [null],
        vlrDividaConsolidadaLiquidaInput7: [null],
        vlrDividaConsolidadaLiquidaInput8: [null],

        vlrDespesaPrimariaPPPInput0: [null],
        vlrDespesaPrimariaPPPInput1: [null],
        vlrDespesaPrimariaPPPInput2: [null],
        vlrDespesaPrimariaPPPInput3: [null],
        vlrDespesaPrimariaPPPInput4: [null],
        vlrDespesaPrimariaPPPInput5: [null],
        vlrDespesaPrimariaPPPInput6: [null],
        vlrDespesaPrimariaPPPInput7: [null],
        vlrDespesaPrimariaPPPInput8: [null],

        vlrDespesaPrimariaGeradaPPPInput0: [null],
        vlrDespesaPrimariaGeradaPPPInput1: [null],
        vlrDespesaPrimariaGeradaPPPInput2: [null],
        vlrDespesaPrimariaGeradaPPPInput3: [null],
        vlrDespesaPrimariaGeradaPPPInput4: [null],
        vlrDespesaPrimariaGeradaPPPInput5: [null],
        vlrDespesaPrimariaGeradaPPPInput6: [null],
        vlrDespesaPrimariaGeradaPPPInput7: [null],
        vlrDespesaPrimariaGeradaPPPInput8: [null],

        vlrReceitaCorrenteLiquidaPInput0: [null],
        vlrReceitaCorrenteLiquidaPInput1: [null],
        vlrReceitaCorrenteLiquidaPInput2: [null],

        fonte: [null],
        notaExplicativa: [null],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid && !this.modal) this.buscar(uuid)
      else this.loadSelects()
    })
  }

  private fetchGrid(): void {
    const exerciseReference = parseInt(this.userService.userData.exercicio.toString().substring(0, 4));
    this.formulario.get('annualGoals2020.exercicioReferencia').patchValue(exerciseReference);

    this.subscription = this.service
      .getMetaAnualValor()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(data => {
        if (data.dados != undefined) {
          const validationTwoYearOne = data.dados.anosReferencia[0] && data.dados.anosReferencia[1] && data.dados.anosReferencia[2]
          const validationTwoYearTwo = data.dados.anosReferencia[0] && data.dados.anosReferencia[1]
          const validationTwoYearTree = data.dados.anosReferencia[0]

          this.formulario.get('annualGoals2020').patchValue(data.dados)
          this.formulario.get('annualGoals2020.uuid').patchValue(data.dados.uuid)
          this.formulario.get('annualGoals2020.leiUuid').patchValue(data.dados.leiUuid.uuid)
          this.formulario.get('annualGoals2020.lei').patchValue(`${data.dados.leiUuid.descricaoTipoDocumento} / ${data.dados.leiUuid.anoInicialAplicacao}`)
          this.formulario.get('annualGoals2020.exercicioReferencia').patchValue(data.dados.exercicioReferencia || exerciseReference)
          this.formulario.get('annualGoals2020.flagPublicar').patchValue(data.dados.flagPublicar === 'N' ? false : true)
          this.formulario.get('annualGoals2020.fonte').patchValue(data.dados.publicacao.fonte)
          this.formulario.get('annualGoals2020.notaExplicativa').patchValue(data.dados.publicacao.notaExplicativa)

          if (validationTwoYearOne) {
            this.ano0 = this.formulario.get('annualGoals2020.exercicioReferencia').value
            this.ano1 = this.formulario.get('annualGoals2020.exercicioReferencia').value
            this.ano2 = this.formulario.get('annualGoals2020.exercicioReferencia').value

            this.formulario.get('annualGoals2020.vlrReceitaInput0').patchValue(data.dados.anosReferencia[0].valorReceitaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput1').patchValue(data.dados.anosReferencia[0].valorReceitaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput2').patchValue(data.dados.anosReferencia[0].valorReceitaTotalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput3').patchValue(data.dados.anosReferencia[1].valorReceitaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput4').patchValue(data.dados.anosReferencia[1].valorReceitaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput5').patchValue(data.dados.anosReferencia[1].valorReceitaTotalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput6').patchValue(data.dados.anosReferencia[2].valorReceitaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput7').patchValue(data.dados.anosReferencia[2].valorReceitaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput8').patchValue(data.dados.anosReferencia[2].valorReceitaTotalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput0').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput1').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput2').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPercentualPib)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput3').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput4').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput5').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput6').patchValue(data.dados.anosReferencia[2].valorReceitaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput7').patchValue(data.dados.anosReferencia[2].valorReceitaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput8').patchValue(data.dados.anosReferencia[2].valorReceitaPrimariaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaTotalInput0').patchValue(data.dados.anosReferencia[0].valorDespesaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput1').patchValue(data.dados.anosReferencia[0].valorDespesaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput2').patchValue(data.dados.anosReferencia[0].valorDespesaTotalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput3').patchValue(data.dados.anosReferencia[1].valorDespesaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput4').patchValue(data.dados.anosReferencia[1].valorDespesaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput5').patchValue(data.dados.anosReferencia[1].valorDespesaTotalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput6').patchValue(data.dados.anosReferencia[2].valorDespesaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput7').patchValue(data.dados.anosReferencia[2].valorDespesaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput8').patchValue(data.dados.anosReferencia[2].valorDespesaTotalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput0').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput1').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput2').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput3').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput4').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput5').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput6').patchValue(data.dados.anosReferencia[2].valorDespesaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput7').patchValue(data.dados.anosReferencia[2].valorDespesaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput8').patchValue(data.dados.anosReferencia[2].valorDespesaPrimariaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrResultadoNominalInput0').patchValue(data.dados.anosReferencia[0].valorResidualNominalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput1').patchValue(data.dados.anosReferencia[0].valorResidualNominalConstante || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput2').patchValue(data.dados.anosReferencia[0].valorResidualNominalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput3').patchValue(data.dados.anosReferencia[1].valorResidualNominalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput4').patchValue(data.dados.anosReferencia[1].valorResidualNominalConstante || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput5').patchValue(data.dados.anosReferencia[1].valorResidualNominalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput6').patchValue(data.dados.anosReferencia[2].valorResidualNominalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput7').patchValue(data.dados.anosReferencia[2].valorResidualNominalConstante || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput8').patchValue(data.dados.anosReferencia[2].valorResidualNominalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDividaPublicaInput0').patchValue(data.dados.anosReferencia[0].valorDividaPublicaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput1').patchValue(data.dados.anosReferencia[0].valorDividaPublicaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput2').patchValue(data.dados.anosReferencia[0].valorDividaPublicaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput3').patchValue(data.dados.anosReferencia[1].valorDividaPublicaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput4').patchValue(data.dados.anosReferencia[1].valorDividaPublicaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput5').patchValue(data.dados.anosReferencia[1].valorDividaPublicaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput6').patchValue(data.dados.anosReferencia[2].valorDividaPublicaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput7').patchValue(data.dados.anosReferencia[2].valorDividaPublicaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput8').patchValue(data.dados.anosReferencia[2].valorDividaPublicaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput0').patchValue(data.dados.anosReferencia[0].valorDividaConsolidadaLiquidaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput1').patchValue(data.dados.anosReferencia[0].valorDividaConstanteLiquidaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput2').patchValue(data.dados.anosReferencia[0].valorDividaConstanteLiquidaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput3').patchValue(data.dados.anosReferencia[1].valorDividaConsolidadaLiquidaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput4').patchValue(data.dados.anosReferencia[1].valorDividaConstanteLiquidaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput5').patchValue(data.dados.anosReferencia[1].valorDividaConstanteLiquidaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput6').patchValue(data.dados.anosReferencia[2].valorDividaConsolidadaLiquidaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput7').patchValue(data.dados.anosReferencia[2].valorDividaConstanteLiquidaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput8').patchValue(data.dados.anosReferencia[2].valorDividaConstanteLiquidaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput0').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput1').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput2').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput3').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput4').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput5').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPppPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput6').patchValue(data.dados.anosReferencia[2].valorReceitaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput7').patchValue(data.dados.anosReferencia[2].valorReceitaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput8').patchValue(data.dados.anosReferencia[2].valorReceitaPrimariaPppPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput0').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput1').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput2').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput3').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput4').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput5').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPppPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput6').patchValue(data.dados.anosReferencia[2].valorDespesaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput7').patchValue(data.dados.anosReferencia[2].valorDespesaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput8').patchValue(data.dados.anosReferencia[2].valorDespesaPrimariaPppPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput0').patchValue(data.dados.anosReferencia[0].valorProjetadoReceitaCorrenteLiquida || 0)
            this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput1').patchValue(data.dados.anosReferencia[1].valorProjetadoReceitaCorrenteLiquida || 0)
            this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput2').patchValue(data.dados.anosReferencia[2].valorProjetadoReceitaCorrenteLiquida || 0)
          }

          if (validationTwoYearTwo) {
            this.ano0 = data.dados.anosReferencia[0].exercicioReferencia
            this.ano1 = data.dados.anosReferencia[1].exercicioReferencia

            this.formulario.get('annualGoals2020.vlrReceitaInput0').patchValue(data.dados.anosReferencia[0].valorReceitaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput1').patchValue(data.dados.anosReferencia[0].valorReceitaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput2').patchValue(data.dados.anosReferencia[0].valorReceitaTotalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput3').patchValue(data.dados.anosReferencia[1].valorReceitaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput4').patchValue(data.dados.anosReferencia[1].valorReceitaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput5').patchValue(data.dados.anosReferencia[1].valorReceitaTotalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput0').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput1').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput2').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput3').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput4').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput5').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaTotalInput0').patchValue(data.dados.anosReferencia[0].valorDespesaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput1').patchValue(data.dados.anosReferencia[0].valorDespesaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput2').patchValue(data.dados.anosReferencia[0].valorDespesaTotalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput3').patchValue(data.dados.anosReferencia[1].valorDespesaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput4').patchValue(data.dados.anosReferencia[1].valorDespesaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput5').patchValue(data.dados.anosReferencia[1].valorDespesaTotalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput0').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput1').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput2').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput3').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput4').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput5').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPercentualPib)

            this.formulario.get('annualGoals2020.vlrResultadoNominalInput0').patchValue(data.dados.anosReferencia[0].valorResidualNominalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput1').patchValue(data.dados.anosReferencia[0].valorResidualNominalConstante || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput2').patchValue(data.dados.anosReferencia[0].valorResidualNominalPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput3').patchValue(data.dados.anosReferencia[1].valorResidualNominalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput4').patchValue(data.dados.anosReferencia[1].valorResidualNominalConstante || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput5').patchValue(data.dados.anosReferencia[1].valorResidualNominalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDividaPublicaInput0').patchValue(data.dados.anosReferencia[0].valorDividaPublicaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput1').patchValue(data.dados.anosReferencia[0].valorDividaPublicaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput2').patchValue(data.dados.anosReferencia[0].valorDividaPublicaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput3').patchValue(data.dados.anosReferencia[1].valorDividaPublicaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput4').patchValue(data.dados.anosReferencia[1].valorDividaPublicaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput5').patchValue(data.dados.anosReferencia[1].valorDividaPublicaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput0').patchValue(data.dados.anosReferencia[0].valorDividaConsolidadaLiquidaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput1').patchValue(data.dados.anosReferencia[0].valorDividaConstanteLiquidaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput2').patchValue(data.dados.anosReferencia[0].valorDividaConstanteLiquidaPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput3').patchValue(data.dados.anosReferencia[1].valorDividaConsolidadaLiquidaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput4').patchValue(data.dados.anosReferencia[1].valorDividaConstanteLiquidaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput5').patchValue(data.dados.anosReferencia[1].valorDividaConstanteLiquidaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput0').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput1').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput2').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput3').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput4').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput5').patchValue(data.dados.anosReferencia[1].valorReceitaPrimariaPppPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput0').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput1').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput2').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppPercentualPib || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput3').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput4').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput5').patchValue(data.dados.anosReferencia[1].valorDespesaPrimariaPppPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput0').patchValue(data.dados.anosReferencia[0].valorProjetadoReceitaCorrenteLiquida || 0)
            this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput1').patchValue(data.dados.anosReferencia[1].valorProjetadoReceitaCorrenteLiquida || 0)
          }

          if (validationTwoYearTree) {
            this.ano0 = data.dados.anosReferencia[0].exercicioReferencia

            this.formulario.get('annualGoals2020.vlrReceitaInput0').patchValue(data.dados.anosReferencia[0].valorReceitaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput1').patchValue(data.dados.anosReferencia[0].valorReceitaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaInput2').patchValue(data.dados.anosReferencia[0].valorReceitaTotalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput0').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput1').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput2').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaTotalInput0').patchValue(data.dados.anosReferencia[0].valorDespesaTotalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput1').patchValue(data.dados.anosReferencia[0].valorDespesaTotalConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaTotalInput2').patchValue(data.dados.anosReferencia[0].valorDespesaTotalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput0').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaCorrente)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput1').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput2').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrResultadoNominalInput0').patchValue(data.dados.anosReferencia[0].valorResidualNominalCorrente || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput1').patchValue(data.dados.anosReferencia[0].valorResidualNominalConstante || 0)
            this.formulario.get('annualGoals2020.vlrResultadoNominalInput2').patchValue(data.dados.anosReferencia[0].valorResidualNominalPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDividaPublicaInput0').patchValue(data.dados.anosReferencia[0].valorDividaPublicaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput1').patchValue(data.dados.anosReferencia[0].valorDividaPublicaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaPublicaInput2').patchValue(data.dados.anosReferencia[0].valorDividaPublicaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput0').patchValue(data.dados.anosReferencia[0].valorDividaConsolidadaLiquidaCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput1').patchValue(data.dados.anosReferencia[0].valorDividaConstanteLiquidaConstante || 0)
            this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput2').patchValue(data.dados.anosReferencia[0].valorDividaConstanteLiquidaPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput0').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput1').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput2').patchValue(data.dados.anosReferencia[0].valorReceitaPrimariaPppPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput0').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppCorrente || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput1').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppConstante || 0)
            this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput2').patchValue(data.dados.anosReferencia[0].valorDespesaPrimariaPppPercentualPib || 0)

            this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput0').patchValue(data.dados.anosReferencia[0].valorProjetadoReceitaCorrenteLiquida || 0)
          }
        } else {
          this.isCreated = true;
        }

        this.loadSelects()
        this.dtoDados = data.dados
      })

    setTimeout(() => {
      this.dadosDto = this.dtoDados
    }, 1000)
  }

  private loadSelects(): void {
    this.riscoFiscalTceData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'meta_anual_ldo_2020/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.loadSelects()
      })
  }

  public cancelar(retorno): void {
    if (!this.modal) {
      this.gravarParametros()
      this.router.navigate([`metas-anuais-2013-2020`])
    } else {
      this.ref.close(retorno)
    }
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      if (this.formulario.get('annualGoals2020.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    if (this.isCreated) {
      this.createdDto()
    }

    this.service
      .post(this.getMetasAnuaisDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Meta anual 2013 - 2020 criado com sucesso.',
        })
        this.formulario.get('annualGoals2020.flagPublicar').reset()
        this.formulario.get('annualGoals2020.fonte').reset()
        this.formulario.get('annualGoals2020.notaExplicativa').reset()
        this.fetchGrid()

      })
  }

  public openDecreeSearch(): void {
    const dialogRef = this.dialogService.open(DecreeSearchAnnualGoals2020Component, {
      context: {
        exercise: this.formulario.get('annualGoals2020.exercicioReferencia').value
      }
    })

    dialogRef.onClose.subscribe(decree => {
      if (decree) {
        this.formulario.get('annualGoals2020.lei').patchValue(`${decree.descricaoTipoDocumento} / ${decree.anoInicialAplicacao}`)
        this.formulario.get('annualGoals2020.leiUuid').patchValue(decree.uuid)
      }
    })
  }
  private atualizar(): void {
    this.service
      .put(this.getMetasAnuaisDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Meta anual 2013 - 2020 atualizado com sucesso.',
        })
        this.fetchGrid()
      })
  }

  private getMetasAnuaisDto(): any {

    const dtoTemp = this.formulario.getRawValue()
    let dto  = {
      uuid: dtoTemp.annualGoals2020.uuid,
      exercicioReferencia: dtoTemp.annualGoals2020.exercicioReferencia,
      leiUuid: this.formulario.get('annualGoals2020.leiUuid').value,
      publicacao: {
        flagPublicar: dtoTemp.annualGoals2020.flagPublicar === true ? 'S' : 'N',
        fonte: dtoTemp.annualGoals2020.fonte,
        leiUuid: dtoTemp.annualGoals2020.leiUuid,
        notaExplicativa: dtoTemp.annualGoals2020.notaExplicativa
      },
  
      valorReceitaTotalCorrente: this.formulario.get('annualGoals2020.vlrReceitaInput0').value || 0,
      valorReceitaTotalConstante: this.formulario.get('annualGoals2020.vlrReceitaInput1').value || 0,
      valorReceitaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaInput2').value || 0,
  
      valorReceitaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput0').value || 0,
      valorReceitaPrimariaConstante: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput1').value || 0,
      valorReceitaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput2').value || 0,
  
      valorDespesaTotalCorrente: this.formulario.get('annualGoals2020.vlrDespesaTotalInput0').value || 0,
      valorDespesaTotalConstante: this.formulario.get('annualGoals2020.vlrDespesaTotalInput1').value || 0,
      valorDespesaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaTotalInput2').value || 0,
  
      valorDespesaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput0').value || 0,
      valorDespesaPrimariaConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput1').value || 0,
      valorDespesaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput2').value || 0,
  
      valorResidualNominalCorrente: this.formulario.get('annualGoals2020.vlrResultadoNominalInput0').value || 0,
      valorResidualNominalConstante: this.formulario.get('annualGoals2020.vlrResultadoNominalInput1').value || 0,
      valorResidualNominalPercentualPib: this.formulario.get('annualGoals2020.vlrResultadoNominalInput2').value || 0,
  
      valorDividaPublicaCorrente: this.formulario.get('annualGoals2020.vlrDividaPublicaInput0').value || 0,
      valorDividaPublicaConstante: this.formulario.get('annualGoals2020.vlrDividaPublicaInput1').value || 0,
      valorDividaPublicaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaPublicaInput2').value || 0,
  
      valorDividaConsolidadaLiquidaCorrente: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput0').value || 0,
      valorDividaConstanteLiquidaConstante: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput1').value || 0,
      valorDividaConstanteLiquidaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput2').value || 0,
  
      valorReceitaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput0').value || 0,
      valorReceitaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput1').value || 0,
      valorReceitaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput2').value || 0,
  
      valorDespesaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput0').value || 0,
      valorDespesaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput1').value || 0,
      valorDespesaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput2').value || 0,
  
      valorProjetadoReceitaCorrenteLiquida: this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput0').value || 0,
    }

    return dto
  }

  public abrirEditorDocumentoFonte(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.formulario.get('annualGoals2020.fonte').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.formulario.get('annualGoals2020.fonte').patchValue(retorno)
    })
  }

  public abrirEditorDocumentoNotaExplicativa(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.formulario.get('annualGoals2020.notaExplicativa').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.formulario.get('annualGoals2020.notaExplicativa').patchValue(retorno)
    })
  }

  public gravarLinha(): void {
    const request = this.dadosDto;
    this.subscription = this.service
      .putMetaAnualValor2020(this.getMetasAnuaisDto0(request))
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        retorno => {
          this.toastr.send({
            success: true,
            message: 'Meta anual atualizado com sucesso.',
          })
        },
        () => {
          this.fetchGrid()
        },
      )

    this.subscription = this.service
      .putMetaAnualValor2020(this.getMetasAnuaisDto1(request))
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        retorno => {
          this.toastr.send({
            success: true,
            message: 'Meta anual atualizado com sucesso.',
          })
        },
        () => {
          this.fetchGrid()
        },
      )

    this.subscription = this.service
      .putMetaAnualValor2020(this.getMetasAnuaisDto2(request))
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        retorno => {
          this.toastr.send({
            success: true,
            message: 'Meta anual atualizado com sucesso.',
          })
        },
        () => {
          this.fetchGrid()
        },
      )
  }

  private getMetasAnuaisDto0(request: any): any {
    const dtoData = request

    const dto = {
      uuid: dtoData.anosReferencia[0].uuid,
      leiUuid: dtoData.anosReferencia[0].leiUuid,
      publicacao: {
      flagPublicar: dtoData.publicacao.flagPublicar,
      fonte: dtoData.publicacao.fonte,
      leiUuid: dtoData.publicacao.lei.uuid,
      notaExplicativa: dtoData.publicacao.notaExplicativa,
      },
      exercicioReferencia: dtoData.anosReferencia[0].exercicioReferencia,

      valorReceitaTotalCorrente: this.formulario.get('annualGoals2020.vlrReceitaInput0').value,
      valorReceitaTotalConstante: this.formulario.get('annualGoals2020.vlrReceitaInput1').value,
      valorReceitaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaInput2').value,

      valorReceitaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput0').value,
      valorReceitaPrimariaConstante: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput1').value,
      valorReceitaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput2').value,

      valorDespesaTotalCorrente: this.formulario.get('annualGoals2020.vlrDespesaTotalInput0').value,
      valorDespesaTotalConstante: this.formulario.get('annualGoals2020.vlrDespesaTotalInput1').value,
      valorDespesaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaTotalInput2').value,

      valorDespesaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput0').value,
      valorDespesaPrimariaConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput1').value,
      valorDespesaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput2').value,

      valorResidualNominalCorrente: this.formulario.get('annualGoals2020.vlrResultadoNominalInput0').value,
      valorResidualNominalConstante: this.formulario.get('annualGoals2020.vlrResultadoNominalInput1').value,
      valorResidualNominalPercentualPib: this.formulario.get('annualGoals2020.vlrResultadoNominalInput2').value,

      valorDividaPublicaCorrente: this.formulario.get('annualGoals2020.vlrDividaPublicaInput0').value,
      valorDividaPublicaConstante: this.formulario.get('annualGoals2020.vlrDividaPublicaInput1').value,
      valorDividaPublicaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaPublicaInput2').value,

      valorDividaConsolidadaLiquidaCorrente: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput0').value,
      valorDividaConstanteLiquidaConstante: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput1').value,
      valorDividaConstanteLiquidaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput2').value,

      valorReceitaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput0').value,
      valorReceitaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput1').value,
      valorReceitaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput2').value,

      valorDespesaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput0').value,
      valorDespesaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput1').value,
      valorDespesaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput2').value,

      valorProjetadoReceitaCorrenteLiquida: this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput0').value,
    }

    return dto
  }

  private getMetasAnuaisDto1(request: any): any {
    const dtoData = request

    const dto = {
      uuid: dtoData.anosReferencia[1].uuid,
      leiUuid: dtoData.anosReferencia[1].leiUuid,

      publicacao: {
        flagPublicar: dtoData.publicacao.flagPublicar,
        fonte: dtoData.publicacao.fonte,
        leiUuid: dtoData.publicacao.lei.uuid,
        notaExplicativa: dtoData.publicacao.notaExplicativa,
        },

      exercicioReferencia: dtoData.anosReferencia[1].exercicioReferencia,

      valorReceitaTotalCorrente: this.formulario.get('annualGoals2020.vlrReceitaInput3').value,
      valorReceitaTotalConstante: this.formulario.get('annualGoals2020.vlrReceitaInput4').value,
      valorReceitaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaInput5').value,

      valorReceitaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput3').value,
      valorReceitaPrimariaConstante: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput4').value,
      valorReceitaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput5').value,

      valorDespesaTotalCorrente: this.formulario.get('annualGoals2020.vlrDespesaTotalInput3').value,
      valorDespesaTotalConstante: this.formulario.get('annualGoals2020.vlrDespesaTotalInput4').value,
      valorDespesaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaTotalInput5').value,

      valorDespesaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput3').value,
      valorDespesaPrimariaConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput4').value,
      valorDespesaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput5').value,

      valorResidualNominalCorrente: this.formulario.get('annualGoals2020.vlrResultadoNominalInput3').value,
      valorResidualNominalConstante: this.formulario.get('annualGoals2020.vlrResultadoNominalInput4').value,
      valorResidualNominalPercentualPib: this.formulario.get('annualGoals2020.vlrResultadoNominalInput5').value,

      valorDividaPublicaCorrente: this.formulario.get('annualGoals2020.vlrDividaPublicaInput3').value,
      valorDividaPublicaConstante: this.formulario.get('annualGoals2020.vlrDividaPublicaInput4').value,
      valorDividaPublicaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaPublicaInput5').value,

      valorDividaConsolidadaLiquidaCorrente: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput3').value,
      valorDividaConstanteLiquidaConstante: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput4').value,
      valorDividaConstanteLiquidaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput5').value,

      valorReceitaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput3').value,
      valorReceitaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput4').value,
      valorReceitaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput5').value,

      valorDespesaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput3').value,
      valorDespesaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput4').value,
      valorDespesaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput5').value,

      valorProjetadoReceitaCorrenteLiquida: this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput1').value,
    }

    return dto
  }

  private getMetasAnuaisDto2(request: any): any {
    const dtoData = request

    const dto = {
      uuid: dtoData.anosReferencia[2].uuid,
      leiUuid: dtoData.anosReferencia[2].leiUuid,

      publicacao: {
        flagPublicar: dtoData.publicacao.flagPublicar,
        fonte: dtoData.publicacao.fonte,
        leiUuid: dtoData.publicacao.lei.uuid,
        notaExplicativa: dtoData.publicacao.notaExplicativa,
        },

      exercicioReferencia: dtoData.anosReferencia[2].exercicioReferencia,

      valorReceitaTotalCorrente: this.formulario.get('annualGoals2020.vlrReceitaInput6').value,
      valorReceitaTotalConstante: this.formulario.get('annualGoals2020.vlrReceitaInput7').value,
      valorReceitaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaInput8').value,

      valorReceitaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput6').value,
      valorReceitaPrimariaConstante: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput7').value,
      valorReceitaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrReceitaPrimariaInput8').value,

      valorDespesaTotalCorrente: this.formulario.get('annualGoals2020.vlrDespesaTotalInput6').value,
      valorDespesaTotalConstante: this.formulario.get('annualGoals2020.vlrDespesaTotalInput7').value,
      valorDespesaTotalPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaTotalInput8').value,

      valorDespesaPrimariaCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput6').value,
      valorDespesaPrimariaConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput7').value,
      valorDespesaPrimariaPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaInput8').value,

      valorResidualNominalCorrente: this.formulario.get('annualGoals2020.vlrResultadoNominalInput6').value,
      valorResidualNominalConstante: this.formulario.get('annualGoals2020.vlrResultadoNominalInput7').value,
      valorResidualNominalPercentualPib: this.formulario.get('annualGoals2020.vlrResultadoNominalInput8').value,

      valorDividaPublicaCorrente: this.formulario.get('annualGoals2020.vlrDividaPublicaInput6').value,
      valorDividaPublicaConstante: this.formulario.get('annualGoals2020.vlrDividaPublicaInput7').value,
      valorDividaPublicaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaPublicaInput8').value,

      valorDividaConsolidadaLiquidaCorrente: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput6').value,
      valorDividaConstanteLiquidaConstante: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput7').value,
      valorDividaConstanteLiquidaPercentualPib: this.formulario.get('annualGoals2020.vlrDividaConsolidadaLiquidaInput8').value,

      valorReceitaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput6').value,
      valorReceitaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput7').value,
      valorReceitaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaPPPInput8').value,

      valorDespesaPrimariaPppCorrente: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput6').value,
      valorDespesaPrimariaPppConstante: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput7').value,
      valorDespesaPrimariaPppPercentualPib: this.formulario.get('annualGoals2020.vlrDespesaPrimariaGeradaPPPInput8').value,

      valorProjetadoReceitaCorrenteLiquida: this.formulario.get('annualGoals2020.vlrReceitaCorrenteLiquidaPInput2').value,
    }

    return dto
  }

  createdDto() {
  }
}
