import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'

import { BaseService } from '../../../../@common/services/base/base.service'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class  AnnualGoals2020Service extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'meta_anual_ldo_2020')
  }

  public getMetaAnualValor(): Observable<any> {
    return this.http.get<any>(`meta_anual_ldo_2020/agrupado`, { params: { take: '30' } })
  }

  public putMetaAnualValor2020(data: any): Observable<any> {
    return this.http.put<any>(`meta_anual_ldo_2020/${data.uuid}`, data)
  }
}
