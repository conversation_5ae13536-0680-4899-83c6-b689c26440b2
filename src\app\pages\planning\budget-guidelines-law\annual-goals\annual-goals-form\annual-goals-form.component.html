<nb-card [formGroup]="formulario" [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
        <eqp-breadcrumb></eqp-breadcrumb>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row" formGroupName="annualGoals">
      <div class="col-xl-3 col-lg-3 mb-3">
        <eqp-nebular-input
          [size]="'small'"
          [showSuffixIcon]="true"
          [primarySuffixIcon]="true"
          [firstSuffixIcon]="'search'"
          formControlName="lei"
          name="lei"
          label="Lei/Ato"
          placeholder="Lei/Ato"
          disabled
          (click)="openDecreeSearch()"
          required
        ></eqp-nebular-input>
      </div>
      <div class="mt-4">
        <eqp-publication-note-field
          formControlName="publicacao"
        ></eqp-publication-note-field>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>
        <button
          *ngIf="
            formulario.get('annualGoals.uuid').value &&
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <!-- (formulario.get('annualGoals.uuid').value && -->
        <button
          *ngIf="
              (nivelPermissao === 'EDITOR' ||
              nivelPermissao === 'FULL')
          "
          type="button"
          [disabled]="formulario.invalid"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>

<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-body [formGroup]="formulario">
    <ng-container>
      <dx-data-grid
        id="annualGoals"
        [dataSource]="colunasTabela"
        [allowColumnResizing]="false"
        [columnAutoWidth]="false"
        [showColumnLines]="false"
        [showRowLines]="false"
        [showBorders]="false"
        [rowAlternationEnabled]="false"
        [wordWrapEnabled]="true"
        [loadPanel]="false"
        [columnHidingEnabled]="true"
        [remoteOperations]="true"
        keyExpr="uuid"
        (onRowUpdated)="onRowUpdated($event)"
        (onCellPrepared)="onCellPrepared($event)"
        (onRowUpdating)="onRowUpdating($event)"
        (onRowPrepared)="onRowPrepared($event)"
      >
        <dxo-state-storing
          [enabled]="true"
          type="custom"
          [customLoad]="loadState"
          [customSave]="saveState"
          savingTimeout="100"
        ></dxo-state-storing>
        <dxo-export
          [enabled]="true"
          [excelWrapTextEnabled]="true"
          [excelFilterEnabled]="true"
          [fileName]="pageTitle"
        >
        </dxo-export>

        <dxo-paging
          [enabled]="true"
          [pageSize]="30"
          [pageIndex]="0"
        ></dxo-paging>

        <dxo-header-filter [visible]="false"> </dxo-header-filter>
        <dxo-filter-row [visible]="false"></dxo-filter-row>

        <dxo-sorting mode="multiple"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>
        <dxo-editing
          [allowUpdating]="allowUpdating"
          [allowAdding]="false"
          [allowDeleting]="false"
          mode="row"
        >
        </dxo-editing>

        <dxi-column
          caption="Especificação"
          dataField="nome"
          type="basic"
          cssClass="align-middle"
          width="298"
          [allowEditing]="false"
        >
        </dxi-column>

        <dxi-column
          [caption]="formulario.get('annualGoals.exercicioReferencia').value"
          alignment="center"
        >
          <dxi-column
            alignment="center"
            dataField="anosReferencia[0].vlrCorrente"
            caption="Corrente"
            type="decimal"
            [format]="{ style: 'currency', currency: 'BRL' }"
            [allowEditing]="true"
          >
          </dxi-column>
          <dxi-column
            alignment="center"
            dataField="anosReferencia[0].vlrConstante"
            caption="Constante"
            type="decimal"
            [format]="{ style: 'currency', currency: 'BRL' }"
            [allowEditing]="true"
          >
          </dxi-column>
          <dxi-column
            alignment="center"
            dataField="anosReferencia[0].percentualPib"
            caption="% PIB"
            type="decimal"
            format="0,###"
            [allowEditing]="true"
          >
          </dxi-column>
        </dxi-column>

        <dxi-column
          [caption]="
            formulario.get('annualGoals.exercicioReferencia').value + 1
          "
          alignment="center"
        >
          <dxi-column
            alignment="center"
            dataField="anosReferencia[1].vlrCorrente"
            caption="Corrente"
            type="decimal"
            [format]="{ style: 'currency', currency: 'BRL' }"
            [allowEditing]="true"
          >
          </dxi-column>
          <dxi-column
            alignment="center"
            dataField="anosReferencia[1].vlrConstante"
            caption="Constante"
            type="decimal"
            [format]="{ style: 'currency', currency: 'BRL' }"
            [allowEditing]="true"
          >
          </dxi-column>
          <dxi-column
            alignment="center"
            dataField="anosReferencia[1].percentualPib"
            caption="% PIB"
            type="decimal"
            format="0,###"
            [allowEditing]="true"
          >
          </dxi-column>
        </dxi-column>

        <dxi-column
          [caption]="
            formulario.get('annualGoals.exercicioReferencia').value + 2
          "
          alignment="center"
        >
          <dxi-column
            alignment="center"
            dataField="anosReferencia[2].vlrCorrente"
            caption="Corrente"
            type="decimal"
            [format]="{ style: 'currency', currency: 'BRL' }"
            [allowEditing]="true"
          >
          </dxi-column>
          <dxi-column
            alignment="center"
            dataField="anosReferencia[2].vlrConstante"
            caption="Constante"
            type="decimal"
            [format]="{ style: 'currency', currency: 'BRL' }"
            [allowEditing]="true"
          >
          </dxi-column>
          <dxi-column
            alignment="center"
            dataField="anosReferencia[2].percentualPib"
            caption="% PIB"
            type="decimal"
            format="0,###"
            [allowEditing]="true"
          >
          </dxi-column>
        </dxi-column>
      </dx-data-grid>
      <div formGroupName="annualGoals">
        <table class="recCorLiqProj">
          <tr
            class="dx-row dx-data-row"
            role="row"
            aria-selected="false"
            aria-rowindex="25"
          >
            <td class="td-one">Receita Corrente Líquida Projetada</td>
            <td class="td-two">
              <eqp-nebular-input
                [size]="'small'"
                formControlName="vlrProjetadoRcl0"
                name="vlrProjetadoRcl"
                placeholder="R$0,00"
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                class="text-left"
              >
              </eqp-nebular-input>
            </td>
            <td class="td-tree">
              <eqp-nebular-input
                [size]="'small'"
                formControlName="vlrProjetadoRcl1"
                name="vlrProjetadoRcl"
                placeholder="R$0,00"
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                class="text-left"
              >
              </eqp-nebular-input>
            </td>
            <td class="td-four">
              <eqp-nebular-input
                [size]="'small'"
                formControlName="vlrProjetadoRcl2"
                name="vlrProjetadoRcl"
                placeholder="R$0,00"
                [style]="'currency'"
                [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
                class="text-left"
              >
              </eqp-nebular-input>
            </td>
            <td>
              <button
                *ngIf="
                  (formulario.get('annualGoals.uuid').value &&
                    nivelPermissao === 'EDITOR') ||
                  nivelPermissao === 'FULL'
                "
                type="button"
                class="btn btn-success float-md-right"
                (click)="gravaReceitaCorrenteLiquidaProjetada()"
              >
                Gravar
              </button>
            </td>
          </tr>
        </table>
      </div>
    </ng-container>
  </nb-card-body>
</nb-card>
