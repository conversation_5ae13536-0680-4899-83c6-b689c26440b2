import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ModalConfirmarComponent } from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { Action, ActionReturn } from '../interfaces/action'

import { DecreeSearchAnnualGoalsComponent } from '@common/dialogs/decree-search-annual-goals/decree-search-annual-goals.component'
import { UserDataService } from '@guards/services/user-data.service'
import { ToastrService } from '../../../../../@common/services/toastr/toastr.service'
import { MenuService } from '../../../../menu.service'
import { AnnualGoalsService } from '../annual-goals.service'
import { EditorDocComponent } from './../../../../../@common/dialogs/editor-doc/editor-doc.component'

@Component({
  selector: 'eqp-annual-goals-form',
  templateUrl: './annual-goals-form.component.html',
  styleUrls: ['./annual-goals-form.component.scss'],
})
export class AnnualGoalsFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public isLoadIndicatorVisible: boolean = false
  public pageTitle: string = 'Meta anual'
  public formulario: FormGroup
  public nomeData: any
  public riscoFiscalTceData: any
  public colunasTabela: Action[] = []
  public colunasTabelaLength: any
  public subtotalcolunasTabela: number
  public colunasRegra: any
  public dtoDados: any
  public dadosDto: any
  public isVal: boolean = false
  public isVal2: boolean = false
  private subscription: Subscription
  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<AnnualGoalsFormComponent>

  constructor(
    private formBuilder: FormBuilder,
    private service: AnnualGoalsService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
    private userService: UserDataService,
  ) {
    super(menuService, router)
    this.permissao('/metas-anuais')
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
    this.fetchGrid()
  }

  public ngOnDestroy(): void {}

  public getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      annualGoals: this.formBuilder.group({
        uuid: [null],
        lei: [''],
        leiUuid: ['test-uuid'],
        exercicioReferencia: [null],
        flagPublicar: [null],
        vlrProjetadoRcl: [null],
        vlrProjetadoRcl0: [null],
        vlrProjetadoRcl1: [null],
        vlrProjetadoRcl2: [null],
        fonte: [null],
        notaExplicativa: [null],
        publicacao: []
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid && !this.modal) this.buscar(uuid)
      else this.loadSelects()
    })
  }

  private fetchGrid(): void {
    const exerciseReference = parseInt(
      this.userService.userData.exercicio.toString().substring(0, 4),
    )

    this.subscription = this.service
      .getMetaAnualLdoExercicioReferencia()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(data => {
        if (data.dados) {
          const val = data.dados.exercicioReferenciaAno[0]
            ? data.dados.exercicioReferenciaAno[0].vlrProjetadoRcl
            : 'notValue'
          const val1 = data.dados.exercicioReferenciaAno[1]
            ? data.dados.exercicioReferenciaAno[1].vlrProjetadoRcl
            : 'notValue'
          const val2 = data.dados.exercicioReferenciaAno[2]
            ? data.dados.exercicioReferenciaAno[2].vlrProjetadoRcl
            : 'notValue'

          this.formulario.get('annualGoals').patchValue(data.dados)
          this.formulario
            .get('annualGoals.flagPublicar')
            .patchValue(data.dados.flagPublicar === 'N' ? false : true)
          this.formulario
            .get('annualGoals.leiUuid')
            .patchValue(data.dados.lei.uuid)
          this.formulario
            .get('annualGoals.lei')
            .patchValue(
              `${data.dados.lei.descricaoTipoDocumento} / ${data.dados.lei.anoInicialAplicacao}`,
            )
          this.formulario
            .get('annualGoals.exercicioReferencia')
            .patchValue(data.dados.exercicioReferencia)

          if (val != 'notValue') {
            this.formulario
              .get('annualGoals.vlrProjetadoRcl0')
              .patchValue(data.dados.exercicioReferenciaAno[0].vlrProjetadoRcl)
          }

          if (val1 != 'notValue') {
            this.formulario
              .get('annualGoals.vlrProjetadoRcl1')
              .patchValue(data.dados.exercicioReferenciaAno[1].vlrProjetadoRcl)
          }

          if (val2 != 'notValue') {
            this.formulario
              .get('annualGoals.vlrProjetadoRcl2')
              .patchValue(data.dados.exercicioReferenciaAno[2].vlrProjetadoRcl)
          }

          this.loadSelects()
          this.dtoDados = data.dados

          if (this.dtoDados) {
            this.subscription = this.service
              .getMetaAnualValor()
              .pipe(finalize(() => (this.loading = false)))
              .subscribe((data: ActionReturn) => {
                if (data.dados.length > 0) {
                  if (
                    data.dados[0].anosReferencia[0].ano === exerciseReference
                  ) {
                    this.colunasTabela = data.dados
                  }
                }
              })
          }
        } else {
          this.formulario
            .get('annualGoals.exercicioReferencia')
            .patchValue(exerciseReference)
          this.formulario.get('annualGoals.vlrProjetadoRcl0').patchValue(0)
          this.formulario.get('annualGoals.vlrProjetadoRcl1').patchValue(0)
          this.formulario.get('annualGoals.vlrProjetadoRcl2').patchValue(0)
        }
      })

    setTimeout(() => {
      const colunasComConta = this.colunasTabela.filter(
        x => x.metaAnualLdoValorGrupo !== undefined,
      )
      this.colunasTabelaLength = this.colunasTabela.length
      this.subtotalcolunasTabela = 0
      this.colunasRegra = colunasComConta
      this.dadosDto = this.dtoDados
    }, 1000)
  }

  public onRowPrepared(event: any): void {
    const codigos = [
      'Receitas Primárias (I)',
      'Receitas Primárias Correntes',
      'Despesas Primárias (II)',
      'Despesas Primárias Correntes',
      'Resultado Primário (III) = (I - II)',
      'Resultado Nominal - (VI) = (III + (IV - V))',
      'Impacto do saldo das PPPs (IX) = (VII - VIII)',
    ]
    if (event.data) {
      if (codigos.filter(x => x === event.data.nome).length > 0) {
        event.rowElement.style.backgroundColor = '#cccccc'
      }
    }
  }

  public onCellPrepared(event: any): void {
    const codigos = [
      'Receitas Primárias Correntes',
      'Receitas Primárias de Capital',
      'Despesas Primárias Correntes',
      'Despesas Primárias de Capital',
      'Pagamento de Restos a Pagar de Despesas Primárias',
      'Juros, Encargos e Variações Monetárias Ativos (IV)',
      'Juros, Encargos e Variações Monetárias Passivos (V)',
    ]
    const codigosIndentados = [
      'Impostos, Taxas e Contribuições de Melhoria',
      'Contribuições',
      'Transferências Correntes',
      'Demais Receitas Primárias Correntes',
      'Pessoal e Encargos Sociais',
      'Outras Despesas Correntes',
    ]

    if (event.data) {
      if (
        event.data.metaAnualLdoValorGrupo &&
        codigos.filter(x => x === event.row.data.nome).length > 0
      ) {
        event.row.cells[0].cellElement.className = 'align-middle pl-4'
      }
      if (
        event.data.metaAnualLdoValorGrupo &&
        codigosIndentados.filter(x => x === event.row.data.nome).length > 0
      ) {
        event.row.cells[0].cellElement.className = 'align-middle pl-5'
      }
    }
  }

  public allowUpdating(event: any): void {
    const codigos = [
      'Receitas Primárias (I)',
      'Receitas Primárias Correntes',
      'Despesas Primárias (II)',
      'Despesas Primárias Correntes',
      'Resultado Primário (III) = (I - II)',
      'Resultado Nominal - (VI) = (III + (IV - V))',
      'Impacto do saldo das PPPs (IX) = (VII - VIII)',
    ]
    if (event.row.data) {
      if (codigos.filter(x => x === event.row.data.nome).length === 0) {
        return event.row.rowIndex + 1
      }
    }
  }

  private loadSelects(): void {
    this.riscoFiscalTceData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'meta_anual_ldo/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('annualGoals').patchValue(data.dados)
        this.formulario
          .get('annualGoals.flagPublicar')
          .patchValue(data.dados.flagPublicar === 'N' ? false : true)
        this.formulario
          .get('annualGoals.leiUuid')
          .patchValue(data.dados.lei.uuid)
        this.formulario
          .get('annualGoals.exercicioReferencia')
          .patchValue(data.dados.exercicioReferencia)
        this.loadSelects()
      })
  }

  public cancelar(retorno): void {
    if (!this.modal) {
      this.gravarParametros()
      this.router.navigate([`metas-anuais`])
    } else {
      this.ref.close(retorno)
    }
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.formulario.get('annualGoals.uuid').value)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Meta anual excluído com sucesso.',
              })
              this.cancelar(null)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      if (this.formulario.get('annualGoals.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getMetasAnuaisDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Meta anual criado com sucesso.',
        })
        this.formulario.get('annualGoals.flagPublicar').reset()
        this.formulario.get('annualGoals.fonte').reset()
        this.formulario.get('annualGoals.notaExplicativa').reset()
        this.fetchGrid()
      })
  }

  public openDecreeSearch(): void {
    const dialogRef = this.dialogService.open(
      DecreeSearchAnnualGoalsComponent,
      {
        context: {
          exercise: this.formulario.get('annualGoals.exercicioReferencia')
            .value,
        },
      },
    )

    dialogRef.onClose.subscribe(decree => {
      if (decree) {
        this.formulario
          .get('annualGoals.lei')
          .patchValue(
            `${decree.descricaoTipoDocumento} / ${decree.anoInicialAplicacao}`,
          )
        this.formulario.get('annualGoals.leiUuid').patchValue(decree.uuid)
      }
    })
  }

  private atualizar(): void {
    this.subscription = this.service
      .put(this.getMetasAnuaisDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Meta anual atualizado com sucesso.',
        })
        this.fetchGrid()
      })
  }

  private getMetasAnuaisDto(): any {
    const form = this.formulario.getRawValue()
    const data = form.annualGoals
    const dto = {
      uuid: data.uuid ?  data.uuid : null,
      exercicioReferencia: data.exercicioReferencia,
      leiUuid: data.leiUuid,
      publicacao: {
        ...data.publicacao,
        flagPublicar: data.publicacao?.flagPublicar ? data.publicacao?.flagPublicar : 'N',
        leiUuid: data.leiUuid,
      },
      vlrProjetadoRcl: 0,
    }

    return dto
  }

  public gravaReceitaCorrenteLiquidaProjetada(): void {
    const request = this.dadosDto

    if (request.exercicioReferenciaAno[0]) {
      this.subscription = this.service
        .putMetaAnualLdo(this.getMetasAnuaisDto0(request))
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          retorno => {
            this.toastr.send({
              success: true,
              message: 'Meta anual atualizado com sucesso.',
            })
          },
          () => {
            this.fetchGrid()
          },
        )
    }

    if (request.exercicioReferenciaAno[1]) {
      this.subscription = this.service
        .putMetaAnualLdo(this.getMetasAnuaisDto1(request))
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          retorno => {
            this.toastr.send({
              success: true,
              message: 'Meta anual atualizado com sucesso.',
            })
          },
          () => {
            this.fetchGrid()
          },
        )
    }

    if (request.exercicioReferenciaAno[2]) {
      this.subscription = this.service
        .putMetaAnualLdo(this.getMetasAnuaisDto2(request))
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          retorno => {
            this.toastr.send({
              success: true,
              message: 'Meta anual atualizado com sucesso.',
            })
          },
          () => {
            this.fetchGrid()
          },
        )
    }
  }

  private getMetasAnuaisDto0(request: any): any {
    const data = request

    const dto = {
      uuid: data.exercicioReferenciaAno[0].uuid,
      exercicioReferencia: data.exercicioReferenciaAno[0].exercicioReferencia,
      leiUuid: data.exercicioReferenciaAno[0].lei,
      publicacao: {
        flagPublicar: data.exercicioReferenciaAno[0].flagPublicar,
        fonte: data.exercicioReferenciaAno[0].fonte,
        leiUuid: data.exercicioReferenciaAno[0].lei.uuid,
        notaExplicativa: data.exercicioReferenciaAno[0].notaExplicativa,
      },
      vlrProjetadoRcl: this.formulario.get('annualGoals.vlrProjetadoRcl0')
        .value,
    }

    return dto
  }

  private getMetasAnuaisDto1(request: any): any {
    const data = request

    const dto = {
      uuid: data.exercicioReferenciaAno[1].uuid,
      exercicioReferencia: data.exercicioReferenciaAno[1].exercicioReferencia,
      leiUuid: data.exercicioReferenciaAno[1].lei,
      publicacao: {
        flagPublicar: data.exercicioReferenciaAno[1].flagPublicar,
        fonte: data.exercicioReferenciaAno[1].fonte,
        leiUuid: data.exercicioReferenciaAno[1].lei.uuid,
        notaExplicativa: data.exercicioReferenciaAno[1].notaExplicativa,
      },
      vlrProjetadoRcl: this.formulario.get('annualGoals.vlrProjetadoRcl1')
        .value,
    }

    return dto
  }

  private getMetasAnuaisDto2(request: any): any {
    const data = request

    const dto = {
      uuid: data.exercicioReferenciaAno[2].uuid,
      exercicioReferencia: data.exercicioReferenciaAno[2].exercicioReferencia,
      leiUuid: data.exercicioReferenciaAno[2].lei,
      publicacao: {
        flagPublicar: data.exercicioReferenciaAno[2].flagPublicar,
        fonte: data.exercicioReferenciaAno[2].fonte,
        leiUuid: data.exercicioReferenciaAno[2].lei.uuid,
        notaExplicativa: data.exercicioReferenciaAno[2].notaExplicativa,
      },
      vlrProjetadoRcl: this.formulario.get('annualGoals.vlrProjetadoRcl2')
        .value,
    }

    return dto
  }

  public onRowUpdated(event: any): void {
    this.fetchGrid()
  }

  public somaColunas(
    colunaRecebe: any,
    colunaAlterada: any,
    isPlus: boolean,
  ): void {
    const request = this.colunasTabela.filter(x => x.nome === colunaRecebe)[0]

    const dtoUp = this.getDtoAtualizar(request, colunaAlterada, isPlus)

    if (this.isVal) {
      this.subscription = this.service
        .putMetaAnualValor(dtoUp)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          () => {
            this.isVal = false
            this.fetchGrid()
          },
          (err: any) => {
            this.toastr.send({
              error: true,
              message:
                err.error?.causa?.descricao || err?.error?.causa?.message,
            })
            this.isVal = false
            this.fetchGrid()
          },
        )
      this.isVal = false
    }

    const dtoUp1 = this.getDtoAtualizar1(request, colunaAlterada, isPlus)

    if (this.isVal) {
      this.subscription = this.service
        .putMetaAnualValor(dtoUp1)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          () => {
            this.isVal = false
            this.fetchGrid()
          },
          (err: any) => {
            this.toastr.send({
              error: true,
              message:
                err.error?.causa?.descricao || err?.error?.causa?.message,
            })
            this.isVal = false
            this.fetchGrid()
          },
        )
      this.isVal = false
    }

    const dtoUp2 = this.getDtoAtualizar2(request, colunaAlterada, isPlus)

    if (this.isVal) {
      this.subscription = this.service
        .putMetaAnualValor(dtoUp2)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          () => {
            this.isVal = false
            this.fetchGrid()
          },
          (err: any) => {
            this.toastr.send({
              error: true,
              message:
                err.error?.causa?.descricao || err?.error?.causa?.message,
            })
            this.isVal = false
            this.fetchGrid()
          },
        )
    }
  }

  public onRowUpdating(event: any): void {
    const receitasCorrentes = [
      'Impostos, Taxas e Contribuições de Melhoria',
      'Contribuições',
      'Transferências Correntes',
      'Demais Receitas Primárias Correntes',
    ]
    const receitasPrimarias = [
      'Receitas Primárias Correntes',
      'Receitas Primárias de Capital',
    ]
    const despesaPrimaria = [
      'Despesas Primárias Correntes',
      'Despesas Primárias de Capital',
      'Pagamento de Restos a Pagar de Despesas Primárias',
    ]
    const despesaCorrente = [
      'Pessoal e Encargos Sociais',
      'Outras Despesas Correntes',
    ]
    const resultadoNominalIv = [
      'Juros, Encargos e Variações Monetárias Ativos (IV)',
    ]
    const resultadoNominalV = [
      'Juros, Encargos e Variações Monetárias Passivos (V)',
    ]
    const impactoSaldodPppVii = ['Receitas Primárias advindas de PPP (VII)']
    const impactoSaldodPppViii = ['Despesas Primárias geradas por PPP (VIII)']

    if (receitasCorrentes.includes(event.oldData.nome)) {
      this.somaColunas('Receitas Primárias Correntes', event, true)
      this.somaColunas('Receitas Primárias (I)', event, true)
      this.somaColunas('Resultado Primário (III) = (I - II)', event, true)
      this.somaColunas(
        'Resultado Nominal - (VI) = (III + (IV - V))',
        event,
        true,
      )
    }

    if (receitasPrimarias.includes(event.oldData.nome)) {
      this.somaColunas('Receitas Primárias (I)', event, true)
      this.somaColunas('Resultado Primário (III) = (I - II)', event, true)
      this.somaColunas(
        'Resultado Nominal - (VI) = (III + (IV - V))',
        event,
        true,
      )
    }

    if (despesaCorrente.includes(event.oldData.nome)) {
      this.somaColunas('Despesas Primárias Correntes', event, false)
      this.somaColunas('Despesas Primárias (II)', event, false)
      this.somaColunas('Resultado Primário (III) = (I - II)', event, false)
      this.somaColunas(
        'Resultado Nominal - (VI) = (III + (IV - V))',
        event,
        true,
      )
    }

    if (despesaPrimaria.includes(event.oldData.nome)) {
      this.somaColunas('Despesas Primárias (II)', event, false)
      this.somaColunas('Resultado Primário (III) = (I - II)', event, false)
      this.somaColunas(
        'Resultado Nominal - (VI) = (III + (IV - V))',
        event,
        true,
      )
    }

    if (resultadoNominalIv.includes(event.oldData.nome)) {
      this.somaColunas(
        'Resultado Nominal - (VI) = (III + (IV - V))',
        event,
        true,
      )
    }

    if (resultadoNominalV.includes(event.oldData.nome)) {
      this.somaColunas(
        'Resultado Nominal - (VI) = (III + (IV - V))',
        event,
        false,
      )
    }

    if (impactoSaldodPppVii.includes(event.oldData.nome)) {
      this.somaColunas(
        'Impacto do saldo das PPPs (IX) = (VII - VIII)',
        event,
        true,
      )
    }

    if (impactoSaldodPppViii.includes(event.oldData.nome)) {
      this.somaColunas(
        'Impacto do saldo das PPPs (IX) = (VII - VIII)',
        event,
        false,
      )
    }

    const dto = this.getDto(event)

    if (this.isVal2) {
      this.subscription = this.service
        .putMetaAnualValor(dto)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          () => {
            this.toastr.send({
              success: true,
              message: `${event.oldData.nome} atualizada com sucesso.`,
            })
            this.isVal2 = false
            this.fetchGrid()
          },
          (err: any) => {
            this.toastr.send({
              error: true,
              message:
                err.error?.causa?.descricao || err?.error?.causa?.message,
            })
            this.isVal2
            this.fetchGrid()
          },
        )
      this.isVal2 = false
    }

    const dto1 = this.getDto1(event)

    if (this.isVal2) {
      this.subscription = this.service
        .putMetaAnualValor(dto1)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          () => {
            this.toastr.send({
              success: true,
              message: `${event.oldData.nome} atualizada com sucesso.`,
            })
            this.isVal2 = false
            this.fetchGrid()
          },
          (err: any) => {
            this.toastr.send({
              error: true,
              message:
                err.error?.causa?.descricao || err?.error?.causa?.message,
            })
            this.isVal2 = false
            this.fetchGrid()
          },
        )
      this.isVal2 = false
    }

    const dto2 = this.getDto2(event)

    if (this.isVal2) {
      this.subscription = this.service
        .putMetaAnualValor(dto2)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          () => {
            this.toastr.send({
              success: true,
              message: `${event.oldData.nome} atualizada com sucesso.`,
            })
            this.isVal2 = false
            this.fetchGrid()
          },
          (err: any) => {
            this.toastr.send({
              error: true,
              message:
                err.error?.causa?.descricao || err?.error?.causa?.message,
            })
            this.isVal2 = false
            this.fetchGrid()
          },
        )
      this.isVal2 = false
    }
  }

  private getDto(event: any): Action {
    const { codigo, nome } = event.data || event.newData

    let index = 0

    const valUuid = event.oldData?.anosReferencia[index]
      ? event.oldData?.anosReferencia[index].uuid
      : 'notUuid'
    let percentualPibNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].percentualPib
    let vlrConstantebNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].vlrConstante
    let vlrCorrenteNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].vlrCorrente

    if (valUuid != 'notUuid') {
      this.isVal2 = true
      const dto = {
        uuid: event.oldData?.anosReferencia[index].uuid,
        codigo: codigo || event.oldData?.codigo,
        nome: nome || event.oldData?.nome,
        percentualPib:
          typeof percentualPibNew === 'number' ? percentualPibNew : null,
        vlrConstante:
          typeof vlrConstantebNew === 'number' ? vlrConstantebNew : null,
        vlrCorrente: typeof vlrCorrenteNew === 'number' ? vlrCorrenteNew : null,
      }
      return dto
    }
  }

  private getDto1(event: any): Action {
    const { codigo, nome } = event.data || event.newData

    let index = 1

    const valUuid = event.oldData?.anosReferencia[index]
      ? event.oldData?.anosReferencia[index].uuid
      : 'notUuid'
    let percentualPibNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].percentualPib
    let vlrConstantebNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].vlrConstante
    let vlrCorrenteNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].vlrCorrente

    if (valUuid != 'notUuid') {
      this.isVal2 = true
      const dto = {
        uuid: event.oldData?.anosReferencia[index].uuid,
        codigo: codigo || event.oldData?.codigo,
        nome: nome || event.oldData?.nome,
        percentualPib:
          typeof percentualPibNew === 'number' ? percentualPibNew : null,
        vlrConstante:
          typeof vlrConstantebNew === 'number' ? vlrConstantebNew : null,
        vlrCorrente: typeof vlrCorrenteNew === 'number' ? vlrCorrenteNew : null,
      }
      return dto
    }
  }

  private getDto2(event: any): Action {
    const { codigo, nome } = event.data || event.newData

    let index = 2

    const valUuid = event.oldData?.anosReferencia[index]
      ? event.oldData?.anosReferencia[index].uuid
      : 'notUuid'
    let percentualPibNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].percentualPib
    let vlrConstantebNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].vlrConstante
    let vlrCorrenteNew =
      event &&
      event.newData &&
      event.newData[`anosReferencia[${index}]`] &&
      event.newData[`anosReferencia[${index}]`].vlrCorrente

    if (valUuid != 'notUuid') {
      this.isVal2 = true
      const dto = {
        uuid: event.oldData?.anosReferencia[index].uuid,
        codigo: codigo || event.oldData?.codigo,
        nome: nome || event.oldData?.nome,
        percentualPib:
          typeof percentualPibNew === 'number' ? percentualPibNew : null,
        vlrConstante:
          typeof vlrConstantebNew === 'number' ? vlrConstantebNew : null,
        vlrCorrente: typeof vlrCorrenteNew === 'number' ? vlrCorrenteNew : null,
      }
      return dto
    }
  }

  private getDtoAtualizar(request: any, event: any, isPlus: boolean): Action {
    const { nome } = request

    let index = 0

    const current = request.anosReferencia[index]
    const old = event.oldData?.anosReferencia[index]
    const newData = event.newData[`anosReferencia[${index}]`]
    const isName =
      nome === 'Resultado Primário (III) = (I - II)' ||
      nome === 'Resultado Nominal - (VI) = (III + (IV - V))' ||
      nome === 'Impacto do saldo das PPPs (IX) = (VII - VIII)'

    if (current != undefined) {
      this.isVal = true
      const dto = {
        uuid: current.uuid,
        nome: nome,
        vlrCorrente: null,
        vlrConstante: null,
        percentualPib: null,
      }

      const val =
        newData && typeof newData.vlrCorrente === 'number' ? true : false
      const val2 =
        newData && typeof newData.vlrConstante === 'number' ? true : false
      const val3 =
        newData && typeof newData.percentualPib === 'number' ? true : false

      if (isName && !isPlus) {
        if (val) {
          dto.vlrCorrente =
            current.vlrCorrente +
            old.vlrCorrente -
            ((newData && newData.vlrCorrente) || 0)
        }

        if (val2) {
          dto.vlrConstante =
            current.vlrConstante +
            old.vlrConstante -
            ((newData && newData.vlrConstante) || 0)
        }

        if (val3) {
          dto.percentualPib =
            current.percentualPib +
            old.percentualPib -
            ((newData && newData.percentualPib) || 0)
        }
      } else {
        if (val) {
          dto.vlrCorrente =
            current.vlrCorrente -
            old.vlrCorrente +
            ((newData && newData.vlrCorrente) || 0)
        }

        if (val2) {
          dto.vlrConstante =
            current.vlrConstante -
            old.vlrConstante +
            ((newData && newData.vlrConstante) || 0)
        }

        if (val3) {
          dto.percentualPib =
            current.percentualPib -
            old.percentualPib +
            ((newData && newData.percentualPib) || 0)
        }
      }
      return dto
    }
  }

  private getDtoAtualizar1(request: any, event: any, isPlus: boolean): Action {
    const { nome } = request

    let index = 1

    const current = request.anosReferencia[index]
    const old = event.oldData?.anosReferencia[index]
    const newData = event.newData[`anosReferencia[${index}]`]
    const isName =
      nome === 'Resultado Primário (III) = (I - II)' ||
      nome === 'Resultado Nominal - (VI) = (III + (IV - V))' ||
      nome === 'Impacto do saldo das PPPs (IX) = (VII - VIII)'

    if (current != undefined) {
      this.isVal = true
      const dto = {
        uuid: current.uuid,
        nome: nome,
        vlrCorrente: null,
        vlrConstante: null,
        percentualPib: null,
      }

      const val =
        newData && typeof newData.vlrCorrente === 'number' ? true : false
      const val2 =
        newData && typeof newData.vlrConstante === 'number' ? true : false
      const val3 =
        newData && typeof newData.percentualPib === 'number' ? true : false

      if (isName && !isPlus) {
        if (val) {
          dto.vlrCorrente =
            current.vlrCorrente +
            old.vlrCorrente -
            ((newData && newData.vlrCorrente) || 0)
        }

        if (val2) {
          dto.vlrConstante =
            current.vlrConstante +
            old.vlrConstante -
            ((newData && newData.vlrConstante) || 0)
        }

        if (val3) {
          dto.percentualPib =
            current.percentualPib +
            old.percentualPib -
            ((newData && newData.percentualPib) || 0)
        }
      } else {
        if (val) {
          dto.vlrCorrente =
            current.vlrCorrente -
            old.vlrCorrente +
            ((newData && newData.vlrCorrente) || 0)
        }

        if (val2) {
          dto.vlrConstante =
            current.vlrConstante -
            old.vlrConstante +
            ((newData && newData.vlrConstante) || 0)
        }

        if (val3) {
          dto.percentualPib =
            current.percentualPib -
            old.percentualPib +
            ((newData && newData.percentualPib) || 0)
        }
      }
      return dto
    }
  }

  private getDtoAtualizar2(request: any, event: any, isPlus: boolean): Action {
    const { nome } = request

    let index = 2

    const current = request.anosReferencia[index]
    const old = event.oldData?.anosReferencia[index]
    const newData = event.newData[`anosReferencia[${index}]`]
    const isName =
      nome === 'Resultado Primário (III) = (I - II)' ||
      nome === 'Resultado Nominal - (VI) = (III + (IV - V))' ||
      nome === 'Impacto do saldo das PPPs (IX) = (VII - VIII)'

    if (current != undefined) {
      this.isVal = true
      const dto = {
        uuid: current.uuid,
        nome: nome,
        vlrCorrente: null,
        vlrConstante: null,
        percentualPib: null,
      }

      const val =
        newData && typeof newData.vlrCorrente === 'number' ? true : false
      const val2 =
        newData && typeof newData.vlrConstante === 'number' ? true : false
      const val3 =
        newData && typeof newData.percentualPib === 'number' ? true : false

      if (isName && !isPlus) {
        if (val) {
          dto.vlrCorrente =
            current.vlrCorrente +
            old.vlrCorrente -
            ((newData && newData.vlrCorrente) || 0)
        }

        if (val2) {
          dto.vlrConstante =
            current.vlrConstante +
            old.vlrConstante -
            ((newData && newData.vlrConstante) || 0)
        }

        if (val3) {
          dto.percentualPib =
            current.percentualPib +
            old.percentualPib -
            ((newData && newData.percentualPib) || 0)
        }
      } else {
        if (val) {
          dto.vlrCorrente =
            current.vlrCorrente -
            old.vlrCorrente +
            ((newData && newData.vlrCorrente) || 0)
        }

        if (val2) {
          dto.vlrConstante =
            current.vlrConstante -
            old.vlrConstante +
            ((newData && newData.vlrConstante) || 0)
        }

        if (val3) {
          dto.percentualPib =
            current.percentualPib -
            old.percentualPib +
            ((newData && newData.percentualPib) || 0)
        }
      }
      return dto
    }
  }

  public abrirEditorDocumentoFonte(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.formulario.get('annualGoals.fonte').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.formulario.get('annualGoals.fonte').patchValue(retorno)
    })
  }

  public abrirEditorDocumentoNotaExplicativa(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.formulario.get('annualGoals.notaExplicativa').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.formulario.get('annualGoals.notaExplicativa').patchValue(retorno)
    })
  }
}
