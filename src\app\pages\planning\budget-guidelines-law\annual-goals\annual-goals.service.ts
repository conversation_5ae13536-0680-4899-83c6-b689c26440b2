import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'

import { BaseService } from '../../../../@common/services/base/base.service'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class AnnualGoalsService extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'meta_anual_ldo')
  }

  public getMetaAnualValor(): Observable<any> {
    return this.http.get<any>(`meta_anual_ldo_valor/agrupado`, { params: { take: '30' } })
  }

  public putMetaAnualValor(data: any): Observable<any> {
    return this.http.put<any>(`meta_anual_ldo_valor/${data.uuid}`, data)
  }

  public putMetaAnualLdo(data: any): Observable<any> {
    return this.http.put<any>(`meta_anual_ldo/${data.uuid}`, data)
  }

  public getMetaAnualLdoExercicioReferencia(): Observable<any> {
    const headers = new HttpHeaders();

    return this.http.get<any>(`meta_anual_ldo/exercicio_referencia`, {
      headers,
    })
  }
}
