<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update-assessment-fiscal-goals-form'"
  [topRightButtonTitle]="'Atualizar'"
  [rightApproveButtonVisible]="true"
  [rightApproveButtonIconVisible]="true"
  [rightApproveButtonIcon]="'fas fa-check'"
  (rightApproveButtonEmitter)="confirm()"
  [rightApproveButtonDisabled]="model.invalid || model.pristine"
  [bottomLeftButtonType]="'danger'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonIcon]="'fas fa-trash'"
  [bottomLeftButtonText]="'Excluir'"
  (bottomLeftButtonEmitter)="remover()"
  [formGroup]="model"
>
  <div class="row">
    <div class="col col-12 col-md-2">
      <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="exercicioDeReferencia"
        name="ExercicioReferencia"
        placeholder=""
        label="Exercício de referência"
        readonly="true"
      >
      </eqp-nebular-input>
    </div>

    <div class="col col-12 col-md-6 mt-4">
      <eqp-publication-note-field
        formControlName="publicacao"
      ></eqp-publication-note-field>
    </div>
  </div>

  <div class="table-container mt-4">
    <table
      class="dx-datagrid-table dx-datagrid-table-fixed"
      role="presentation"
    >
      <tbody>
        <tr>
          <th></th>
          <th>Previsto</th>
          <th>%PIB</th>
          <th>Realizado</th>
          <th>%PIB</th>
        </tr>

        <tr>
          <td>Receita total</td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrRecTotalPrevista"
              name="recTotalPrevista"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrRecTotalPrevistaPib"
              name="recTotalPrevistaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrRecTotalRealizada"
              name="recTotalRealizada"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrRecTotalRealizadaPib"
              name="recTotalRealizadaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>

        <tr>
          <td>Receitas Primárias(I)</td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrRecPrimariaPrevista"
              name="recPrimariaPrevista"
              placeholder="R$00,00"
              required="true"
              label=""
              id="value-1"
            >
            </eqp-nebular-input>
          </td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrRecPrimariaPrevistaPib"
              name="recPrimariaPrevista"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrRecPrimariaRealizada"
              name="recPrimariaRealizada"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrRecPrimariaRealizadaPib"
              name="recPrimariaRealizadaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>

        <tr>
          <td>Despesa Total</td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDespTotalPrevista"
              name="despTotalPrevista"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDespTotalPrevistaPib"
              name="despTotalPrevistaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDespTotalRealizada"
              name="despTotalRealizada"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDespTotalRealizadaPib"
              name="despTotalRealizadaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>

        <tr>
          <td>Despesas Primárias(II)</td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDespPrimariaPrevista"
              name="despPrimariaPrevista"
              placeholder="R$00,00"
              required="true"
              label=""
              id="value-2"
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDespPrimariaPrevistaPib"
              name="despPrimariaPrevistaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDespPrimariaRealizada"
              name="despPrimariaRealizada"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDespPrimariaRealizadaPib"
              name="despPrimariaRealizadaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>

        <tr>
          <td>Resultado Primário(I-II)</td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrResultPrimarioPrevisto"
              name="resultPrimarioPrevisto"
              placeholder=""
              [disabled]="true"
              [value]="resultadoPrevisto"
              label=""
              id="resultado"
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrResultPrimarioPrevistoPib"
              name="resultPrimarioPrevistoPib"
              placeholder=""
              [value]="resultadoPrevistoPib"
              [disabled]="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrResultPrimarioRealizado"
              name="resultPrimarioRealizado"
              placeholder=""
              [value]="resultadoRealizado"
              [disabled]="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrResultPrimarioRealizadoPib"
              name="resultPrimarioRealizadoPib"
              placeholder=""
              [value]="resultadoRealizadoPib"
              [disabled]="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>

        <tr>
          <td>Resultado Nominal</td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrResultNominalPrevisto"
              name="resultNominalPrevisto"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrResultNominalPrevistoPib"
              name="resultNominalPrevistoPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrResultNominalRealizado"
              name="resultNominalRealizado"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrResultNominalRealizadoPib"
              name="resultNominalRealizadoPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>
        <tr>
          <td>Dívida Pública Consolidada</td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDivPubConsPrevista"
              name="divPubConsPrevista"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDivPubConsPrevistaPib"
              name="divPubConsPrevistaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDivPubConsRealizada"
              name="dvPubConsRealizada"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDivPubConsRealizadaPib"
              name="dvPubConsRealizadaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>
        <tr>
          <td>Dívida Consolidada Líquida</td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDivConsLiqPrevista"
              name="divConsLiqPrevista"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDivConsLiqPrevistaPib"
              name="divConsLiqPrevistaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrDivConsLiqRealizada"
              name="divConsLiqRealizada"
              placeholder="R$00,00"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>

          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{
                prefix: '',
                suffix: '%',
                thousands: '.',
                decimal: ',',
                precision: 3
              }"
              [size]="'small'"
              [shape]="'rectangle'"
              maxlength="6"
              formControlName="vlrDivConsLiqRealizadaPib"
              name="divConsLiqRealizadaPib"
              placeholder="00,000%"
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>

        <tr>
          <td>Receita Corrente Líquida Projetada</td>
          <td>
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="vlrReceitaCorrenteLiquida"
              name="receitaCorrenteLiquida"
              placeholder=""
              required="true"
              label=""
            >
            </eqp-nebular-input>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</eqp-standard-page>
