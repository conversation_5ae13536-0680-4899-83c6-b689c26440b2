import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormControl, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataInterface } from '@guards/services/user-data'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { finalize, first, take } from 'rxjs/operators'
import { AssessmentFiscalGoalsPreviousExerciseInterface } from '../interfaces/assessment-fiscal-goals-interface'
import { AssessmentFiscalGoalsService } from '../services/assessment-fiscal-goals.service'

@Component({
  selector: 'eqp-assessment-fiscal-goals-form',
  templateUrl: './assessment-fiscal-goals-form.component.html',
  styleUrls: ['./assessment-fiscal-goals-form.component.scss'],
})
export class AssessmentFiscalGoalsFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle =
    'Avaliação do cumprimento das metas fiscais do exercício anterior'
  public userData!: UserDataInterface

  constructor(
    private formBuilder: FormBuilder,
    public menuService: MenuService,
    public router: Router,
    public service: AssessmentFiscalGoalsService,
    private userService: UserDataService,
    private toastrService: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/avaliacao-metas-fiscais-exercicio-anterior',
    )
  }

  enableEditing = false
  togglePublication = new FormControl(false)

  calcRecPrimariaPrevista: number = 0
  calcRecPrimariaPrevistaPib: number = 0
  calcRecPrimariaRealizada: number = 0
  calcRecPrimariaRealizadaPib: number = 0

  calcDespPrimariaPrevista: number = 0
  calcDespPrimariaPrevistaPib: number = 0
  calcDespPrimariaRealizada: number = 0
  calcDespPrimariaRealizadaPib: number = 0

  resultadoPrevisto: number = 0
  resultadoPrevistoPib: number = 0
  resultadoRealizado: number = 0
  resultadoRealizadoPib: number = 0

  calcValor: number = 0

  changeModelSub: Subscription
  changeTogglePublicationSub: Subscription

  ngOnInit(): void {
    this.userData = this.userService.userData
    this.obterExercicio()
    this.model = this.getNovoFormulario()
    this.loadPageData()
    this.loadCalHandlers()
  }

  getResultadoPrevisto(formData) {
    const { vlrRecPrimariaPrevista, vlrDespPrimariaPrevista } = formData
    const calc =
      Number(vlrRecPrimariaPrevista) - Number(vlrDespPrimariaPrevista)

    return calc
  }

  getResultadoPrevistoPib(formData) {
    const { vlrRecPrimariaPrevistaPib, vlrDespPrimariaPrevistaPib } = formData
    const calc =
      Number(vlrRecPrimariaPrevistaPib) - Number(vlrDespPrimariaPrevistaPib)

    return calc
  }

  getResultadoRealizado(formData) {
    const { vlrDespPrimariaRealizada, vlrRecPrimariaRealizada } = formData
    const calc =
      Number(vlrRecPrimariaRealizada) - Number(vlrDespPrimariaRealizada)

    return calc
  }

  getResultadoRealizadoPib(formData) {
    const { vlrRecPrimariaRealizadaPib, vlrDespPrimariaRealizadaPib } = formData
    const calc =
      Number(vlrRecPrimariaRealizadaPib) - Number(vlrDespPrimariaRealizadaPib)

    return calc
  }

  private loadCalHandlers() {
    this.changeModelSub = this.model.valueChanges.subscribe(val => {
      this.resultadoPrevisto = this.getResultadoPrevisto(val)
      this.resultadoRealizado = this.getResultadoRealizado(val)
      this.resultadoPrevistoPib = this.getResultadoPrevistoPib(val)
      this.resultadoRealizadoPib = this.getResultadoRealizadoPib(val)
    })
  }

  ngOnDestroy(): void {
    if (this.changeModelSub) this.changeModelSub.unsubscribe()
  }

  public loadPageData() {
    this.loading = true
    this.service
      .get()
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        if (res.dados.length > 0) {
          this.loadForm(res.dados)
        } else {
          this.create()
        }
      })
  }

  private prepare(): any {
    let data = this.model.getRawValue()
    return data
  }

  update(): void {
    this.loading = true
    this.service
      .put(this.prepare())
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(_ => {
        this.toastrService.send({
          success: true,
          message: 'Meta fiscal atualizado(a) com sucesso.',
        })
      })
  }

  loadForm(data: AssessmentFiscalGoalsPreviousExerciseInterface[]) {
    const [fiscalGoals] = data

    this.model.patchValue(fiscalGoals)

    const publishInternet = fiscalGoals.publicacao.flagPublicar === 'S'

    this.togglePublication.patchValue(publishInternet)
    this.enableEditing = true
  }

  get uuid() {
    return this.model.get('uuid')
  }

  public obterExercicio(): void {
    this.exercicio.exercicioReferencia =
      Number(`${this.userData.exercicio}`) - 2
  }

  public exercicio: AssessmentFiscalGoalsPreviousExerciseInterface =
    {} as AssessmentFiscalGoalsPreviousExerciseInterface

  public getNovoFormulario() {
    return this.formBuilder.group({
      uuid: [],
      exercicioDeReferencia: [
        this.exercicio.exercicioReferencia,
        Validators.required,
      ],
      vlrRecTotalPrevista: [0, Validators.required],
      vlrRecTotalPrevistaPib: [0, Validators.required],
      vlrRecTotalRealizada: [0, Validators.required],
      vlrRecTotalRealizadaPib: [0, Validators.required],
      vlrRecPrimariaPrevista: [0, Validators.required],
      vlrRecPrimariaPrevistaPib: [0, Validators.required],
      vlrRecPrimariaRealizada: [0, Validators.required],
      vlrRecPrimariaRealizadaPib: [0, Validators.required],
      vlrDespTotalPrevista: [0, Validators.required],
      vlrDespTotalPrevistaPib: [0, Validators.required],
      vlrDespTotalRealizada: [0, Validators.required],
      vlrDespTotalRealizadaPib: [0, Validators.required],
      vlrDespPrimariaPrevista: [0, Validators.required],
      vlrDespPrimariaPrevistaPib: [0, Validators.required],
      vlrDespPrimariaRealizada: [0, Validators.required],
      vlrDespPrimariaRealizadaPib: [0, Validators.required],
      vlrResultPrimarioPrevisto: [0, Validators.required],
      vlrResultPrimarioPrevistoPib: [0, Validators.required],
      vlrResultPrimarioRealizado: [0, Validators.required],
      vlrResultPrimarioRealizadoPib: [0, Validators.required],
      vlrResultNominalPrevisto: [0, Validators.required],
      vlrResultNominalPrevistoPib: [0, Validators.required],
      vlrResultNominalRealizado: [0, Validators.required],
      vlrResultNominalRealizadoPib: [0, Validators.required],
      vlrDivPubConsPrevista: [0, Validators.required],
      vlrDivPubConsPrevistaPib: [0, Validators.required],
      vlrDivPubConsRealizada: [0, Validators.required],
      vlrDivPubConsRealizadaPib: [0, Validators.required],
      vlrDivConsLiqPrevista: [0, Validators.required],
      vlrDivConsLiqPrevistaPib: [0, Validators.required],
      vlrDivConsLiqRealizada: [0, Validators.required],
      vlrDivConsLiqRealizadaPib: [0, Validators.required],
      vlrReceitaCorrenteLiquida: [0, Validators.required],
      publicacao: [],
    })
  }

  private create(): void {
    this.loading = true
    this.service
      .post({
        exercicioReferencia: 0,
        vlrRecTotalPrevista: 0,
        vlrRecTotalPrevistaPib: 0,
        vlrRecTotalRealizada: 0,
        vlrRecTotalRealizadaPib: 0,
        vlrRecPrimariaPrevista: 0,
        vlrRecPrimariaPrevistaPib: 0,
        vlrRecPrimariaRealizada: 0,
        vlrRecPrimariaRealizadaPib: 0,
        vlrDespTotalPrevista: 0,
        vlrDespTotalPrevistaPib: 0,
        vlrDespTotalRealizada: 0,
        vlrDespTotalRealizadaPib: 0,
        vlrDespPrimariaPrevista: 0,
        vlrDespPrimariaPrevistaPib: 0,
        vlrDespPrimariaRealizada: 0,
        vlrDespPrimariaRealizadaPib: 0,
        vlrResultNominalPrevisto: 0,
        vlrResultNominalPrevistoPib: 0,
        vlrResultNominalRealizado: 0,
        vlrResultNominalRealizadoPib: 0,
        vlrDivPubConsPrevista: 0,
        vlrDivPubConsPrevistaPib: 0,
        vlrDivPubConsRealizada: 0,
        vlrDivPubConsRealizadaPib: 0,
        vlrDivConsLiqPrevista: 0,
        vlrDivConsLiqPrevistaPib: 0,
        vlrDivConsLiqRealizada: 0,
        vlrDivConsLiqRealizadaPib: 0,
        vlrReceitaCorrenteLiquida: 0,
        flagPublicar: 'n',
      })
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.model.get('uuid').setValue(res.body.dados.uuid)
        this.toastrService.send({
          success: true,
          message: 'Nova meta fiscal criada por cadastro automático.',
        })
      })
  }

  public confirm() {
    this.update()
  }

  public remover() {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.uuid.value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastrService.send({
                success: true,
                message: 'Meta fiscal excluída com sucesso.',
              })
            },
            (resp: any) => this.toastrService.bulkSend(resp.mensagens),
          )
        this.router
          .navigateByUrl('/', { skipLocationChange: true })
          .then(() =>
            this.router.navigate([
              'lei-diretrizes-orcamentarias/avaliacao-metas-fiscais-exercicio-anterior',
            ]),
          )
      }
    })
  }
}
