import { PublicationNoteInterface } from "@pages/planning/shared/interfaces/publication-note";

export interface AssessmentFiscalGoalsPreviousExerciseInterface{
    uuid: string;
    exercicioReferencia: number;
    vlrRecTotalPrevista: number;
    vlrRecTotalPrevistaPib: number;
    vlrRecTotalRealizada: number;
    vlrRecTotalRealizadaPib: number;
    vlrRecPrimariaPrevista: number;
    vlrRecPrimariaPrevistaPib: number;
    vlrRecPrimariaRealizada: number;
    vlrRecPrimariaRealizadaPib: number;
    vlrDespTotalPrevista: number;
    vlrDespTotalPrevistaPib: number;
    vlrDespTotalRealizada: number;
    vlrDespTotalRealizadaPib: number;
    vlrDespPrimariaPrevista: number;
    vlrDespPrimariaPrevistaPib: number;
    vlrDespPrimariaRealizada: number;
    vlrDespPrimariaRealizadaPib: number;
    vlrResultNominalPrevisto: number;
    vlrResultNominalPrevistoPib: number;
    vlrResultNominalRealizado: number;
    vlrResultNominalRealizadoPib: number;
    vlrDivPubConsPrevista: number;
    vlrDivPubConsPrevistaPib: number;
    vlrDivPubConsRealizada: number;
    vlrDivPubConsRealizadaPib: number;
    vlrDivConsLiqPrevista: number;
    vlrDivConsLiqPrevistaPib: number;
    vlrDivConsLiqRealizada: number;
    vlrDivConsLiqRealizadaPib: number;
    vlrReceitaCorrenteLiquida: number;
    flagPublicar: string;
    publicacao?: PublicationNoteInterface;
  }

