import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { IPagedList } from '@design-tools/wrapped-dx-grid/models';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { Observable } from 'rxjs';
import { AssessmentFiscalGoalsPreviousExerciseInterface } from '../interfaces/assessment-fiscal-goals-interface';

@Injectable({
  providedIn: 'root'
})
export class AssessmentFiscalGoalsService extends BaseService<
	ResponseDto<AssessmentFiscalGoalsPreviousExerciseInterface[]>,
	AssessmentFiscalGoalsPreviousExerciseInterface
> {

  constructor(protected http: HttpClient) {
		super(http, 'avaliacao_metas_fiscais_exercicio_anterior')
	}
}
