import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { ActuarialProjectionListComponent } from './actuarial-projection/actuarial-projection-list/actuarial-projection-list.component'
import { AlienationResourceAppOriginFormComponent } from './alienation-resource-app-origin/alienation-resource-app-origin-form/alienation-resource-app-origin-form.component'
import { AssessmentFiscalGoalsFormComponent } from './assessment-fiscal-goals/assessment-fiscal-goals-form/assessment-fiscal-goals-form.component'
import { BudgetGuidelinesLawComponent } from './budget-guidelines-law.component'
import { ContinuedExpenseExpansionEditComponent } from './continued-expense-expansion/continued-expense-expansion-edit/continued-expense-expansion-edit.component'
import { ContinuedExpenseExpansionListComponent } from './continued-expense-expansion/continued-expense-expansion-list/continued-expense-expansion-list.component'
import { CurrentGoalsComparedPreviousExerciseListComponent } from './current-goals-previous-exercise/current-goals-compared-previous-exercise-list/current-goals-compared-previous-exercise-list.component'
import { EstimationCompensationRevenueWaiverFormComponent } from './estimation-compensation-revenue-waiver/estimation-compensation-revenue-waiver-form/estimation-compensation-revenue-waiver-form.component'
import { EstimationCompensationRevenueWaiverListComponent } from './estimation-compensation-revenue-waiver/estimation-compensation-revenue-waiver-list/estimation-compensation-revenue-waiver-list.component'
import { EvolutionOfEquityFormComponent } from './evolution-of-equity/evolution-of-equity-form/evolution-of-equity-form.component'
import { EvolutionOfEquityListComponent } from './evolution-of-equity/evolution-of-equity-list/evolution-of-equity-list.component'
import { PrevRevenueExpenseRppsFormComponent } from './prev-revenue-expense-rpps/prev-revenue-expense-rpps-form/prev-revenue-expense-rpps-form.component'
import { ProjectInProgressFormComponent } from './project-in-progress/project-in-progress-form/project-in-progress-form.component'
import { ProjectInProgressListComponent } from './project-in-progress/project-in-progress-list/project-in-progress-list.component'
import { RevenueEvolutionLdoEditComponent } from './revenue-evolution-ldo/revenue-evolution-ldo-edit/revenue-evolution-ldo-edit.component'
import { RevenueEvolutionLdoListComponent } from './revenue-evolution-ldo/revenue-evolution-ldo-list/revenue-evolution-ldo-list.component'

const routes: Routes = [
  {
    path: '',
    component: BudgetGuidelinesLawComponent,
    children: [
      {
        path: 'evolucao-patrimonio-liquido',
        component: EvolutionOfEquityListComponent,
      },
      {
        path: 'evolucao-patrimonio-liquido/novo',
        component: EvolutionOfEquityFormComponent,
      },
      {
        path: 'evolucao-patrimonio-liquido/edit/:uuid',
        component: EvolutionOfEquityFormComponent,
      },
      {
        path: 'metas-atuais-comparadas-exercicios-anteriores',
        component: CurrentGoalsComparedPreviousExerciseListComponent,
      },
      {
        path: 'origem-aplicacao-recurso-alienacao-bem',
        component: AlienationResourceAppOriginFormComponent,
      },
      {
        path: 'projetos-em-andamento',
        component: ProjectInProgressListComponent,
      },
      {
        path: 'projetos-em-andamento/novo',
        component: ProjectInProgressFormComponent,
      },
      {
        path: 'projetos-em-andamento/edit/:uuid',
        component: ProjectInProgressFormComponent,
      },
      {
        path: 'avaliacao-metas-fiscais-exercicio-anterior',
        component: AssessmentFiscalGoalsFormComponent,
      },
      {
        path: 'receitas-despesas-previdenciarias-rpps',
        component: PrevRevenueExpenseRppsFormComponent,
      },
      {
        path: 'estimativa-compensacao-renuncia-receita',
        component: EstimationCompensationRevenueWaiverListComponent,
      },
      {
        path: 'estimativa-compensacao-renuncia-receita/novo',
        component: EstimationCompensationRevenueWaiverFormComponent,
      },
      {
        path: 'estimativa-compensacao-renuncia-receita/edit/:uuid',
        component: EstimationCompensationRevenueWaiverFormComponent,
      },
      {
        path: 'margem-expansao-despesas-obrigatorias',
        component: ContinuedExpenseExpansionListComponent,
      },
      {
        path: 'margem-expansao-despesas-obrigatorias/edit/:uuid',
        component: ContinuedExpenseExpansionEditComponent,
      },
      {
        path: 'receita/evolucao',
        component: RevenueEvolutionLdoListComponent
      },
      {
        path: 'receita/evolucao/edit/:uuid',
        component: RevenueEvolutionLdoEditComponent
      },
      {
        path: 'projecao-atuarial',
        component: ActuarialProjectionListComponent,
      },

    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BudgetGuidelinesLawRoutingModule {}
