import { PublicationNoteFieldModule } from './../../shared/components/publication-note-field/publication-note-field.module';
import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { ActuarialProjectionListComponent } from './actuarial-projection/actuarial-projection-list/actuarial-projection-list.component'
import { ActurialProjectionFormComponent } from './actuarial-projection/acturial-projection-form/acturial-projection-form.component'
import { AlienationResourceAppOriginFormComponent } from './alienation-resource-app-origin/alienation-resource-app-origin-form/alienation-resource-app-origin-form.component'
import { AssessmentFiscalGoalsFormComponent } from './assessment-fiscal-goals/assessment-fiscal-goals-form/assessment-fiscal-goals-form.component'
import { BudgetGuidelinesLawRoutingModule } from './budget-guidelines-law-routing.module'
import { BudgetGuidelinesLawComponent } from './budget-guidelines-law.component'
import { ContinuedExpenseExpansionEditComponent } from './continued-expense-expansion/continued-expense-expansion-edit/continued-expense-expansion-edit.component'
import { ContinuedExpenseExpansionListComponent } from './continued-expense-expansion/continued-expense-expansion-list/continued-expense-expansion-list.component'
import { ContinuedExpenseExpansionSearchComponent } from './continued-expense-expansion/continued-expense-expansion-search/continued-expense-expansion-search.component'
import { CurrentGoalsComparedPreviousExerciseEditComponent } from './current-goals-previous-exercise/current-goals-compared-previous-exercise-edit/current-goals-compared-previous-exercise-edit.component'
import { CurrentGoalsComparedPreviousExerciseListComponent } from './current-goals-previous-exercise/current-goals-compared-previous-exercise-list/current-goals-compared-previous-exercise-list.component'
import { EstimationCompensationRevenueWaiverFormComponent } from './estimation-compensation-revenue-waiver/estimation-compensation-revenue-waiver-form/estimation-compensation-revenue-waiver-form.component'
import { EstimationCompensationRevenueWaiverListComponent } from './estimation-compensation-revenue-waiver/estimation-compensation-revenue-waiver-list/estimation-compensation-revenue-waiver-list.component'
import { EvolutionOfEquityFormComponent } from './evolution-of-equity/evolution-of-equity-form/evolution-of-equity-form.component'
import { EvolutionOfEquityListComponent } from './evolution-of-equity/evolution-of-equity-list/evolution-of-equity-list.component'
import { PrevRevenueExpenseRppsFormComponent } from './prev-revenue-expense-rpps/prev-revenue-expense-rpps-form/prev-revenue-expense-rpps-form.component'
import { ProjectInProgressFormComponent } from './project-in-progress/project-in-progress-form/project-in-progress-form.component'
import { ProjectInProgressListComponent } from './project-in-progress/project-in-progress-list/project-in-progress-list.component';
import { RevenueEvolutionLdoEditComponent } from './revenue-evolution-ldo/revenue-evolution-ldo-edit/revenue-evolution-ldo-edit.component';
import { RevenueEvolutionLdoListComponent } from './revenue-evolution-ldo/revenue-evolution-ldo-list/revenue-evolution-ldo-list.component'
import { RevenueEvolutionLdoSearchComponent } from './revenue-evolution-ldo/revenue-evolution-ldo-search/revenue-evolution-ldo-search.component';
import { CreditTypeDialogComponent } from './estimation-compensation-revenue-waiver/types-dialogs/components/credit-type-dialog/credit-type-dialog.component'
import { CommonToolsModule } from '@common/common-tools.module';
import { WaiverTypeDialogComponent } from './estimation-compensation-revenue-waiver/types-dialogs/components/waiver-type-dialog/waiver-type-dialog.component'

@NgModule({
  declarations: [
    BudgetGuidelinesLawComponent,
    EvolutionOfEquityListComponent,
    EvolutionOfEquityFormComponent,
    CurrentGoalsComparedPreviousExerciseListComponent,
    CurrentGoalsComparedPreviousExerciseEditComponent,
    AlienationResourceAppOriginFormComponent,
    ProjectInProgressListComponent,
    ProjectInProgressFormComponent,
    AssessmentFiscalGoalsFormComponent,
    PrevRevenueExpenseRppsFormComponent,
    EstimationCompensationRevenueWaiverFormComponent,
    EstimationCompensationRevenueWaiverListComponent,
    ContinuedExpenseExpansionListComponent,
    ContinuedExpenseExpansionEditComponent,
    ContinuedExpenseExpansionSearchComponent,
    RevenueEvolutionLdoListComponent,
    RevenueEvolutionLdoEditComponent,
    RevenueEvolutionLdoSearchComponent,
    ActuarialProjectionListComponent,
    ActurialProjectionFormComponent,
    CreditTypeDialogComponent,
    WaiverTypeDialogComponent,
  ],
  imports: [CommonModule, BudgetGuidelinesLawRoutingModule, CommonToolsModule, PublicationNoteFieldModule],
  exports: [BudgetGuidelinesLawRoutingModule],
})
export class BudgetGuidelinesLawModule {}
