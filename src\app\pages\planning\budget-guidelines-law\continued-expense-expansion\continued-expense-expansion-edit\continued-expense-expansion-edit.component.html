<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="codigo"
          name="codigoLei"
          label="Lei/ato"
          [readonly]="true"
        >
        </eqp-nebular-input>
      </div>

      <div class="col col-12 col-md-3">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="descricaoTipoDocumento"
          name="descricaoTipoDocumentoSelecionado"
          label="Tipo do documento"
          [readonly]="true"
        >
        </eqp-nebular-input>
      </div>

      <div class="col col-12 col-md-3">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="escopoDocumentoTce"
          name="escopoDocumento"
          label="Escopo do documento"
          [readonly]="true"
        >
        </eqp-nebular-input>
      </div>

      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="codigoControleDocumento"
          name="codigoControleDocumento"
          label="Número do documento"
          [readonly]="true"
        >
        </eqp-nebular-input>
      </div>

      <div class="col col-12 col-md-2">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="ano"
          name="anoDocumento"
          label="Ano do documento"
          [readonly]="true"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row">
      <div class="col col-12 col-md-6 mt-4">
        <eqp-publication-note-field
          formControlName="publicacao"
        ></eqp-publication-note-field>
      </div>
    </div>

    <div class="table-container espacamento mt-4">
      <table
        class="dx-datagrid-table dx-datagrid-table-fixed"
        role="presentation"
      >
        <tbody>
          <tr>
            <th>EVENTOS</th>
            <th>Valor Previsto</th>
          </tr>

          <tr>
            <td>Aumento permanente da receita</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="vlrAumentoReceita"
                name="aumentoReceita"
                placeholder="R$00,00"
                label=""
                placeholder="R$00,00"
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>(-)Transferências constitucionais</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="vlrTransfConstitucionais"
                name="transfConstitucionais"
                placeholder="R$00,00"
                label=""
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>(-)Transferências ao FUNDEB</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="vlrTransFundeb"
                name="transFundeb"
                placeholder="R$00,00"
                label=""
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>Saldo final do aumento permanente de receita(I)</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="camposCalculados"
                name="camposCalculadosSaldoFinalReceita"
                placeholder="R$00,00"
                label=""
                [value]="saldoFinal"
                [disabled]="true"
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>Redução permanente de despesa(II)</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="vlrRedPermDespesa"
                name="Conta de receita"
                placeholder="R$00,00"
                label=""
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>Margem bruta(III)=(I)+(II)</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="camposCalculados"
                name="campoCalculadoMargemBruta"
                placeholder="R$00,00"
                label=""
                [value]="margemBruta"
                [disabled]="true"
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>Saldo utilizado da margem bruta(IV)=(V+VI)</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="camposCalculados"
                name="campoCalculadoSaldoMargemBruta"
                placeholder="R$00,00"
                label=""
                [value]="saldoUtilizadoMargemBruta"
                [disabled]="true"
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>Novas DOCC(V)</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="vlrNovasDocc"
                name="novasDocc"
                placeholder="R$00,00"
                label=""
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>Novas DOCC geradas por PPP's(VI)</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="vlrNovasDoccGeradasPpp"
                name="novasDoccGeradasPpp"
                placeholder="R$00,00"
                label=""
              >
              </eqp-nebular-input>
            </td>
          </tr>

          <tr>
            <td>Margem líquida de expansão de DOCC(VII)=(III-IV)</td>
            <td>
              <eqp-nebular-input
                [style]="'currency'"
                [options]="{ prefix: 'R$', thousands: '.', decimal: ',' }"
                [size]="'small'"
                [shape]="'rectangle'"
                formControlName="camposCalculados"
                name="camposCalculadosMargemLiquida"
                placeholder="R$00,00"
                label=""
                [value]="margemLiquida"
                [disabled]="true"
              >
              </eqp-nebular-input>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>

        <button
          *ngIf="
            (model.get('uuid').value && nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="update()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
