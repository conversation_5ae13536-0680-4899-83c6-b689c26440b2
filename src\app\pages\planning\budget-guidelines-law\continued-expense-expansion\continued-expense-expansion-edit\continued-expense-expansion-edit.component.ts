import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { EditorDocComponent } from '@common/dialogs/editor-doc/editor-doc.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { finalize, first, pluck } from 'rxjs/operators'
import { ContinuedExpenseExpansionService } from '../services/continued-expense-expansion.service'

@Component({
  selector: 'eqp-continued-expense-expansion-edit',
  templateUrl: './continued-expense-expansion-edit.component.html',
  styleUrls: ['./continued-expense-expansion-edit.component.scss'],
})
export class ContinuedExpenseExpansionEditComponent
  extends BaseTelasComponent
  implements OnInit, OnD<PERSON>roy
{
  public loading: boolean = false
  pageTitle =
    'Margem de expansão das despesas obrigatórias de caráter continuado'
  public model: FormGroup

  changeTogglePublicationSub: Subscription
  changeModelSub: Subscription

  saldoFinal: number = 0
  margemBruta: number = 0
  saldoUtilizadoMargemBruta: number = 0
  margemLiquida: number = 0

  constructor(
    private formBuilder: FormBuilder,
    private continuedExpenseService: ContinuedExpenseExpansionService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    public menuService: MenuService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/margem-expansao-despesas-obrigatorias',
    )
  }

  ngOnInit(): void {
    this.model = this.getNewForm()
    this.carregarTela()
    this.loadCalHandlers()
  }

  ngOnDestroy(): void {
    if (this.changeModelSub) this.changeModelSub.unsubscribe()
    if (this.changeTogglePublicationSub)
      this.changeTogglePublicationSub.unsubscribe()
  }

  get uuid() {
    return this.model.get('uuid')
  }

  getSaldoFinal(formData) {
    const { vlrAumentoReceita, vlrTransfConstitucionais, vlrTransFundeb } =
      formData

    const calc1 =
      Number(vlrAumentoReceita) -
      Number(vlrTransfConstitucionais) -
      Number(vlrTransFundeb)

    return calc1
  }

  getMargemBruta(formData) {
    const { vlrRedPermDespesa } = formData

    const calc = this.getSaldoFinal(formData) + vlrRedPermDespesa

    return calc
  }

  getSaldoUtilizadoMargemBruta(formData) {
    const { vlrNovasDocc, vlrNovasDoccGeradasPpp } = formData

    const calc = Number(vlrNovasDocc) + Number(vlrNovasDoccGeradasPpp)

    return calc
  }

  getMargemLiquida(formData) {
    const calc =
      this.getMargemBruta(formData) -
      this.getSaldoUtilizadoMargemBruta(formData)

    return calc
  }

  private getNewForm(): FormGroup {
    return this.formBuilder.group({
      uuid: [''],
      leiUuid: [],
      codigo: [],
      camposCalculados: [],
      descricaoTipoDocumento: [],
      escopoDocumentoTce: [],
      codigoControleDocumento: [],
      ano: [],
      vlrTransFundeb: [],
      vlrRedPermDespesa: [],
      vlrNovasDocc: [],
      vlrNovasDoccGeradasPpp: [],
      vlrAumentoReceita: [],
      vlrTransfConstitucionais: [],
      publicacao: [],
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) {
        this.buscar(uuid)
      }
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.continuedExpenseService
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
        pluck('dados'),
      )
      .subscribe(data => {
        this.loadForm(data)
      })
  }

  public cancelar(resposta): void {
    this.router.navigate([
      `lei-diretrizes-orcamentarias/margem-expansao-despesas-obrigatorias`,
    ])
  }

  private loadCalHandlers() {
    this.changeModelSub = this.model.valueChanges.subscribe(val => {
      this.saldoFinal = this.getSaldoFinal(val)
      this.margemBruta = this.getMargemBruta(val)
      this.saldoUtilizadoMargemBruta = this.getSaldoUtilizadoMargemBruta(val)
      this.margemLiquida = this.getMargemLiquida(val)
    })
  }

  loadForm(data) {
    let dto = {
      uuid: data.uuid,
      leiUuid: data.lei.uuid,
      codigo: data.lei.codigo,
      descricaoTipoDocumento: data.lei.descricaoTipoDocumento,
      escopoDocumentoTce: data.lei.escopoDocumentoTce.nome,
      codigoControleDocumento: data.lei.numero,
      ano: data.lei.ano,
      vlrTransFundeb: data.vlrTransFundeb,
      vlrRedPermDespesa: data.vlrRedPermDespesa,
      vlrNovasDocc: data.vlrNovasDocc,
      vlrNovasDoccGeradasPpp: data.vlrNovasDoccGeradasPpp,
      vlrAumentoReceita: data.vlrAumentoReceita,
      vlrTransfConstitucionais: data.vlrTransfConstitucionais,
      flagPublicar: data.flagPublicar === 'S',
    }

    this.model.get('publicacao').setValue(data.publicacao)
    this.model.patchValue(dto)
  }

  private prepare(): any {
    const {
      uuid,
      leiUuid,
      codigo,
      descricaoTipoDocumento,
      escopoDocumentoTce,
      codigoControleDocumento,
      ano,
      vlrTransFundeb,
      vlrRedPermDespesa,
      vlrNovasDocc,
      vlrNovasDoccGeradasPpp,
      vlrAumentoReceita,
      vlrTransfConstitucionais,
      flagPublicar,
      publicacao,
    } = this.model.getRawValue()

    let res = {
      uuid: uuid,
      flagPublicar: flagPublicar,
      lei: {
        uuid: leiUuid,
      },
      vlrAumentoReceita: vlrAumentoReceita,
      vlrNovasDocc: vlrNovasDocc,
      vlrNovasDoccGeradasPpp: vlrNovasDoccGeradasPpp,
      vlrRedPermDespesa: vlrRedPermDespesa,
      vlrTransFundeb: vlrTransFundeb,
      vlrTransfConstitucionais: vlrTransfConstitucionais,
      publicacao: {
        ...publicacao,
        leiUuid: leiUuid,
      },
    }

    res.flagPublicar = flagPublicar ? 'S' : 'N'
    return res
  }

  public update() {
    this.loading = true
    this.continuedExpenseService
      .put(this.prepare())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: 'Margem de expansão atualizada com sucesso.',
        })
      })
  }

  public abrirEditorDocumentoFonte(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.model.get('fonte').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.model.get('fonte').patchValue(retorno)
    })
  }

  public abrirEditorDocumentoNotaExplicativa(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.model.get('notaExplicativa').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.model.get('notaExplicativa').patchValue(retorno)
    })
  }
}
