import { Component, OnDestroy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { ContinuedExpenseExpansionSearchComponent } from '../continued-expense-expansion-search/continued-expense-expansion-search.component'
import { ContinuedExpenseExpansionService } from '../services/continued-expense-expansion.service'

@Component({
  selector: 'eqp-continued-expense-expansion-list',
  templateUrl: './continued-expense-expansion-list.component.html',
  styleUrls: ['./continued-expense-expansion-list.component.scss'],
})
export class ContinuedExpenseExpansionListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle =
    'Margem de expansão das despesas obrigatórias de caráter continuado'

  private subscription: Subscription
  dataSource: DataSource

  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  constructor(
    public menuService: MenuService,
    public router: Router,
    public service: ContinuedExpenseExpansionService,
    public userService: UserDataService,
    private toastrService: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/margem-expansao-despesas-obrigatorias',
    )
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'expansao_despesa_continuada',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public novoRegistro(): void {
    const dialogRef = this.dialogService.open(
      ContinuedExpenseExpansionSearchComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.fetchGrid()
      }
    })
  }

  public remove(value): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastrService.send({
                success: true,
                message: 'Margem de expansão excluída com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastrService.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public alterar(value) {
    this.router.navigate([
      `lei-diretrizes-orcamentarias/margem-expansao-despesas-obrigatorias/edit/${value}`,
    ])
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Margem de expansão'
          item.options.hint = 'Nova margem de expansão'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }
}
