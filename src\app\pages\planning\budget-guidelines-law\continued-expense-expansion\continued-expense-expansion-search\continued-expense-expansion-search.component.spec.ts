import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContinuedExpenseExpansionSearchComponent } from './continued-expense-expansion-search.component';

describe('ContinuedExpenseExpansionSearchComponent', () => {
  let component: ContinuedExpenseExpansionSearchComponent;
  let fixture: ComponentFixture<ContinuedExpenseExpansionSearchComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ContinuedExpenseExpansionSearchComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContinuedExpenseExpansionSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
