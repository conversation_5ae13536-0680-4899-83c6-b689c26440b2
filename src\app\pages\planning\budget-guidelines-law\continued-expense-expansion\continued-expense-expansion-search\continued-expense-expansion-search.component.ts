import { Component, Input, On<PERSON><PERSON>roy, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core'
import { Router } from '@angular/router'
import { DecreeSearchService } from '@common/dialogs/decree-search/decree-search.service'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { ContinuedExpenseExpansionService } from '../services/continued-expense-expansion.service'

@Component({
  selector: 'eqp-continued-expense-expansion-search',
  templateUrl: './continued-expense-expansion-search.component.html',
  styleUrls: ['./continued-expense-expansion-search.component.scss'],
})
export class ContinuedExpenseExpansionSearchComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false

  public userData: {
    exercicio: string
    exercicioUuid: string
  }

  @Input() public dialogTitle: string = 'Leis/Atos | Busca'
  @Input() public url: any

  public gridData: any
  public columnsTemplate: DxColumnInterface[] = []
  public selected: any[] = []

  private subscription: Subscription

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private decreeService: DecreeSearchService,
    private continuedExpenseExpansionService: ContinuedExpenseExpansionService,
    private toastrService: ToastrService,
    private userService: UserDataService,
    public menuService: MenuService,
    public router: Router,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/margem-expansao-despesas-obrigatorias',
    )
  }

  public ngOnInit(): void {
    const { exercicio, exercicioUuid } = this.userService.userData
    this.userData = {
      exercicio,
      exercicioUuid,
    }
    this.fetchGrid()
    this.columnsTemplate = this.getColumnsTemplate()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private fetchGrid(): void {
    this.gridData = {
      store: this.decreeService.getDataSourceFiltro(
        'uuid',
        this.url ? this.url : 'expansao_despesa_continuada/scope_year',
        10,
      ),
      paginate: true,
      pageSize: 10,
    }
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: '',
        dataField: 'uuid',
        width: 70,
        cellTemplate: 'checkedTemplate',
      },
      {
        caption: 'Código',
        dataField: 'codigo',
        width: 100,
      },
      {
        caption: 'Tipo de Documento',
        dataField: 'tipoDocumento.tipoDocumentoTce.nome',
      },
      {
        caption: 'Número',
        dataField: 'numero',
      },
      {
        caption: 'Ano',
        dataField: 'ano',
      },
      {
        caption: 'Descrição',
        dataField: 'descricaoTipoDocumento',
      },
      {
        caption: 'Data',
        dataField: 'data',
      },
      {
        caption: 'Ano Inicial',
        dataField: 'anoInicialAplicacao',
      },
    ]
    return template
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid) return true
    } else false
  }

  public confirm(): void {
    this.loading = true
    const decree = this.grid.instance.getSelectedRowsData()[0]
    this.continuedExpenseExpansionService
      .post({
        lei: {
          uuid: decree.uuid,
        },
        vlrAumentoReceita: 0,
        vlrNovasDocc: 0,
        vlrNovasDoccGeradasPpp: 0,
        vlrRedPermDespesa: 0,
        vlrTransFundeb: 0,
        vlrTransfConstitucionais: 0,
        publicacao: {
          leiUuid: decree.uuid,
          flagPublicar: 'N',
        },
      })
      .pipe(
        first(),
        finalize(() => {
          this.fetchGrid()
          this.loading = false
        }),
      )
      .subscribe(
        res => {
          this.toastrService.send({
            success: true,
            message: 'A lei selecionada foi importada.',
          })
          this.dialogRef.close(res.status)
        },
        (err: any) => this.toastrService.bulkSend(err.mensagens),
      )
  }

  public dispose(): void {
    this.dialogRef.close(false)
  }
}
