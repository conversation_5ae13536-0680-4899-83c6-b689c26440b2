import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { Observable, of } from 'rxjs';
import { ContinuedExpenseExpansionInterface } from '../interfaces/continued-expense-expansion-interface';

@Injectable({
  providedIn: 'root'
})
export class ContinuedExpenseExpansionService extends BaseService<
  ResponseDto<ContinuedExpenseExpansionInterface[]>,
  ContinuedExpenseExpansionInterface
>{
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'expansao_despesa_continuada')
   }


}
