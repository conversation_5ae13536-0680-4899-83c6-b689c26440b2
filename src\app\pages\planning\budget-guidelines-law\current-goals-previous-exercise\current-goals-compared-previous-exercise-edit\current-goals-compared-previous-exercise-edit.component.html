<nb-card>
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ 'Editar valores' }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          readonly="true"
          formControlName="especificacao"
          name="especificacao"
          label="Especificação"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <br />
    <div class="row">
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="corrente"
          name="corrente"
          label="Corrente"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="constante"
          name="constante"
          label="Constante"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="model.invalid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
