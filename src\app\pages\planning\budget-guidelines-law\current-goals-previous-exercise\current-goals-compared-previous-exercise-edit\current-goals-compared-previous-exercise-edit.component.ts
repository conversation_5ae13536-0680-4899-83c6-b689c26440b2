import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NbDialogRef } from '@nebular/theme';

@Component({
  selector: 'eqp-current-goals-compared-previous-exercise-edit',
  templateUrl: './current-goals-compared-previous-exercise-edit.component.html',
  styleUrls: ['./current-goals-compared-previous-exercise-edit.component.scss']
})
export class CurrentGoalsComparedPreviousExerciseEditComponent implements OnInit {

	public model: FormGroup

	@Input() data: {
		especificacao: string
		corrente: number
		constante: number
	}

  constructor(
		protected ref: NbDialogRef<CurrentGoalsComparedPreviousExerciseEditComponent>,
		private formBuilder: FormBuilder
	) {}

  ngOnInit(): void {
		this.model = this.getNovoFormulario()
  }

	public getNovoFormulario() {
		return this.formBuilder.group({
			especificacao: [this.data.especificacao],
			corrente: [this.data.corrente, Validators.required],
			constante: [this.data.constante, Validators.required]
		})
	}

	public cancel() {
		this.ref.close(null)
	}

	public confirm() {
		this.ref.close(this.model.getRawValue())
	}
}
