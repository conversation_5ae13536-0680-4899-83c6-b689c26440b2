<eqp-standard-page [mainTitle]="pageTitle" [spinnerActive]="loading" [spinnerStatus]="'info'"
  [rightApproveButtonVisible]="true" [rightApproveButtonText]="'Confirmar'"
  [rightApproveButtonIcon]="'fas fa-check'" [rightApproveButtonId]="'put-current-goals-compared'"
  [rightApproveButtonDisabled]="!exercicioReferenciaUuid" (rightApproveButtonEmitter)="salvar()" [formGroup]="model"
  [rightCustomButtonVisible]="false" [rightCustomButtonIconVisible]="true" [rightCustomButtonText]="'Importar valores'"
  [rightCustomButtonIcon]="'fas fa-file-import'" [rightCustomButtonId]="'import-revenue-plan'"
  >
  <div class="row">
    <div class="col col-12 col-md-auto">
      <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="exercicioReferencia"
        label="Exercício de referência" placeholder="Exercício de referência" valueExpr="uuid"
        displayExpr="referenceExercise" [dataSource]="exercicioReferenciaData"
        formControlName="currentGoalsComparedPreviousExerciseUuid">
      </eqp-nebular-select>
    </div>
    <div class="col col-4 col-sm-auto mt-4 p-1">
      <eqp-publication-note-field [formControl]="publication"></eqp-publication-note-field>
    </div>
  </div>
  <ng-container *ngIf="exercicioReferenciaUuid">
    <dx-data-grid id="currentGoalsComparedPreviousExerciseGrid" [dataSource]="dataSource" [allowColumnResizing]="true"
      [columnAutoWidth]="true" [showColumnLines]="false" [showRowLines]="false" [showBorders]="false"
      [rowAlternationEnabled]="true" [wordWrapEnabled]="true" [loadPanel]="false" [columnHidingEnabled]="true"
      [remoteOperations]="true">
      <dxo-state-storing [enabled]="true" type="custom" [customLoad]="loadState" [customSave]="saveState"
        savingTimeout="100"></dxo-state-storing>
      <dxo-export [enabled]="true" [excelWrapTextEnabled]="true" [excelFilterEnabled]="true"
        [title]="'Exportar a tabela'" [fileName]="pageTitle"></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>

      <dxo-pager [showInfo]="true" [showNavigationButtons]="true" [showPageSizeSelector]="false">
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

      <dxo-search-panel [visible]="false" placeholder="Buscar receita"></dxo-search-panel>

      <dxo-editing mode="form" [allowUpdating]="false" [allowDeleting]="false" [allowAdding]="false" [useIcons]="true">
      </dxo-editing>

      <dxi-column dataField="especificacao" caption="Especificação">
      </dxi-column>

      <dxi-column alignment="center" [caption]="anoReferencia">
        <dxi-column dataField="corrente" caption="Corrente" alignment="left" [format]="currencyFormat">
        </dxi-column>
        <dxi-column dataField="constante" caption="Constante" alignment="left" [format]="currencyFormat">
        </dxi-column>
      </dxi-column>

      <dxi-column dataField="uuid" caption="" [width]="40" [allowFiltering]="false" [allowSorting]="false"
        cellTemplate="acaoColumn"></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a title="Alterar" (click)="alterar(data)" class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid">
        </a>
      </div>
    </dx-data-grid>
    <!-- (buttonEmitter)="file.click()" -->
  </ng-container>
</eqp-standard-page>
