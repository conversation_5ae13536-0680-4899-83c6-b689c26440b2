import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { FormBuilder, FormControl, FormGroup } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { forkJoin, from, Observable, Subscription } from 'rxjs'
import { distinctUntilChanged, finalize, first } from 'rxjs/operators'
import { CurrentGoalsComparedPreviousExerciseEditComponent } from '../current-goals-compared-previous-exercise-edit/current-goals-compared-previous-exercise-edit.component'
import {
  CALCULATED_FIELD_ID,
  PRIMARY_FIELDS,
  loadForm,
  prepare,
} from '../helpers/helper'
import { CurrentGoalsComparedPreviousExerciseInterface } from '../interfaces/current-goals-compared-previous-exercise'
import { CurrentGoalsComparedPreviousExerciseReportInterface } from '../interfaces/current-goals-compared-previous-exercise-report'
import { CurrentGoalsComparedPreviousExerciseService } from '../services/current-goals-compared-previous-exercise.service'

@Component({
  selector: 'eqp-current-goals-compared-previous-exercise-list',
  templateUrl: './current-goals-compared-previous-exercise-list.component.html',
  styleUrls: ['./current-goals-compared-previous-exercise-list.component.scss'],
})
export class CurrentGoalsComparedPreviousExerciseListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public pageTitle =
    'Metas atuais comparadas com as fixadas nos três exercícios anteriores'
  public loading: boolean
  public model: FormGroup
  public exercicioReferenciaData: DataSource<
    CurrentGoalsComparedPreviousExerciseInterface,
    string
  >
  public dataSource: DataSource<
    CurrentGoalsComparedPreviousExerciseReportInterface,
    number
  >
  public exercicioReferenciaUuid: string
  public anoReferencia: number
  public pendingRequisitionsCounter: number
  public isSaveAll: boolean
  private subscription: Subscription

  public publication = new FormControl()

  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  constructor(
    public menuService: MenuService,
    public router: Router,
    private formBuilder: FormBuilder,
    private service: CurrentGoalsComparedPreviousExerciseService,
    private dialogService: NbDialogService,
    private toastrService: ToastrService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/metas-atuais-comparadas-exercicios-anteriores',
    )
  }

  ngOnInit(): void {
    this.model = this.getNovoFormulario()
    this.loadSelect()
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
  }

  public getNovoFormulario() {
    return this.formBuilder.group({
      currentGoalsComparedPreviousExerciseUuid: [],
      publicacao: []
    })
  }

  private criar() {
    this.loading = true
    this.service
      .postBatch([])
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.exercicioReferenciaData = new DataSource({
          store: {
            type: 'array',
            data: res.body.dados,
            key: 'uuid',
          },
        })
        this.toastrService.send({
          success: true,
          message: 'Metas fiscais criadas com sucesso.',
        })
      })
  }

  public loadSelect() {
    this.loading = true
    this.service
      .get()
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        const {dados} = res
        if (dados.length) {
          const {dados} = res
          this.publication.patchValue(dados[0].publicacao ? dados[0].publicacao : null)
          this.exercicioReferenciaData = new DataSource({
            store: {
              type: 'array',
              data: dados,
              key: 'uuid',
            },
          })
        } else {
          this.criar()
        }
        this.selectionHandler()
      })
  }

  public selectionHandler() {
    this.subscription = this.model
      .get('currentGoalsComparedPreviousExerciseUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(async exercicioUuid => {
        if (this.exercicioReferenciaUuid) {
          await this.atualizarDataSource()
        }
        this.exercicioReferenciaUuid = exercicioUuid
        let selectedExercise = await this.exercicioReferenciaData
          .store()
          .byKey(exercicioUuid)
        this.anoReferencia = selectedExercise.referenceExercise
        this.dataSource = new DataSource({
          store: {
            type: 'array',
            data: loadForm(selectedExercise),
            key: 'id',
          },
        })
      })
  }

  private async atualizarDataSource() {
    await this.exercicioReferenciaData
    .store()
    .update(
      this.exercicioReferenciaUuid,
      prepare(
        this.exercicioReferenciaUuid,
        this.anoReferencia,
        this.dataSource.items(),
        this.publicacao?.value
      ),
    )
  }

  public alterar({ data }) {
    if (data.id == CALCULATED_FIELD_ID) {
      this.toastrService.send({
        error: true,
        message: 'Essa linha não pode ser alterada',
      })
    } else {
      const ref = this.dialogService.open(
        CurrentGoalsComparedPreviousExerciseEditComponent,
        {
          context: {
            data: {
              especificacao: data.especificacao,
              corrente: data.corrente,
              constante: data.constante,
            },
          },
          closeOnBackdropClick: false,
          closeOnEsc: false,
        },
      )
      ref.onClose.pipe(first()).subscribe(res => {
        if (res) {
          this.updateGridValues(res, data.id)
        }
      })
    }
  }

  private async updateGridValues(
    newValue: { corrente: number; constante: number },
    id: number,
  ) {
    this.loading = true
    await this.dataSource.store().update(id, {
      corrente: newValue.corrente,
      constante: newValue.constante,
    })
    if (PRIMARY_FIELDS.includes(id)) {
      const x = await this.dataSource.store().byKey(PRIMARY_FIELDS[0])
      const y = await this.dataSource.store().byKey(PRIMARY_FIELDS[1])
      await this.dataSource.store().update(CALCULATED_FIELD_ID, {
        corrente: x.corrente - y.corrente,
        constante: x.constante - y.constante,
      })
    }
    await this.dataSource.reload()
    await this.atualizarDataSource()
    this.loading = false
  }

  public finalizouRequisicao() {
    this.pendingRequisitionsCounter--
    if (!this.pendingRequisitionsCounter) {
      this.loading = false
      if (this.isSaveAll) {
        this.loadSelect()
        this.toastrService.send({
          success: true,
          message: 'Metas fiscais atualizadas com sucesso.',
        })
      }
    }
  }

  public salvar() {
    this.pendingRequisitionsCounter =
      this.exercicioReferenciaData.items().length
    this.isSaveAll = true
    this.loading = true
    
    this.exercicioReferenciaData
      .items()
      .forEach((goal: CurrentGoalsComparedPreviousExerciseInterface) => {
        goal.publicacao = this.publication.value
        this.service
          .put(goal)
          .pipe(
            first(),
            finalize(() => this.finalizouRequisicao()),
          )
          .subscribe({
            error: err => {
              this.isSaveAll = false
            },
          })
      })
  }

  public get publicacao(){
    return this.model.get('publicacao').value
  }

  public set flagPublicar(value: boolean) {
    this.model.get('flagPublicar').setValue(value)
  }
}
