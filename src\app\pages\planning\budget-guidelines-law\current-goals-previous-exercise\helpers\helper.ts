import { PublicationNoteInterface } from '@pages/planning/shared/interfaces/publication-note'
import { CurrentGoalsComparedPreviousExerciseInterface } from '../interfaces/current-goals-compared-previous-exercise'
import { CurrentGoalsComparedPreviousExerciseReportInterface } from '../interfaces/current-goals-compared-previous-exercise-report'

export function loadForm(
  data: CurrentGoalsComparedPreviousExerciseInterface,
): CurrentGoalsComparedPreviousExerciseReportInterface[] {
  return [
    {
      id: 0,
      especificacao: 'Receita Total',
      corrente: data.vlrRecTotalCorrente,
      constante: data.vlrRecTotalConstante,
    },
    {
      id: 1,
      especificacao: 'Receitas Primárias (I)',
      corrente: data.vlrRecPrimariaCorrente,
      constante: data.vlrRecPrimariaConstante,
    },
    {
      id: 2,
      especificacao: 'Despesa Total',
      corrente: data.vlrDespTotalCorrente,
      constante: data.vlrDespTotalConstante,
    },
    {
      id: 3,
      especificacao: 'Despesas Primárias (II)',
      corrente: data.vlrDespPrimariaCorrente,
      constante: data.vlrDespPrimariaConstante,
    },
    {
      id: 4,
      especificacao: 'Resultado Primário (III) = (I) - (II)',
      corrente: data.vlrRecPrimariaCorrente - data.vlrDespPrimariaCorrente,
      constante: data.vlrRecPrimariaConstante - data.vlrDespPrimariaConstante,
    },
    {
      id: 5,
      especificacao: 'Resultado Nominal',
      corrente: data.vlrResNomCorrente,
      constante: data.vlrResNomConstante,
    },
    {
      id: 6,
      especificacao: 'Dívida Pública Consolidada',
      corrente: data.vlrDivPublicaCorrente,
      constante: data.vlrDivPublicaConstante,
    },
    {
      id: 7,
      especificacao: 'Dívida Consolidada Líquida',
      corrente: data.vlrDivConsLiqCorrente,
      constante: data.vlrDivConsLiqConstante,
    },
  ]
}

export function prepare(
  uuid: string,
  referenceExercise: number,
  data: CurrentGoalsComparedPreviousExerciseReportInterface[],
  publicacao: PublicationNoteInterface,
): CurrentGoalsComparedPreviousExerciseInterface {
  data.sort((a, b) => a.id - b.id)
  return {
    uuid,
    referenceExercise,
    vlrRecTotalCorrente: data[0].corrente,
    vlrRecTotalConstante: data[0].constante,
    vlrRecPrimariaCorrente: data[1].corrente,
    vlrRecPrimariaConstante: data[1].constante,
    vlrDespTotalCorrente: data[2].corrente,
    vlrDespTotalConstante: data[2].constante,
    vlrDespPrimariaCorrente: data[3].corrente,
    vlrDespPrimariaConstante: data[3].constante,
    vlrResNomCorrente: data[5].corrente,
    vlrResNomConstante: data[5].constante,
    vlrDivPublicaCorrente: data[6].corrente,
    vlrDivPublicaConstante: data[6].constante,
    vlrDivConsLiqCorrente: data[7].corrente,
    vlrDivConsLiqConstante: data[7].constante,
    publicacao,
  }
}

export const CALCULATED_FIELD_ID = 4
export const PRIMARY_FIELDS = [1, 3]
