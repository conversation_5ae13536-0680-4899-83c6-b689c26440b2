import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { Observable } from 'rxjs';
import { CurrentGoalsComparedPreviousExerciseInterface } from '../interfaces/current-goals-compared-previous-exercise';

@Injectable({
  providedIn: 'root'
})
export class CurrentGoalsComparedPreviousExerciseService
	extends BaseService<
			ResponseDto<CurrentGoalsComparedPreviousExerciseInterface[]>,
			CurrentGoalsComparedPreviousExerciseInterface	
		>
{

  constructor(http: HttpClient) {
		super(http, 'metas_atuais_comparadas_exercicios_anteriores')
	}

	public postLote() {
    const headers = new HttpHeaders()

    return this.http.post<ResponseDto<CurrentGoalsComparedPreviousExerciseInterface[]>>(
			'metas_atuais_comparadas_exercicios_anteriores', null, {
      headers,
      observe: 'response',
    })
  }
}
