<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
        <eqp-breadcrumb></eqp-breadcrumb>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row" formGroupName="lei">
      <div class="col col-12 col-md-3">
        <eqp-nebular-input
          [size]="'small'"
          [showSuffixIcon]="true"
          [primarySuffixIcon]="true"
          [firstSuffixIcon]="'search'"
          (click)="openDecreeSearch()"
          formControlName="nome"
          name="leiAto"
          [disabled]="true"
          label="Lei/Ato *"
          placeholder="Lei/Ato"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="tipoDocumento"
          name="tipoDocumento"
          label="Tipo do documento"
          placeholder="Tipo do documento"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-5">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="escopoDocumento"
          name="escopoDocumento"
          label="Escopo do documento"
          placeholder="Escopo do documento"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row mt-md-3">
      <div class="col col-12 col-md-2" formGroupName="lei">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="numDocumento"
          name="numDocumento"
          label="N° do documento"
          placeholder="N° do documento"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-2" formGroupName="lei">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="anoDocumento"
          name="anoDocumento"
          label="Ano do documento"
          placeholder="Ano do documento"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-2">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="exercicioReferencia"
          label="Exercício *"
          placeholder=""
          formControlName="exercicioReferencia"
          [dataSource]="exercicioData"
          displayExpr="ano"
          valueExpr="ano"
        >
        </eqp-nebular-select>
      </div>
      <div class="col col-12 col-md-6 mt-4">
        <eqp-publication-note-field
          formControlName="publicacao"
        ></eqp-publication-note-field>
      </div>
    </div>
    <div class="row mt-md-3">
      <div class="col col-12 col-md-4" formGroupName="tipoRenuncia">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="modalidadeRrnuncia"
          label="Modalidade da renúncia *"
          placeholder=""
          formControlName="uuid"
          [dataSource]="waiverTypeData"
          valueExpr="uuid"
          displayExpr="nome"
          [enableAddButton]="true"
          (onAddButtonClick)="openTipoModalidadeRenuncia()"
        ></eqp-nebular-select>
      </div>
      <div class="col col-12 col-md-4" formGroupName="tipoCredito">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="tipoCredito"
          label="Tipo de crédito *"
          placeholder=""
          errorMessage="É obrigatório selecionar o tipo de crédito"
          formControlName="uuid"
          [dataSource]="creditTypeData"
          valueExpr="uuid"
          displayExpr="nome"
          [enableAddButton]="true"
          (onAddButtonClick)="openTipoCredito()"
        ></eqp-nebular-select>
      </div>
      <div class="col col-12 col-md-4">
        <eqp-nebular-input
          [style]="'currency'"
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="vlrRenuncia"
          name="valor"
          label="Valor *"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row mt-md-4">
      <div class="col">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="dsSetorBeneficiario"
          name="dsSetorBeneficiario"
          label="Descrição do setor/programa beneficiário *"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row mt-md-3">
      <div class="col">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="dsCompensacao"
          name="dsCompensacao"
          label="Descrição da compensação *"
          placeholder=""
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          *ngIf="
            (model.get('uuid').value && nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          [disabled]="model.invalid"
          class="btn btn-success float-md-right"
          (click)="confirm()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
