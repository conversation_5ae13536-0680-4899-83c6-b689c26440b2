import { Component, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { Observable } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { CustomDecreeSearchComponent } from '../../../../../@common/dialogs/custom-decree-search/custom-decree-search.component'
import { CreditTypeInterface } from '../interfaces/credit-type'
import { CustomDecreeInterface } from '../interfaces/custom-decree'
import { EstimationCompensationRevenueWaiverInterface } from '../interfaces/estimation-compensation-revenue-waiver'
import { WaiverTypeInterface } from '../interfaces/waiver-type'
import { EstimationCompensationRevenueWaiverService } from '../services/estimation-compensation-revenue-waiver.service'
import { CreditTypeDialogComponent } from '../types-dialogs/components/credit-type-dialog/credit-type-dialog.component'
import { WaiverTypeDialogComponent } from '../types-dialogs/components/waiver-type-dialog/waiver-type-dialog.component'

@Component({
  selector: 'eqp-estimation-compensation-revenue-waiver-form',
  templateUrl: './estimation-compensation-revenue-waiver-form.component.html',
  styleUrls: ['./estimation-compensation-revenue-waiver-form.component.scss'],
})
export class EstimationCompensationRevenueWaiverFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Estimativa e compensação da renúncia de receita'
  public loading = false
  public model: FormGroup
  public exercicioData: { ano: number }[]
  public waiverTypeData: DataSource<WaiverTypeInterface, string>
  public creditTypeData: DataSource<CreditTypeInterface, string>

  constructor(
    public menuService: MenuService,
    public router: Router,
    private route: ActivatedRoute,
    private userService: UserDataService,
    private formBuilder: FormBuilder,
    private toastrService: ToastrService,
    private dialogService: NbDialogService,
    private service: EstimationCompensationRevenueWaiverService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/estimativa-compensacao-renuncia-receita',
    )
  }

  ngOnInit(): void {
    this.model = this.getNovoFormulario()
    this.carregarTela()
  }

  private getNovoFormulario() {
    return this.formBuilder.group({
      uuid: [''],
      exercicioReferencia: ['', Validators.required],
      lei: this.formBuilder.group({
        uuid: ['', Validators.required],
        nome: [''],
        tipoDocumento: [''],
        escopoDocumento: [''],
        numDocumento: [''],
        anoDocumento: [''],
      }),
      tipoRenuncia: this.formBuilder.group({
        uuid: [undefined, Validators.required],
      }),
      tipoCredito: this.formBuilder.group({
        uuid: [undefined, Validators.required],
      }),
      vlrRenuncia: [0, [Validators.required, Validators.min(0)]],
      dsSetorBeneficiario: ['', Validators.required],
      dsCompensacao: ['', Validators.required],
      publicacao: [],
    })
  }

  private carregarTela() {
    this.loadSelects()
    const uuid = this.route.snapshot.params['uuid']
    if (uuid) {
      this.buscar(uuid)
    }
  }

  private loadSelects() {
    const logado = +this.userService.userData.exercicio
    this.exercicioData = [
      { ano: logado },
      { ano: logado + 1 },
      { ano: logado + 2 },
    ]
    this.waiverTypeData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'estimativa_compensacao_renuncia_receita/tipo_renuncia',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.creditTypeData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'estimativa_compensacao_renuncia_receita/planejamento_tipo_credito',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.waiverTypeData.load()
    this.creditTypeData.load()
  }

  private buscar(uuid: string) {
    this.loading = true
    this.service
      .getOne(uuid)
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        console.log(res)
        this.fillFields(res)
        console.log(res)

        this.fillDecreeFields(res.lei)
      })
  }

  public fillFields(dto: EstimationCompensationRevenueWaiverInterface) {
    this.model.patchValue(dto)
  }

  public openDecreeSearch(): void {
    const dialogRef = this.dialogService.open(CustomDecreeSearchComponent, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(decree => {
      if (decree) {
        this.fillDecreeFields(decree)
      }
    })
  }

  private fillDecreeFields(decree: CustomDecreeInterface) {
    this.model.get('lei.uuid').patchValue(decree.uuid)
    this.model.get('lei.nome').patchValue(`${decree.numero} / ${decree.ano}`)
    this.model.get('lei.tipoDocumento').patchValue(decree.tipoDocumento.nome)
    this.model
      .get('lei.escopoDocumento')
      .patchValue(decree.escopoDocumentoTce.nome)
    this.model.get('lei.numDocumento').patchValue(decree.numero)
    this.model.get('lei.anoDocumento').patchValue(decree.ano)
  }

  public confirm() {
    this.loading = true
    const uuid = this.model.get('uuid').value
    const dto = this.model.getRawValue()
    if (uuid) {
      dto.publicacao = {
        ...dto.publicacao,
        leiUuid: dto.lei?.uuid
      }
      this.salvar(
        this.service.put(dto),
        'Estimativa e compensação da renúncia de receita atualizada com sucesso.',
        )
      } else {
      dto.publicacao = {
        ...dto.publicacao,
        flagPublicar: dto.publicacao?.flagPublicar ? dto.publicacao?.flagPublicar : 'N',
        leiUuid: dto.lei?.uuid
      }
      this.salvar(
        this.service.post(dto),
        'Nova estimativa e compensação da renúncia de receita cadastrada com sucesso.',
      )
    }
  }

  public salvar(observable$: Observable<any>, successMessage: string) {
    observable$
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.router.navigate([
          'lei-diretrizes-orcamentarias/estimativa-compensacao-renuncia-receita',
        ])
        this.toastrService.send({
          success: true,
          message: successMessage,
        })
      })
  }

  public cancel() {
    this.router.navigate([
      'lei-diretrizes-orcamentarias/estimativa-compensacao-renuncia-receita',
    ])
  }

  openTipoCredito() {
    const dialogRef = this.dialogService.open(CreditTypeDialogComponent, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(res => {
      if (res) {
        this.loadSelects()
      }
    })
  }

  openTipoModalidadeRenuncia() {
    const dialogRef = this.dialogService.open(WaiverTypeDialogComponent, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(res => {
      if (res) {
        this.loadSelects()
      }
    })
  }
}
