import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { EstimationCompensationRevenueWaiverInterface } from '../interfaces/estimation-compensation-revenue-waiver'
import { EstimationCompensationRevenueWaiverService } from '../services/estimation-compensation-revenue-waiver.service'

@Component({
  selector: 'eqp-estimation-compensation-revenue-waiver-list',
  templateUrl: './estimation-compensation-revenue-waiver-list.component.html',
  styleUrls: ['./estimation-compensation-revenue-waiver-list.component.scss'],
})
export class EstimationCompensationRevenueWaiverListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Estimativa e compensação da renúncia de receita'
  public loading = false
  public dataSource: DataSource<
    EstimationCompensationRevenueWaiverInterface,
    string
  >
  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  constructor(
    public menuService: MenuService,
    public router: Router,
    private service: EstimationCompensationRevenueWaiverService,
    private dialogService: NbDialogService,
    private toastrService: ToastrService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/estimativa-compensacao-renuncia-receita',
    )
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  public fetchGrid() {
    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'estimativa_compensacao_renuncia_receita/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'NOVO'
          item.options.hint =
            'Nova estimativa e compensação da renúncia de receita'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public novoRegistro() {
    this.router.navigate([
      'lei-diretrizes-orcamentarias/estimativa-compensacao-renuncia-receita/novo',
    ])
  }

  public alterar(value) {
    this.router.navigate([
      `lei-diretrizes-orcamentarias/estimativa-compensacao-renuncia-receita/edit/${value}`,
    ])
  }

  public remover(value) {
    const ref = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    ref.onClose.pipe(first()).subscribe(res => {
      if (res == 'S') {
        this.loading = true
        this.service
          .delete(value)
          .pipe(
            first(),
            finalize(() => (this.loading = false)),
          )
          .subscribe(res => {
            this.fetchGrid()
            this.toastrService.send({
              success: true,
              message:
                'Estimativa e compensação da renúncia de receita deletada com sucesso.',
            })
          })
      }
    })
  }
}
