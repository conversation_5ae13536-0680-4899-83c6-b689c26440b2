
export interface CustomDecreeInterface {
	uuid: string;
	entidade: string;
	codigo: number;
	tipoDocumento: TipoDocumento;
	descricaoTipoDocumento: string;
	numero: number;
	ano: number;
	data: string;
	anoInicialAplicacao: number;
	entidadeOrigem: EntidadeOrigem;
	codigoControleDocumento: number;
	dataInclusaoTce: string;
	rNaInicializacao: string;
	publicarInternet: string;
	escopoDocumentoTce: EscopoDocumentoTce;
}

interface TipoDocumentoTce {
	uuid: string;
	uf: string;
	codigo: number;
	nome: string;
	exigeNrNumero: string;
}

interface TipoDocumento {
	uuid: string;
	nome: string;
	municipioClienteUuid: string;
	tipoDocumentoTce: TipoDocumentoTce;
}

interface TipoEntidade {
	uuid: string;
	nome: string;
}

interface TipoEsferaGoverno {
	uuid: string;
	nome: string;
	flagOrgaoRepassador: string;
	flagLei: string;
}

interface TipoPrevidencia {
	uuid: string;
	nome: string;
}

interface PessoaFisica {
	dataNascimento: Date;
	cargoUuid: string;
	estadoCivilUuid: string;
	sexoUuid: string;
}

interface Pessoa {
	uuid: string;
	codigo: number;
	digitoVerificador: string;
	tipoPessoaId: number;
	nome: string;
	ativo: string;
	documento: string;
	pessoaFisica: PessoaFisica;
}

interface EntidadeOrigem {
	uuid: string;
	clienteUuid: string;
	codigo: number;
	nome: string;
	uf: string;
	pessoaJuridicaUuid: string;
	tipoEntidade: TipoEntidade;
	codigoTce: number;
	cnae: number;
	codfpas: number;
	codigoSiconfi: string;
	tipoEsferaGoverno: TipoEsferaGoverno;
	tipoPrevidencia: TipoPrevidencia;
	pessoa: Pessoa;
}

interface EscopoDocumentoTce {
	uuid: string;
	uf: string;
	codigo: number;
	nome: string;
	plurianual: string;
}