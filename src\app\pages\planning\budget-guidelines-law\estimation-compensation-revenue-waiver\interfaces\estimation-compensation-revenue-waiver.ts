import { CreditTypeInterface } from './credit-type'
import { CustomDecreeInterface } from './custom-decree'
import { WaiverTypeInterface } from './waiver-type'

export interface EstimationCompensationRevenueWaiverInterface {
  uuid: string
  exercicioReferencia: number
  lei: CustomDecreeInterface // Lei e ato
  tipoRenuncia: WaiverTypeInterface
  tipoCredito: CreditTypeInterface
  vlrRenuncia: number
  dsSetorBeneficiario: string
  dsCompensacao: string
  publicacao: any
  flagPublicar: string // 'S' || 'N'
}
