import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { CreditTypeInterface } from '../interfaces/credit-type';

@Injectable({
  providedIn: 'root'
})
export class CreditTypeService extends BaseService<
	ResponseDto<CreditTypeInterface>,
	CreditTypeInterface
> {

  constructor(protected http: HttpClient) {
		super(http, 'planejamento_tipo_credito')
	}
}
