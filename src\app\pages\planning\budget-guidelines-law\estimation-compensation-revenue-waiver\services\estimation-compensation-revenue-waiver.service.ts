import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { EstimationCompensationRevenueWaiverInterface } from '../interfaces/estimation-compensation-revenue-waiver';

@Injectable({
  providedIn: 'root'
})
export class EstimationCompensationRevenueWaiverService extends
	BaseService<
		ResponseDto<EstimationCompensationRevenueWaiverInterface>,
		EstimationCompensationRevenueWaiverInterface
	>
{
  constructor(protected http: HttpClient) {
		super(http, 'estimativa_compensacao_renuncia_receita')
	}
}
