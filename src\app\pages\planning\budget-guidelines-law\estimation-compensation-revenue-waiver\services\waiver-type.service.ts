import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { WaiverTypeInterface } from '../interfaces/waiver-type';

@Injectable({
  providedIn: 'root'
})
export class WaiverTypeService extends BaseService<
	ResponseDto<WaiverTypeInterface>,
	WaiverTypeInterface
>
{
  constructor(protected http: HttpClient) {
		super(http, 'tipo_renuncia')
	}
}
