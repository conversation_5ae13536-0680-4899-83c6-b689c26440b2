<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col col-md-6">
        <eqp-nebular-input
          [size]="'small'"
          [shape]="'rectangle'"
          label="Nome*"
          placeholder="Nome"
          formControlName="nome"
          valueExpr="uuid"
        ></eqp-nebular-input>
      </div>
      <div class="col col-md-6" formGroupName="tipoCreditoTce">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Tipo de crédito TCE*"
          placeholder=""
          formControlName="uuid"
          [dataSource]="creditTceData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancel()">
          Voltar
        </button>

        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="confirm()"
          [disabled]="model.pristine || !model.valid"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
