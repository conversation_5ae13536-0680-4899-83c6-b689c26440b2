import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import DataSource from 'devextreme/data/data_source';
import { Observable } from 'rxjs';
import { finalize, take } from 'rxjs/operators';
import { CreditTypeService } from '../../../services/credit-type.service';

@Component({
  selector: 'eqp-credit-type-dialog',
  templateUrl: './credit-type-dialog.component.html',
  styleUrls: ['./credit-type-dialog.component.scss']
})
export class CreditTypeDialogComponent implements OnInit {
  pageTitle = 'Tipo de Crédito';
  loading: boolean = false;
  model: FormGroup;

  creditTceData: DataSource

  constructor(
    private service: CreditTypeService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<CreditTypeDialogComponent>,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel();
    this.initializeSelectData();
  }


  getNewModel(): FormGroup {
    return this.builder.group({
      nome: ['', Validators.required],
      tipoCreditoTce: this.builder.group({
        uuid: ['', Validators.required],
      }),
    });
  }

  codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`;
  }


  private async initializeSelectData() {
    const creditTce = await this.service
      .customGetSingleData('tipo_credito_tce', {
        take: 20,
        skip: 0,
      })
      .toPromise();
    this.creditTceData = new DataSource({
      store: {
        data: creditTce.data || [],
        type: 'array',
        key: 'uuid',
      },
    });
  }



  prepare(formData: any){
    const dto = {
      nome: formData.nome,
      tipoCreditoTce: formData.tipoCreditoTce
        ? { uuid: formData.tipoCreditoTce.uuid }
        : null,
    };
    return dto;
  }


  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue());
      let req: Observable<any>;

      req = this.service.post(dto);

      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe((res) => {
          this.toastr.send({
            title: 'Sucesso',
            message: `Tipo de crédito cadastrado(a) com sucesso.`,
            success: true,
          });
          this.dialogRef.close(res.body.dados);
        });
    }
  }

  cancel() {
    this.dialogRef.close();
  }
}
