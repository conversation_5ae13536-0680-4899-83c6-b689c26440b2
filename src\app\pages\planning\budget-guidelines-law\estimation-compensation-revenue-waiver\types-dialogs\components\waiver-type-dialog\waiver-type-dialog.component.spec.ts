import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WaiverTypeDialogComponent } from './waiver-type-dialog.component';

describe('WaiverTypeDialogComponent', () => {
  let component: WaiverTypeDialogComponent;
  let fixture: ComponentFixture<WaiverTypeDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ WaiverTypeDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(WaiverTypeDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
