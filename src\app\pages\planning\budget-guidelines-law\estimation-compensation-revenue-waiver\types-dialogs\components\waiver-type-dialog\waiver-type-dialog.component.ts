import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef } from '@nebular/theme';
import DataSource from 'devextreme/data/data_source';
import { Observable } from 'rxjs';
import { finalize, take } from 'rxjs/operators';
import { WaiverTypeService } from '../../../services/waiver-type.service';

@Component({
  selector: 'eqp-waiver-type-dialog',
  templateUrl: './waiver-type-dialog.component.html',
  styleUrls: ['./waiver-type-dialog.component.scss']
})
export class WaiverTypeDialogComponent implements OnInit {

  pageTitle = 'Modalidade de Renúncia';
  loading: boolean = false;
  model: FormGroup;

  creditTceData: DataSource

  constructor(
    private service: WaiverTypeService,
    private builder: FormBuilder,
    private dialogRef: NbDialogRef<WaiverTypeDialogComponent>,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel();
    this.initializeSelectData();
  }


  getNewModel(): FormGroup {
    return this.builder.group({
      nome: ['', Validators.required],
      tipoRenunciaTce: this.builder.group({
        uuid: ['', Validators.required],
      }),
    });
  }

  codeNameDisplay(item) {
    return item && `${item.codigo} - ${item.nome}`;
  }


  private async initializeSelectData() {
    const creditTce = await this.service
      .customGetSingleData('tipo_renuncia_tce', {
        take: 20,
        skip: 0,
      })
      .toPromise();
    this.creditTceData = new DataSource({
      store: {
        data: creditTce.data || [],
        type: 'array',
        key: 'uuid',
      },
    });
  }



  prepare(formData: any){
    const dto = {
      nome: formData.nome,
      tipoRenuncia: formData.tipoRenunciaTce
        ? { uuid: formData.tipoRenunciaTce.uuid }
        : null,
    };
    return dto;
  }


  confirm() {
    if (this.model.valid) {
      const dto = this.prepare(this.model.getRawValue());
      let req: Observable<any>;

      req = this.service.post(dto);

      req
        .pipe(
          take(1),
          finalize(() => (this.loading = false)),
        )
        .subscribe((res) => {
          this.toastr.send({
            title: 'Sucesso',
            message: `Modalidade de renúncia cadastrada(o) com sucesso.`,
            success: true,
          });
          this.dialogRef.close(res.body.dados);
        });
    }
  }

  cancel() {
    this.dialogRef.close();
  }
}
