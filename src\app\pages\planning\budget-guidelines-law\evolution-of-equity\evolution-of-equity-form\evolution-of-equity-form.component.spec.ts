import { ComponentFixture, TestBed } from '@angular/core/testing';

import { EvolutionOfEquityFormComponent } from './evolution-of-equity-form.component';

describe('EvolutionOfEquityFormComponent', () => {
  let component: EvolutionOfEquityFormComponent;
  let fixture: ComponentFixture<EvolutionOfEquityFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ EvolutionOfEquityFormComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EvolutionOfEquityFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
