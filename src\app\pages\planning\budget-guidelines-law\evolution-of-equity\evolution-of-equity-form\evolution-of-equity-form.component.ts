import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { NbDialogRef } from '@nebular/theme'

@Component({
  selector: 'eqp-evolution-of-equity-form',
  templateUrl: './evolution-of-equity-form.component.html',
  styleUrls: ['./evolution-of-equity-form.component.scss'],
})
export class EvolutionOfEquityFormComponent implements OnInit {
  public model: FormGroup

  @Input() data: {
    especificacao: string
    administracao: number
    regimePrevidenciario: number
  }

  constructor(
    private formBuilder: FormBuilder,
    protected ref: NbDialogRef<EvolutionOfEquityFormComponent>,
  ) {}

  ngOnInit(): void {
    this.model = this.getNovoFormulario()
  }

  public getNovoFormulario() {
    return this.formBuilder.group({
      especificacao: [this.data.especificacao],
      administracao: [this.data.administracao, Validators.required],
      regimePrevidenciario: [
        this.data.regimePrevidenciario,
        Validators.required,
      ],
    })
  }

  public cancel() {
    this.ref.close(null)
  }

  public confirm() {
    this.ref.close(this.model.getRawValue())
  }
}
