<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [rightApproveButtonVisible]="true"
  [rightApproveButtonIconVisible]="true"
  [rightApproveButtonText]="'Salvar'"
  [rightApproveButtonIcon]="'fas fa-check'"
  [rightApproveButtonId]="'put-current-goals-compared'"
  [rightApproveButtonDisabled]="
    exercicioReferenciaUuid ||
    (nivelPermissao !== 'FULL' && nivelPermissao !== 'EDITOR')
  "
  (rightApproveButtonEmitter)="salvar()"
  [formGroup]="model"
>
  <div class="row">
    <div class="col col-12 col-md-2">
      <eqp-nebular-select
        [size]="'small'"
        [shape]="'rectangle'"
        name="exercicioReferencia"
        label="Exercício de referência"
        placeholder="Exercício de referência"
        valueExpr="uuid"
        displayExpr="exercicioReferencia"
        [dataSource]="exercicioReferenciaData"
        formControlName="evolutionOfEquityUuid"
      >
      </eqp-nebular-select>
    </div>
    <div class="col col-12 col-md-6 mt-4">
      <eqp-publication-note-field
        formControlName="publicacao"
      ></eqp-publication-note-field>
    </div>
  </div>
  <ng-container *ngIf="exercicioReferenciaUuid">
    <dx-data-grid
      id="EvolutionOfEquityGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [title]="'Exportar a tabela'"
        [textContent]="'texto'"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>

      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="false"
        placeholder="Buscar"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column dataField="especificacao" caption="Especificação">
      </dxi-column>

      <dxi-column alignment="center" [caption]="anoReferencia">
        <dxi-column
          dataField="administracao"
          caption="Administração"
          alignment="left"
          [format]="currencyFormat"
        >
        </dxi-column>
        <dxi-column
          dataField="regimePrevidenciario"
          caption="Regime previdênciário"
          alignment="left"
          [format]="currencyFormat"
        >
        </dxi-column>
      </dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="40"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          title="Alterar"
          (click)="alterar(data)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
