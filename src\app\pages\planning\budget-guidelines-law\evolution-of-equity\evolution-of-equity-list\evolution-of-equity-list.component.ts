import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { distinctUntilChanged, finalize, first } from 'rxjs/operators'
import {
  EquityEvolutionReport,
  EvolutionOfEquityInterface,
} from '../../interfaces/evolution-of-equity.model'
import { EvolutionOfEquityService } from '../../services/evolution-of-equity.service'
import { EvolutionOfEquityFormComponent } from '../evolution-of-equity-form/evolution-of-equity-form.component'
import { CALCULATED_FIELD_ID, loadForm, prepare } from '../helpers/helper'

@Component({
  selector: 'eqp-evolution-of-equity-list',
  templateUrl: './evolution-of-equity-list.component.html',
  styleUrls: ['./evolution-of-equity-list.component.scss'],
})
export class EvolutionOfEquityListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public pageTitle = 'Evolução do Patrimônio Líquido'
  public loading: boolean
  public model: FormGroup
  public exercicioReferenciaData: DataSource<EvolutionOfEquityInterface, string>
  public exercicioReferenciaUuid: string
  public dataSource: DataSource<EquityEvolutionReport, number>
  public anoReferencia: number
  private subscription: Subscription

  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  constructor(
    public menuService: MenuService,
    public router: Router,
    private formBuilder: FormBuilder,
    private service: EvolutionOfEquityService,
    private dialogService: NbDialogService,
    private toastrService: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('/lei-diretrizes-orcamentarias/evolucao-patrimonio-liquido')
  }

  ngOnInit(): void {
    this.model = this.getNovoFormulario()
    this.loadSelect()
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
  }

  public getNovoFormulario() {
    return this.formBuilder.group({
      evolutionOfEquityUuid: [''],
      publicacao: [],
    })
  }

  private criar() {
    this.loading = true
    this.service
      .postBatch([])
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.exercicioReferenciaData = new DataSource({
          store: {
            type: 'array',
            data: res.body.dados,
            key: 'uuid',
          },
        })
        this.toastrService.send({
          success: true,
          message: 'Evolução do patrimônio líquido criado com sucesso.',
        })
      })
  }

  public loadSelect() {
    this.loading = true
    this.service
      .getList()
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe((res: any) => {
        if (res.data.length) {
          this.exercicioReferenciaData = new DataSource({
            store: {
              type: 'array',
              data: res.data,
              key: 'uuid',
            },
          })
        } else {
          this.criar()
        }
        this.selectionHandler()
      })
  }

  public selectionHandler() {
    this.subscription = this.model
      .get('evolutionOfEquityUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(async exercicioUuid => {
        if (this.exercicioReferenciaUuid) {
          await this.exercicioReferenciaData
            .store()
            .update(
              this.exercicioReferenciaUuid,
              prepare(
                this.exercicioReferenciaUuid,
                this.anoReferencia,
                this.dataSource.items(),
                this.publicacao,
              ),
            )
        }
        this.exercicioReferenciaUuid = exercicioUuid
        let selectedExercise = await this.exercicioReferenciaData
          .store()
          .byKey(exercicioUuid)
        this.anoReferencia = selectedExercise.referenceExercise
        this.dataSource = new DataSource({
          store: {
            type: 'array',
            data: loadForm(selectedExercise),
            key: 'id',
          },
        })
      })
  }

  public alterar({ data }) {
    if (data.id == CALCULATED_FIELD_ID) {
      this.toastrService.send({
        error: true,
        message: 'Essa linha não pode ser alterada',
      })
    } else {
      const ref = this.dialogService.open(EvolutionOfEquityFormComponent, {
        context: {
          data: {
            especificacao: data.especificacao,
            administracao: data.administracao,
            regimePrevidenciario: data.regimePrevidenciario,
          },
        },
        closeOnBackdropClick: false,
        closeOnEsc: false,
      })
      ref.onClose.pipe(first()).subscribe(res => {
        if (res) {
          this.updateGridValues(res, data.id)
        }
      })
    }
  }

  private async updateGridValues(
    newValue: { administracao: number; regimePrevidenciario: number },
    id: number,
  ) {
    this.loading = true
    await this.dataSource.store().update(id, {
      administracao: newValue.administracao,
      regimePrevidenciario: newValue.regimePrevidenciario,
    })
    await this.dataSource.reload()
    this.loading = false
  }

  public salvar() {
    this.loading = true
    this.service
      .put(
        prepare(
          this.exercicioReferenciaUuid,
          this.anoReferencia,
          this.dataSource.items(),
          this.publicacao,
        ),
      )
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.loadSelect()
        this.toastrService.send({
          success: true,
          message: 'Evolução do Patrimônio Líquido atualizada com sucesso.',
        })
      })
  }

  public get publicacao() {
    return this.model.get('publicacao').value
  }

  public set publicacao(value) {
    this.model.get('publicacao').setValue(value)
  }
}
