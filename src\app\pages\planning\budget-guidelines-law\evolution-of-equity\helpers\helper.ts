import { PublicationNoteInterface } from '@pages/planning/shared/interfaces/publication-note';
import {
  EquityEvolutionReport,
  EvolutionOfEquityInterface,
} from '../../interfaces/evolution-of-equity.model'

export function loadForm(
  data: EvolutionOfEquityInterface,
): EquityEvolutionReport[] {
  return [
    {
      id: 0,
      especificacao: 'Patrimônio/Capital',
      administracao: data.vlrPatrimonio,
      regimePrevidenciario: data.vlrPatrimonioRegPrev,
    },
    {
      id: 1,
      especificacao: 'Reservas',
      administracao: data.vlrReservas,
      regimePrevidenciario: data.vlrReservasRegPrev,
    },
    {
      id: 2,
      especificacao: 'Resultado Acumulado',
      administracao: data.vlrResultadoAcumulado,
      regimePrevidenciario: data.vlrResultadoAcumuladoRegPrev,
    },
  ]
}

export function prepare(
  uuid: string,
  exercicioReferencia: number,
  data: EquityEvolutionReport[],
  publicacao: PublicationNoteInterface,
): EvolutionOfEquityInterface {
  data.sort((a, b) => a.id - b.id)
  return {
    uuid,
    exercicioReferencia,
    vlrPatrimonio: data[0].administracao,
    vlrPatrimonioRegPrev: data[0].regimePrevidenciario,
    vlrReservas: data[1].administracao,
    vlrReservasRegPrev: data[1].regimePrevidenciario,
    vlrResultadoAcumulado: data[2].administracao,
    vlrResultadoAcumuladoRegPrev: data[2].regimePrevidenciario,
    publicacao,
  }
}

export const CALCULATED_FIELD_ID = 4
export const PRIMARY_FIELDS = [1, 2, 3]
