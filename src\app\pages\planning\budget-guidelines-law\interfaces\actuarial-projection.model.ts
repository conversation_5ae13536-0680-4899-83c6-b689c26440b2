export interface ActuarialProjectionInterface {
  uuid?: string
  exercicioReferencia?: number
  flagPublicar?: string
  tipoPrevidenciaUuid?: string
  vlrDespesaPrevidenciaria?: number
  vlrReceitaPrevidenciaria?: number
  vlrSaldoFinanceiro?: number
}

export interface ActuarialProjectionReturnInterface {
  dados: ActuarialProjectionInterface[]
  sucesso: boolean
}

export interface ActuarialProjectionReport {
  id: number
  exercicioReferencia: number
  vlrReceitaPrevidenciaria: number
  vlrDespesaPrevidenciaria: number
  vlrSaldoFinanceiro: number
  tipoPrevidenciaUuid: string
}
