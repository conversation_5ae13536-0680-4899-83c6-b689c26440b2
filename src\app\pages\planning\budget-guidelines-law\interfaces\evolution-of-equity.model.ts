import { PublicationNoteInterface } from "@pages/planning/shared/interfaces/publication-note"

export interface EvolutionOfEquityInterface {
  uuid?: string
  referenceExercise?: number
  exercicioReferencia?: number
  vlrPatrimonio?: number
  vlrPatrimonioRegPrev?: number
  vlrReservas?: number
  vlrReservasRegPrev?: number
  vlrResultadoAcumulado?: number
  vlrResultadoAcumuladoRegPrev?: number
  publicacao?: PublicationNoteInterface
}

export interface EvolutionOfEquityReturnInterface {
  dados: EvolutionOfEquityInterface[]
  sucesso: boolean
}

export interface EquityEvolutionReport {
  id: number
  especificacao: string
  administracao: number
  regimePrevidenciario: number
}
