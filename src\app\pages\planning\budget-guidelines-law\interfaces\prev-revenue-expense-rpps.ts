import { PublicationNoteInterface } from '@pages/planning/shared/interfaces/publication-note'
import { PrevidencialTypeInterface } from './previdencial-type'

export interface PrevidencialRevenueExpenseRpps2018Interface {
  uuid: string
  exercicioReferencia: number
  tipoPrevidencia: PrevidencialTypeInterface
  vlrRecSegCivilAtivo: number
  vlrRecSegCivilInativo: number
  vlrRecSegCivilPensionista: number
  vlrRecSegMilitarAtivo: number
  vlrRecSegMilitarInativo: number
  vlrRecSegMilitarPensionista: number
  vlrRecCpatCivilAtivo: number
  vlrRecCpatCivilInativo: number
  vlrRecCpatCivilPensionista: number
  vlrRecCpatMilitarAtivo: number
  vlrRecCpatMilitarInativo: number
  vlrRecCpatMilitarPensionista: number
  vlrRecCpatRegimeParcDeb: number
  vlrRecPatRecImobiliaria: number
  vlrRecPatValImobiliario: number
  vlrRecPatOutrasReceitas: number
  vlrRecServicos: number
  vlrRecAportePeriodico: number
  vlrOutRecCompPrevidenciaria: number
  vlrOutRecDemaisReceitas: number
  vlrRecCapAlienacaoBens: number
  vlrRecCapAmortizacaoEmprest: number
  vlrRecCapOutrasReceita: number
  vlrRecursosArrecExeAnt: number
  vlrDespAdmDespCorrente: number
  vlrDespAdmDespCapital: number
  vlrDespPrevCivilAposentado: number
  vlrDespPrevCivilPensoes: number
  vlrDespPrevCivilOutros: number
  vlrDespPrevMilitarAposentado: number
  vlrDespPrevMilitarPensoes: number
  vlrDespPrevMilitarOutros: number
  vlrDespOutDespPrevidenciaria: number
  vlrDespOutDespDemaisDesp: number
  vlrDesPreservaOrcamentaria: number
  vlrAporteContPatronal: number
  vlrAportePeriodicos: number
  vlrAporteOutros: number
  vlrAporteCoberturaDefFinan: number
  vlrAporteCoberturaInsufFinan: number
  vlrAporteFormacaoReserva: number
  vlrBensDireitosCaixa: number
  vlrBensDireitosInvestimentos: number
  vlrBensDireitosOutros: number
  vlrRecCorrenteAdministracao: number
  publicacao: PublicationNoteInterface
}
