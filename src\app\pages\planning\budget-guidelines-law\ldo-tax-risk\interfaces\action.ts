export interface Action {
  uuid?: string
  descricao?: string
  flagPublicar?: string
  fonte?: string
  id?: number
  leiUuid?: string
  notaExplicativa?: string
  numeroProvidencia?: number
  vlrEstimadoProvidencia?:number
  vlrEstimadoRisco?:number
  riscoFiscalUuid?: {
    id?: number
    nome?: string
    riscoFiscalTceUuid?: {
      codigo?: number
      nome?: string
      uf?: string
      uuid?: string
    }
  }
}

export interface ReturnInterface {
  sucesso: boolean
  data: any
}

export interface ActionReturn extends ReturnInterface { }
