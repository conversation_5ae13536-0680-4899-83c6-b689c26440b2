<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
        <eqp-breadcrumb></eqp-breadcrumb>
      </div>

    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="ldoTaxRisk">
      <div class="col-xl-2 col-lg-3 mb-4">
        <eqp-nebular-input [size]="'small'" [showSuffixIcon]="true" [primarySuffixIcon]="true"
          [firstSuffixIcon]="'search'" formControlName="lei" name="lei" label="Lei/Ato" placeholder="Lei/Ato" disabled
          (click)="openDecreeSearch()" required></eqp-nebular-input>
      </div>
      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" formControlName="tipoDocumento"
          name="Tipo do Documento" label="Tipo do Documento" placeholder="Tipo do Documento" disabled>
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" formControlName="escopoDocumento"
          name="Escopo Documento" label="Escopo Documento" placeholder="Escopo Documento" disabled>
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" formControlName="numeroDocumento"
          name="Nº do Documento" label="Nº do Documento" placeholder="Nº do Documento" disabled>
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" formControlName="anoDocumento"
          name="Ano do Documento" label="Ano do Documento" placeholder="Ano do Documento" disabled>
        </eqp-nebular-input>
      </div>
    </div>
    <div class="row" formGroupName="ldoTaxRisk">
      <div [class]="(modal?'col-md-12': 'col-md-4') + ' col-sm-12 mb-4'">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Tipo do Risco Fiscal"
          label="Tipo do Risco Fiscal" placeholder="Tipo do Risco Fiscal"
          errorMessage="É obrigatório selecionar o tipo do risco fiscal" formControlName="riscoFiscalUuid"
          [dataSource]="riscoFiscalData" valueExpr="uuid" displayExpr="nome"
          [enableAddButton]="permissaoTipoRiscoFiscal" (onAddButtonClick)="openTipoRiscoFiscal()"></eqp-nebular-select>
      </div>
      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" [style]="'currency' "
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-left" [shape]="'rectangle'"
          [type]="'decimal'" formControlName="vlrEstimadoRisco" name="Valor Estimado" label="Valor Estimado"
          placeholder="Valor Estimado" errorMessage="É obrigatório preencher o Valor Estimado" minlength="1"
          maxlength="18" required="true">
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [size]="'small'" [style]="'currency' "
          [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' } " class="text-left" [shape]="'rectangle'"
          [type]="'decimal'" formControlName="vlrEstimadoProvidencia" name="Valor Estimado das Providências"
          label="Valor Estimado das Providências" placeholder="Valor Estimado das Providências"
          errorMessage="É obrigatório preencher o Valor Estimado das Providências" minlength="1" maxlength="18"
          required="true">
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-2' + ' col-sm-12 mb-4'">
        <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" [type]="'number'"
          formControlName="numeroProvidencia" name="Nrº Providência" label="Nrº Providência"
          placeholder="Nrº Providência" errorMessage="É obrigatório preencher o Numero da Providência" disabled>
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-8'">
        <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" formControlName="descricao"
          name="Descrição das Providências" label="Descrição das Providências" placeholder="Descrição das Providências"
          errorMessage="É obrigatório preencher a descrição" maxlength="250" required="true">
        </eqp-nebular-input>
      </div>
      <div [class]="'col-md-4' + ' col-sm-12 mt-3 pt-2'">
        <eqp-publication-note-field
          formControlName="publicacao"
        ></eqp-publication-note-field>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button *ngIf="formulario.get('ldoTaxRisk.uuid').value && nivelPermissao === 'FULL'" type="button"
          class="btn btn-danger ml-3 float-md-right" (click)="remover()">Apagar</button>
        <button
          *ngIf="formulario.get('ldoTaxRisk.uuid').value && nivelPermissao === 'EDITOR' || nivelPermissao === 'FULL'  "
          type="button" class="btn btn-success float-md-right" (click)="gravar()">Gravar</button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">

</nb-card>

<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-body [formGroup]="formulario">
    <ng-container>
      <dx-data-grid id="ldoTaxRiskGrid" [dataSource]="passivosContingentes" [allowColumnResizing]="false"
        [columnAutoWidth]="false" [showColumnLines]="false" [showRowLines]="false" [showBorders]="false"
        [rowAlternationEnabled]="true" [wordWrapEnabled]="true" [loadPanel]="false" [columnHidingEnabled]="true"
        [remoteOperations]="true" keyExpr="uuid" (onRowRemoving)="onRowRemoving($event)"
        (onRowUpdated)="onRowUpdated($event)" (onRowUpdating)="onRowUpdating($event)">
        <dxo-state-storing [enabled]="true" type="custom" [customLoad]="loadState" [customSave]="saveState"
          savingTimeout="100"></dxo-state-storing>

        <dxo-paging [pageSize]="10"></dxo-paging>
        <dxo-pager [showInfo]="true" [showNavigationButtons]="true" [showPageSizeSelector]="false">
        </dxo-pager>

        <dxo-header-filter [visible]="false">
        </dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>

        <dxo-sorting mode="multiple"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxi-column caption="Passivos Contigentes" alignment="center">
          <dxi-column dataField="riscoFiscalUuid.nome" caption="Descrição"
            [format]="{ style: 'currency', currency: 'BRL', useGrouping: true,  maximumFractionDigit: 2 }"
            [allowEditing]="false">
          </dxi-column>
          <dxi-column dataField="vlrEstimadoRisco" caption="Valor" width="190"
            [format]="{ style: 'currency', currency: 'BRL', useGrouping: true,  maximumFractionDigit: 2 }">
          </dxi-column>
        </dxi-column>
        <dxi-column caption="Providências" alignment="center">
          <dxi-column dataField="descricao" caption="Descrição">
          </dxi-column>
          <dxi-column dataField="vlrEstimadoProvidencia" caption="Valor" width="190"
            [format]="{ style: 'currency', currency: 'BRL', useGrouping: true, maximumFractionDigit: 2 }">
          </dxi-column>
        </dxi-column>
        <dxo-editing mode="row" [allowUpdating]="true" [allowAdding]="false" [allowDeleting]="true"
          [confirmDelete]="false">
        </dxo-editing>
      </dx-data-grid>

      <div class="row m-0 border-top border-bottom" [ngStyle]="{'border-color': '#e0e0e0'}">
        <div class="m-0 pl-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '38%'}">
          <p class="tabela p-2 m-0" [ngStyle]="{'color': 'currentColor'}" >Subtotal</p>
        </div>
        <div class="m-0 pr-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '12%'}">
          <p class="text-right p-2 m-0" [ngStyle]="{'color': 'currentColor'}">{{subtotalPassivosRisco | currency:
            'BRL'}}</p>
        </div>
        <div class="m-0 pl-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '38%'}">
          <p class="p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">Subtotal</p>
        </div>
        <div class="m-0 pr-1" [ngStyle]="{'width': '12%'}">
          <p class="text-right p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">{{subtotalPassivosProvidencia |
            currency: 'BRL'}}</p>
        </div>
      </div>

    </ng-container>
  </nb-card-body>
</nb-card>


<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-body [formGroup]="formulario">
    <ng-container>
      <dx-data-grid id="ldoTaxRiskGrid" [dataSource]="demaisRiscosFiscais" [allowColumnResizing]="false"
        [columnAutoWidth]="false" [showColumnLines]="false" [showRowLines]="false" [showBorders]="false"
        [rowAlternationEnabled]="true" [wordWrapEnabled]="true" [loadPanel]="false" [columnHidingEnabled]="true"
        [remoteOperations]="true" keyExpr="uuid" (onRowRemoving)="onRowRemoving($event)"
        (onRowUpdated)="onRowUpdated($event)" (onRowUpdating)="onRowUpdating($event)">

        <dxo-state-storing [enabled]="true" type="custom" [customLoad]="loadState" [customSave]="saveState"
          savingTimeout="100"></dxo-state-storing>

        <dxo-paging [enabled]="true" [pageSize]="30" [pageIndex]="0"></dxo-paging>

        <dxo-pager [showInfo]="true" [showNavigationButtons]="true" [showPageSizeSelector]="false">
        </dxo-pager>

        <dxo-header-filter [visible]="false">
        </dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>

        <dxo-sorting mode="multiple"></dxo-sorting>

        <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

        <dxi-column caption="Demais Riscos Fiscais Passivos" alignment="center">
          <dxi-column dataField="riscoFiscalUuid.nome" caption="Descrição" [allowEditing]="false">
          </dxi-column>
          <dxi-column dataField="vlrEstimadoRisco" caption="Valor" width="190"
            [format]="{ style: 'currency', currency: 'BRL', useGrouping: true, maximumFractionDigits: 2 }">
          </dxi-column>
        </dxi-column>
        <dxi-column caption="Providências" alignment="center">
          <dxi-column dataField="descricao" caption="Descrição">
          </dxi-column>
          <dxi-column dataField="vlrEstimadoProvidencia" caption="Valor" width="190"
            [format]="{ style: 'currency', currency: 'BRL', useGrouping: true, maximumFractionDigits: 2 }">
          </dxi-column>
        </dxi-column>
        <dxo-editing mode="row" [allowUpdating]="true" [allowAdding]="false" [allowDeleting]="true"
          [confirmDelete]="false">
        </dxo-editing>
      </dx-data-grid>


      <div class="row m-0 border-top border-bottom" [ngStyle]="{'border-color': '#e0e0e0'}">
        <div class="m-0 pl-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '38%'}">
          <p class="p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">Subtotal</p>
        </div>
        <div class="m-0 pr-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '12%'}">
          <p class="text-right p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">{{subtotalDemaisRisco | currency:
            'BRL'}}</p>
        </div>
        <div class="m-0 pl-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '38%'}">
          <p class="p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">Subtotal</p>
        </div>
        <div class="m-0 pr-1" [ngStyle]="{'width': '12%'}">
          <p class="text-right p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">{{subtotalDemaisProvidencia |
            currency: 'BRL'}}</p>
        </div>
      </div>
      <br>
      <div class="row m-0 border-top border-bottom" [ngStyle]="{'border-color': '#e0e0e0'}">
        <div class="m-0 pl-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '38%'}">
          <p class="p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">Total</p>
        </div>
        <div class="m-0 pr-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '12%'}">
          <p class="text-right p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">{{totalRiscosFiscais | currency:
            'BRL'}}</p>
        </div>
        <div class="m-0 pl-1 border-right" [ngStyle]="{'border-color': '#e0e0e0', 'width': '38%'}">
          <p class="p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">Total</p>
        </div>
        <div class="m-0 pr-1" [ngStyle]="{'width': '12%'}">
          <p class="text-right p-2 m-0" [ngStyle]="{'color': 'currentcolor'}">{{totalProvidencias| currency:
            'BRL'}}</p>
        </div>
      </div>
    </ng-container>
  </nb-card-body>
</nb-card>
