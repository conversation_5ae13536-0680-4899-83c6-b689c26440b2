import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { DecreeSearchComponent } from '@dialogs/decree-search/decree-search.component'
import { ModalConfirmarComponent } from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { EditorDocComponent } from './../../../../../@common/dialogs/editor-doc/editor-doc.component'
import { DialogTaxRiskComponent } from './../../tax-risk/dialog-tax-risk-form/dialog-tax-risk-form.component'

import { Subscription } from 'rxjs'
import { Action, ActionReturn } from '../interfaces/action'
import { LdoTaxRiskService } from '../ldo-tax-risk.service'
import { ToastrService } from './../../../../../@common/services/toastr/toastr.service'
import { MenuService } from './../../../../menu.service'
import { PublicationNoteInterface } from '@pages/planning/shared/interfaces/publication-note'

@Component({
  selector: 'eqp-ldo-tax-risk-form',
  templateUrl: './ldo-tax-risk-form.component.html',
  styleUrls: ['./ldo-tax-risk-form.component.scss'],
})
export class LdoTaxRiskFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public gridData: any
  public loading: boolean = false
  public pageTitle: string = 'Risco fiscal'
  public formulario: FormGroup
  public riscoFiscalData: any
  public subtotalPassivosRisco: number
  public subtotalPassivosProvidencia: number
  public subtotalDemaisRisco: number
  public subtotalDemaisProvidencia: number
  public totalRiscosFiscais: number
  public totalProvidencias: number
  public passivosContingentes: Action[] = []
  public demaisRiscosFiscais: Action[] = []
  private subscription: Subscription
  public permissaoTipoRiscoFiscal: boolean = false
  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<LdoTaxRiskFormComponent>

  constructor(
    private formBuilder: FormBuilder,
    private service: LdoTaxRiskService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/risco-fiscal')
  }

  public ngOnInit(): void {
    this.gravar
    this.fetchGrid()
    this.formulario = this.getNovoFormulario()

    this.carregarTela()
    this.permissaoModal('/tipo-risco-fiscal/novo')
      .pipe(first())
      .subscribe(dados => {
        this.permissaoTipoRiscoFiscal = dados.dados === 'FULL'
      })
    this.carregarTela()
  }

  public ngOnDestroy(): void {}

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      ldoTaxRisk: this.formBuilder.group({
        uuid: [''],
        lei: ['', Validators.required],
        leiUuid: ['test-uuid'],
        tipoDocumento: [null, Validators.required],
        escopoDocumento: [null, Validators.required],
        numeroDocumento: [null, Validators.required],
        anoDocumento: [null, Validators.required],
        flagPublicar: [null],
        riscoFiscalUuid: [null, Validators.required],
        vlrEstimadoRisco: [null, Validators.required],
        vlrEstimadoProvidencia: [null, Validators.required],
        numeroProvidencia: [null],
        descricao: [null, Validators.required],
        fonte: [null],
        notaExplicativa: [null],
        publicacao: []
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid && !this.modal) this.buscar(uuid)
      else this.loadSelects()
    })
  }

  private fetchGrid(): void {
    if (this.formulario && this.formulario.get('ldoTaxRisk.leiUuid').value) {
      this.subscription = this.service
        .getRiscoFiscalPassivosContingentes(
          this.formulario.get('ldoTaxRisk.leiUuid').value,
        )
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          (data: ActionReturn) => {
            this.passivosContingentes = data.data
          },
        )

      this.subscription = this.service
        .getRiscoFiscalDemaisRiscosFiscais(
          this.formulario.get('ldoTaxRisk.leiUuid').value,
        )
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          (data: ActionReturn) => {
            this.demaisRiscosFiscais = data.data
          },
        )

      setTimeout(() => {
        this.subtotalPassivosRisco = 0
        this.subtotalPassivosProvidencia = 0

        for (var i = 0; i < this.passivosContingentes.length; i++) {
          this.subtotalPassivosRisco +=
            this.passivosContingentes[i].vlrEstimadoRisco
          this.subtotalPassivosProvidencia +=
            this.passivosContingentes[i].vlrEstimadoProvidencia
        }

        this.subtotalDemaisRisco = 0
        this.subtotalDemaisProvidencia = 0
        for (var i = 0; i < this.demaisRiscosFiscais.length; i++) {
          this.subtotalDemaisRisco +=
            this.demaisRiscosFiscais[i].vlrEstimadoRisco
          this.subtotalDemaisProvidencia +=
            this.demaisRiscosFiscais[i].vlrEstimadoProvidencia
        }

        this.totalRiscosFiscais =
          this.subtotalPassivosRisco + this.subtotalDemaisRisco
        this.totalProvidencias =
          this.subtotalPassivosProvidencia + this.subtotalDemaisProvidencia
      }, 1000)
    }
  }

  private loadSelects(): void {
    this.riscoFiscalData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'risco_fiscal/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('ldoTaxRisk').patchValue(data.dados)
        this.loadSelects()
      })
  }

  public cancelar(retorno): void {
    if (!this.modal) {
      this.gravarParametros()
      this.router.navigate([`risco_fiscal`])
    } else {
      this.ref.close(retorno)
    }
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.formulario.get('ldoTaxRisk.uuid').value)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Risco fiscal excluído com sucesso.',
              })
              this.cancelar(null)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      if (this.formulario.get('ldoTaxRisk.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getRiscoFiscalDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Risco fiscal criado com sucesso.',
        })
        this.formulario.get('ldoTaxRisk.flagPublicar').reset()
        this.formulario.get('ldoTaxRisk.riscoFiscalUuid').reset()
        this.formulario.get('ldoTaxRisk.vlrEstimadoRisco').reset()
        this.formulario.get('ldoTaxRisk.vlrEstimadoProvidencia').reset()
        this.formulario.get('ldoTaxRisk.numeroProvidencia').reset()
        this.formulario.get('ldoTaxRisk.descricao').reset()
        this.fetchGrid()
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getRiscoFiscalDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Risco fiscal atualizado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private getRiscoFiscalDto(): any {
    const dto = this.formulario.getRawValue()
    const {ldoTaxRisk} = dto
    ldoTaxRisk.publicacao = {
      ...ldoTaxRisk.publicacao,
      flagPublicar: ldoTaxRisk.publicacao?.flagPublicar ? ldoTaxRisk.publicacao?.flagPublicar : 'N',
      leiUuid: ldoTaxRisk.leiUuid
    }

    return dto.ldoTaxRisk
  }

  private setPublicationFields(data: any[]){
    if(data.length > 0 && data[0] && data[0].publicacao){
      this.formulario.get('ldoTaxRisk.publicacao').patchValue(data[0].publicacao)
    }
  }

  public openDecreeSearch(): void {
    const dialogRef = this.dialogService.open(DecreeSearchComponent, {
      context: { codigoEscopo: [2], url: 'risco_fiscal_ldo/lei/paginado' },
    })

    dialogRef.onClose.subscribe(decree => {
      if (decree) {
        this.formulario.get('ldoTaxRisk.publicacao').reset()
        this.formulario.get('ldoTaxRisk.leiUuid').patchValue(decree.uuid)
        this.formulario
          .get('ldoTaxRisk.lei')
          .patchValue(`${decree.numero} / ${decree.ano}`)
        this.formulario
          .get('ldoTaxRisk.tipoDocumento')
          .patchValue(decree.tipoDocumento.nome)
        this.formulario
          .get('ldoTaxRisk.escopoDocumento')
          .patchValue(decree.escopoDocumentoTce.nome)
        this.formulario
          .get('ldoTaxRisk.numeroDocumento')
          .patchValue(decree.numero)
        this.formulario.get('ldoTaxRisk.anoDocumento').patchValue(decree.ano)

        this.subscription = this.service
          .getRiscoFiscalPassivosContingentes(decree.uuid)
          .pipe(finalize(() => (this.loading = false)))
          .subscribe(
            (data: ActionReturn) => {
              this.setPublicationFields(data.data)
              this.passivosContingentes = data.data
            },
          )

        this.subscription = this.service
          .getRiscoFiscalDemaisRiscosFiscais(decree.uuid)
          .pipe(finalize(() => (this.loading = false)))
          .subscribe(
            (data: ActionReturn) => {
              this.setPublicationFields(data.data)
              this.demaisRiscosFiscais = data.data
            },
          )

        setTimeout(() => {
          this.subtotalPassivosRisco = 0
          this.subtotalPassivosProvidencia = 0

          for (var i = 0; i < this.passivosContingentes.length; i++) {
            this.subtotalPassivosRisco +=
              this.passivosContingentes[i].vlrEstimadoRisco
            this.subtotalPassivosProvidencia +=
              this.passivosContingentes[i].vlrEstimadoProvidencia
          }

          this.subtotalDemaisRisco = 0
          this.subtotalDemaisProvidencia = 0
          for (var i = 0; i < this.demaisRiscosFiscais.length; i++) {
            this.subtotalDemaisRisco +=
              this.demaisRiscosFiscais[i].vlrEstimadoRisco
            this.subtotalDemaisProvidencia +=
              this.demaisRiscosFiscais[i].vlrEstimadoProvidencia
          }

          this.totalRiscosFiscais =
            this.subtotalPassivosRisco + this.subtotalDemaisRisco
          this.totalProvidencias =
            this.subtotalPassivosProvidencia + this.subtotalDemaisProvidencia
        }, 1000)
      }
    })
  }

  public openTipoRiscoFiscal(): void {
    const dialogRef = this.dialogService.open(DialogTaxRiskComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        this.formulario.get('ldoTaxRisk.riscoFiscalUuid').patchValue(retorno)
      }
    })
  }

  public abrirEditorDocumentoFonte(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.formulario.get('ldoTaxRisk.fonte').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.formulario.get('ldoTaxRisk.fonte').patchValue(retorno)
    })
  }

  public abrirEditorDocumentoNotaExplicativa(): void {
    const dialogRef = this.dialogService.open(EditorDocComponent, {
      context: {
        dados: this.formulario.get('ldoTaxRisk.notaExplicativa').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      this.formulario.get('ldoTaxRisk.notaExplicativa').patchValue(retorno)
    })
  }

  public onRowRemoving(event: any): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(event.data.uuid)
          .pipe(first())
          .subscribe(() => {
            this.toastr.send({
              success: true,
              message: 'Risco fiscal excluído com sucesso.',
            })
            this.fetchGrid()
          })
      }
    })
  }

  public onRowUpdating(event: any): void {
    this.subscription = this.service
      .putRiscoFiscal(this.getDto(event))
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: `Risco fiscal atualizado com sucesso.`,
          })
          this.fetchGrid()
        },
        (err: any) => {
          this.toastr.send({
            error: true,
            message: err.error?.causa?.descricao || err?.error?.causa?.message,
          })
          this.fetchGrid()
        },
      )
  }

  public onRowUpdated(event: any): void {
    this.fetchGrid()
  }

  private getDto(event: any): Action {
    const { descricao, vlrEstimadoProvidencia, vlrEstimadoRisco } =
      event.data || event.newData

    const dto = {
      uuid: event.oldData?.uuid,
      descricao: descricao || event.oldData?.descricao,
      flagPublicar: event.oldData?.flagPublicar,
      fonte: event.oldData?.fonte,
      leiUuid: event.oldData?.leiUuid,
      notaExplicativa: event.oldData?.notaExplicativa,
      riscoFiscalUuid: event.oldData?.riscoFiscalUuid.uuid,
      vlrEstimadoProvidencia:
        vlrEstimadoProvidencia || event.oldData?.vlrEstimadoProvidencia,
      vlrEstimadoRisco: vlrEstimadoRisco || event.oldData?.vlrEstimadoRisco,
    }

    return dto
  }
}
