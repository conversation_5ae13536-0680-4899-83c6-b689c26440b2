import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'

import { BaseService } from './../../../../@common/services/base/base.service'

@Injectable({
  providedIn: 'root',
})
export class LdoTaxRiskService extends BaseService<any, any> {
  constructor(private httpParameter: HttpClient) {
    super(httpParameter, 'risco_fiscal_ldo')
  }

  public getRiscoFiscalPassivosContingentes(uuid: String): Observable<any> {
    return this.http.get<any>(`risco_fiscal_ldo/paginado/passivos?filter=["decreeUuid","=",${uuid}]`, { params: { take: '30'} })
  }
  public getRiscoFiscalDemaisRiscosFiscais(uuid: String): Observable<any> {
    return this.http.get<any>(`risco_fiscal_ldo/paginado/demais?filter=["decreeUuid","=",${uuid}]`, { params: { take: '30' } })
  }

  public putRiscoFiscal(data: any): Observable<any> {
    return this.http.put<any>(`risco_fiscal_ldo/${data.uuid}`, data)
  }
}
