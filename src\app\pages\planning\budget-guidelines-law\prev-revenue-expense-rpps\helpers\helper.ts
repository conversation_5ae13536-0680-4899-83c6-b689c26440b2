import { CalculatedFieldsInterface } from "../interfaces/calculated-fields-interface"

export function getCalculatedFields(): CalculatedFieldsInterface {
	return {
		tela1: {
			resultado: 0,
			totalReceita: 0,
			totalDespesa: 0,
			recCorrente: {
				vlr: 0,
				seguro: {
					vlr: 0,
					civil: 0,
					militar: 0,
				},
				patronal: {
					vlr: 0,
					civil: 0,
					militar: 0,
				},
				patrimonial: 0,
				outras: 0,
			},
			recCapital: 0,
			despesa: {
				civil: 0,
				militar: 0,
				outras: 0
			}
		},
		tela2: {
			totalReceita: 0,
			totalDespesa: 0,
			resultado: 0
		}
	}
}

export function calcAdminResultado(calc: CalculatedFieldsInterface, formData) {
	return calc.tela2.resultado =
		calcTotalAdminReceita(calc, formData) -
		calcTotalAdminDespesa(calc, formData)
}

function calcTotalAdminReceita(calc: CalculatedFieldsInterface, {vlrRecCorrenteAdministracao}) {
	return calc.tela2.totalReceita = vlrRecCorrenteAdministracao
}

function calcTotalAdminDespesa(calc: CalculatedFieldsInterface, {
	vlrDespAdmDespCorrente,
	vlrDespAdmDespCapital
}) {
	return calc.tela2.totalDespesa =
		vlrDespAdmDespCorrente +
		vlrDespAdmDespCapital
}

export function calcResultado(calc: CalculatedFieldsInterface, formData) {
	const r = calcTotalReceita(calc, formData)
	const d = calcTotalDespesa(calc, formData)
	return calc.tela1.resultado = r - d
}

function calcTotalDespesa(calc: CalculatedFieldsInterface, {
	vlrDespPrevCivilAposentado,
	vlrDespPrevCivilPensoes,
	vlrDespPrevCivilOutros,
	vlrDespPrevMilitarAposentado,
	vlrDespPrevMilitarPensoes,
	vlrDespPrevMilitarOutros,
	vlrDespOutDespPrevidenciaria,
	vlrDespOutDespDemaisDesp,
}) {
	calc.tela1.despesa.civil =
		vlrDespPrevCivilAposentado +
		vlrDespPrevCivilPensoes +
		vlrDespPrevCivilOutros
	calc.tela1.despesa.militar =
		vlrDespPrevMilitarAposentado +
		vlrDespPrevMilitarPensoes +
		vlrDespPrevMilitarOutros
	calc.tela1.despesa.outras =
		vlrDespOutDespPrevidenciaria +
		vlrDespOutDespDemaisDesp
	return calc.tela1.totalDespesa =
		calc.tela1.despesa.civil +
		calc.tela1.despesa.militar +
		calc.tela1.despesa.outras
}

function calcTotalReceita(calc: CalculatedFieldsInterface, formData) {
	return calc.tela1.totalReceita =
		calcRecCorrentes(calc, formData) +
		calcRecCapital(calc, formData)
}

function calcRecCorrentes(calc: CalculatedFieldsInterface, formData) {
	const {vlrRecServicos} = formData
	return calc.tela1.recCorrente.vlr =
		calcRecSeguro(calc, formData) +
		calcRecPatronal(calc, formData) +
		calcRecPatrimonial(calc, formData) +
		// calcRecPatrimonial(calc, formData) +
		vlrRecServicos +
		calcRecOutras(calc, formData)
}

function calcRecOutras(calc: CalculatedFieldsInterface, {
	vlrOutRecCompPrevidenciaria,
	vlrRecAportePeriodico,
	vlrOutRecDemaisReceitas,
}) {
	return calc.tela1.recCorrente.outras =
		vlrOutRecCompPrevidenciaria + vlrOutRecDemaisReceitas - vlrRecAportePeriodico
	// acho que eh isso
}

function calcRecPatrimonial(calc: CalculatedFieldsInterface, {
	vlrRecPatRecImobiliaria,
	vlrRecPatValImobiliario,
	vlrRecPatOutrasReceitas,
}) {
	return calc.tela1.recCorrente.patrimonial =
		vlrRecPatRecImobiliaria + vlrRecPatValImobiliario + vlrRecPatOutrasReceitas
}

function calcRecSeguro(calc: CalculatedFieldsInterface, {
	vlrRecSegCivilAtivo,
	vlrRecSegCivilInativo,
	vlrRecSegCivilPensionista,
	vlrRecSegMilitarAtivo,
	vlrRecSegMilitarInativo,
	vlrRecSegMilitarPensionista,
}) {
	calc.tela1.recCorrente.seguro.civil =
		vlrRecSegCivilAtivo + vlrRecSegCivilInativo + vlrRecSegCivilPensionista
	calc.tela1.recCorrente.seguro.militar =
		vlrRecSegMilitarAtivo + vlrRecSegMilitarInativo + vlrRecSegMilitarPensionista
	return calc.tela1.recCorrente.seguro.vlr =
		calc.tela1.recCorrente.seguro.civil +
		calc.tela1.recCorrente.seguro.militar
}

function calcRecPatronal(calc: CalculatedFieldsInterface, {
	vlrRecCpatCivilAtivo,
	vlrRecCpatCivilInativo,
	vlrRecCpatCivilPensionista,
	vlrRecCpatMilitarAtivo,
	vlrRecCpatMilitarInativo,
	vlrRecCpatMilitarPensionista,
}) {
	calc.tela1.recCorrente.patronal.civil =
		vlrRecCpatCivilAtivo + vlrRecCpatCivilInativo + vlrRecCpatCivilPensionista
	calc.tela1.recCorrente.patronal.militar =
		vlrRecCpatMilitarAtivo + vlrRecCpatMilitarInativo + vlrRecCpatMilitarPensionista
	return calc.tela1.recCorrente.patronal.vlr =
	calc.tela1.recCorrente.patronal.civil +
	calc.tela1.recCorrente.patronal.militar
}

function calcRecCapital(calc: CalculatedFieldsInterface, {
	vlrRecCapAlienacaoBens,
	vlrRecCapAmortizacaoEmprest,
	vlrRecCapOutrasReceita,
}) {
	return calc.tela1.recCapital =
		vlrRecCapAlienacaoBens +
		vlrRecCapAmortizacaoEmprest +
		vlrRecCapOutrasReceita
}
