<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [rightApproveButtonText]="'Salvar'"
  [rightApproveButtonVisible]="true"
  (rightApproveButtonEmitter)="update()"
  [rightApproveButtonDisabled]="
    !enableEditing ||
    (models[0].pristine &&
      models[1].pristine &&
      models[2].pristine &&
      publication.pristine)
  "
  rightApproveButtonId="alienation-resource-app-origin-submit-button"
>
  <div class="container" [formGroup]="model">
    <div class="row mt-2 mb-4 align-items-end">
      <div class="col col-md-4 d-flex flex-column">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          label="Previdência"
          placeholder="Previdência"
          formControlName="tipoPrevidencia"
          [dataSource]="tipoPrevidenciaData"
          [displayExpr]="'nome'"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col">
        <eqp-publication-note-field
          [formControl]="publication"
        ></eqp-publication-note-field>
      </div>
    </div>
  </div>
  <ng-container *ngIf="selectedPrevType">
    <div class="container">
      <div>
        <nb-tabset>
          <nb-tab tabTitle="Tela I">
            <nb-accordion>
              <nb-accordion-item>
                <nb-accordion-item-header
                  >Receitas Correntes
                  {{
                    selectedPrevType.nome == 'Plano previdenciário'
                      ? '(I)'
                      : '(VII)'
                  }}</nb-accordion-item-header
                >
                <nb-accordion-item-body>
                  <div class="table-container">
                    <table
                      class="dx-datagrid-table dx-datagrid-table-fixed"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <th class="w-md-35"></th>
                          <th>{{ exercise_1 }}</th>
                          <th>{{ exercise_2 }}</th>
                          <th>{{ exercise_3 }}</th>
                        </tr>
                        <tr>
                          <td>
                            Receitas correntes
                            {{
                              selectedPrevType.nome == 'Plano previdenciário'
                                ? '(I)'
                                : '(VII)'
                            }}
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">
                            Receita de Contribuição dos Segurados
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.seguro.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.seguro.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.seguro.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-4">Civil</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.seguro.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.seguro.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.seguro.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Ativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecSegCivilAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecSegCivilAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecSegCivilAtivo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Inativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecSegCivilInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecSegCivilInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecSegCivilInativo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Pensionista</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecSegCivilPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecSegCivilPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecSegCivilPensionista')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-4">Militar</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.seguro.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.seguro.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.seguro.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Ativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecSegMilitarAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecSegMilitarAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecSegMilitarAtivo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Inativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecSegMilitarInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecSegMilitarInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecSegMilitarInativo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Pensionista</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecSegMilitarPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecSegMilitarPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecSegMilitarPensionista')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">
                            Receita de Contribuição Patronais
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.patronal.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.patronal.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.patronal.vlr
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-4">Civil</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.patronal.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.patronal.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.patronal.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Ativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCpatCivilAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCpatCivilAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCpatCivilAtivo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Inativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCpatCivilInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCpatCivilInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCpatCivilInativo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Pensionista</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCpatCivilPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCpatCivilPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCpatCivilPensionista')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-4">Militar</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.patronal.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.patronal.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.patronal.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Ativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCpatMilitarAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCpatMilitarAtivo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCpatMilitarAtivo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Inativo</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCpatMilitarInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCpatMilitarInativo')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCpatMilitarInativo')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-3 pl-md-5">Pensionista</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCpatMilitarPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCpatMilitarPensionista')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCpatMilitarPensionista')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-1 pl-md-3">Receita Patrimonial</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.patrimonial
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.patrimonial
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.patrimonial
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-4">Receitas Imobiliárias</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecPatRecImobiliaria')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecPatRecImobiliaria')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecPatRecImobiliaria')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-4">
                            Receitas de Valores Mobiliários
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecPatValImobiliario')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecPatValImobiliario')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecPatValImobiliario')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-4">
                            Outras Receitas Patrimoniais
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecPatOutrasReceitas')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecPatOutrasReceitas')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecPatOutrasReceitas')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-1 pl-md-3">Receitas de Serviços</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="models[0].get('vlrRecServicos')"
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="models[1].get('vlrRecServicos')"
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="models[2].get('vlrRecServicos')"
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-1 pl-md-3">
                            Outras Receitas Correntes
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCorrente.outras
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCorrente.outras
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCorrente.outras
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-4">
                            Compensação Previdenciária do RGPS
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrOutRecCompPrevidenciaria')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrOutRecCompPrevidenciaria')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrOutRecCompPrevidenciaria')
                              "
                            />
                          </td>
                        </tr>
                        <ng-container
                          *ngIf="
                            selectedPrevType.nome == 'Plano previdenciário'
                          "
                        >
                          <tr>
                            <td class="pl-2 pl-md-4">
                              Aportes Periódicos para Amortização de Déficit
                              Atuarial do RPPS (II)¹
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrRecAportePeriodico')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrRecAportePeriodico')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrRecAportePeriodico')
                                "
                              />
                            </td>
                          </tr>
                        </ng-container>
                        <tr>
                          <td class="pl-2 pl-md-4">
                            Demais Receitas Correntes
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrOutRecDemaisReceitas')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrOutRecDemaisReceitas')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrOutRecDemaisReceitas')
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
              <nb-accordion-item>
                <nb-accordion-item-header
                  >Receitas de Capital
                  {{
                    selectedPrevType.nome == 'Plano previdenciário'
                      ? '(III)'
                      : '(VIII)'
                  }}</nb-accordion-item-header
                >
                <nb-accordion-item-body>
                  <div class="table-container">
                    <table
                      class="dx-datagrid-table dx-datagrid-table-fixed"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <th class="w-md-35"></th>
                          <th>{{ exercise_1 }}</th>
                          <th>{{ exercise_2 }}</th>
                          <th>{{ exercise_3 }}</th>
                        </tr>
                        <tr>
                          <td>
                            Receitas de capital
                            {{
                              selectedPrevType.nome == 'Plano previdenciário'
                                ? '(III)'
                                : '(VIII)'
                            }}
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.recCapital | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.recCapital | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.recCapital | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-2">
                            Alienação de Bens, Direitos e Ativos
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCapAlienacaoBens')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCapAlienacaoBens')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCapAlienacaoBens')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-2">
                            Amortização de Empréstimos
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCapAmortizacaoEmprest')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCapAmortizacaoEmprest')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCapAmortizacaoEmprest')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-2 pl-md-2">
                            Outras Receitas de Capital
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrRecCapOutrasReceita')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrRecCapOutrasReceita')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrRecCapOutrasReceita')
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
              <nb-accordion-item [disabled]="true">
                <nb-accordion-item-header>
                  <div>
                    <table
                      class="dx-datagrid-table dx-datagrid-table-fixed"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <th class="w-md-35"></th>
                          <th>{{ exercise_1 }}</th>
                          <th>{{ exercise_2 }}</th>
                          <th>{{ exercise_3 }}</th>
                        </tr>
                        <tr>
                          <td>
                            Total das Receitas Previdenciárias RPPS
                            {{
                              selectedPrevType.nome == 'Plano previdenciário'
                                ? '(IV) = (I + III - II)'
                                : '(IX) = (VII + VIII)'
                            }}
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.totalReceita
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.totalReceita
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.totalReceita
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </nb-accordion-item-header>
              </nb-accordion-item>
              <nb-accordion-item>
                <nb-accordion-item-header>Despesas</nb-accordion-item-header>
                <nb-accordion-item-body>
                  <div class="table-container">
                    <table
                      class="dx-datagrid-table dx-datagrid-table-fixed"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <th class="w-md-35"></th>
                          <th>{{ exercise_1 }}</th>
                          <th>{{ exercise_2 }}</th>
                          <th>{{ exercise_3 }}</th>
                        </tr>
                        <tr>
                          <td>Benefícios - Civil</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.despesa.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.despesa.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.despesa.civil
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">Aposentadorias</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespPrevCivilAposentado')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespPrevCivilAposentado')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespPrevCivilAposentado')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">Pensões</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespPrevCivilPensoes')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespPrevCivilPensoes')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespPrevCivilPensoes')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">
                            Outros Benefícios Previdenciários
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespPrevCivilOutros')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespPrevCivilOutros')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespPrevCivilOutros')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>Benefícios - Militar</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.despesa.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.despesa.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.despesa.militar
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">Reformas</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespPrevMilitarAposentado')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespPrevMilitarAposentado')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespPrevMilitarAposentado')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">Pensões</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespPrevMilitarPensoes')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespPrevMilitarPensoes')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespPrevMilitarPensoes')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">
                            Outros Benefícios Previdenciários
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespPrevMilitarOutros')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespPrevMilitarOutros')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespPrevMilitarOutros')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>Outras Despesas Previdenciárias</td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela1.despesa.outras
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela1.despesa.outras
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela1.despesa.outras
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">
                            Compensação Previdenciária para o RGPS
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespOutDespPrevidenciaria')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespOutDespPrevidenciaria')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespOutDespPrevidenciaria')
                              "
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="pl-0 pl-md-2">
                            Demais Despesas Previdenciárias
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[0].get('vlrDespOutDespDemaisDesp')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[1].get('vlrDespOutDespDemaisDesp')
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              currencyMask
                              [options]="{
                                prefix: 'R$ ',
                                thousands: '.',
                                decimal: ','
                              }"
                              placeholder="00,00"
                              [formControl]="
                                models[2].get('vlrDespOutDespDemaisDesp')
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
              <nb-accordion-item [disabled]="true">
                <nb-accordion-item-header>
                  <table
                    class="dx-datagrid-table dx-datagrid-table-fixed"
                    role="presentation"
                  >
                    <tbody>
                      <tr>
                        <th class="w-md-35"></th>
                        <th>{{ exercise_1 }}</th>
                        <th>{{ exercise_2 }}</th>
                        <th>{{ exercise_3 }}</th>
                      </tr>
                      <tr>
                        <td>
                          Total das Despesas Previdenciárias RPPS
                          {{
                            selectedPrevType.nome == 'Plano previdenciário'
                              ? '(V)'
                              : '(X)'
                          }}
                        </td>
                        <td>
                          <input
                            type="text"
                            nbInput
                            fullWidth
                            fieldSize="tiny"
                            placeholder="00,00"
                            status="primary"
                            disabled
                            class="text-right"
                            [value]="
                              calcs[0].tela1.totalDespesa | currency: 'BRL':true
                            "
                          />
                        </td>
                        <td>
                          <input
                            type="text"
                            nbInput
                            fullWidth
                            fieldSize="tiny"
                            placeholder="00,00"
                            status="primary"
                            disabled
                            class="text-right"
                            [value]="
                              calcs[1].tela1.totalDespesa | currency: 'BRL':true
                            "
                          />
                        </td>
                        <td>
                          <input
                            type="text"
                            nbInput
                            fullWidth
                            fieldSize="tiny"
                            placeholder="00,00"
                            status="primary"
                            disabled
                            class="text-right"
                            [value]="
                              calcs[2].tela1.totalDespesa | currency: 'BRL':true
                            "
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </nb-accordion-item-header>
              </nb-accordion-item>
              <nb-accordion-item [disabled]="true">
                <nb-accordion-item-header>
                  <table
                    class="dx-datagrid-table dx-datagrid-table-fixed"
                    role="presentation"
                  >
                    <tbody>
                      <tr>
                        <th class="w-md-35"></th>
                        <th>{{ exercise_1 }}</th>
                        <th>{{ exercise_2 }}</th>
                        <th>{{ exercise_3 }}</th>
                      </tr>
                      <tr>
                        <td>
                          Resultado Previdenciário
                          {{
                            selectedPrevType.nome == 'Plano previdenciário'
                              ? '(VI) = (IV - V)'
                              : '(XI) = (IX - X)'
                          }}
                        </td>
                        <td>
                          <input
                            type="text"
                            nbInput
                            fullWidth
                            fieldSize="tiny"
                            placeholder="00,00"
                            status="primary"
                            disabled
                            class="text-right"
                            [value]="
                              calcs[0].tela1.resultado | currency: 'BRL':true
                            "
                          />
                        </td>
                        <td>
                          <input
                            type="text"
                            nbInput
                            fullWidth
                            fieldSize="tiny"
                            placeholder="00,00"
                            status="primary"
                            disabled
                            class="text-right"
                            [value]="
                              calcs[1].tela1.resultado | currency: 'BRL':true
                            "
                          />
                        </td>
                        <td>
                          <input
                            type="text"
                            nbInput
                            fullWidth
                            fieldSize="tiny"
                            placeholder="00,00"
                            status="primary"
                            disabled
                            class="text-right"
                            [value]="
                              calcs[2].tela1.resultado | currency: 'BRL':true
                            "
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </nb-accordion-item-header>
              </nb-accordion-item>
            </nb-accordion>
          </nb-tab>
          <nb-tab tabTitle="Tela II">
            <ng-container *ngIf="selectedPrevType.nome == 'Plano financeiro'">
              <nb-accordion>
                <nb-accordion-item>
                  <nb-accordion-item-header
                    >Aportes de recursos para o plano financeiro do RPPS
                  </nb-accordion-item-header>
                  <nb-accordion-item-body>
                    <div class="table-container">
                      <table
                        class="dx-datagrid-table dx-datagrid-table-fixed"
                        role="presentation"
                      >
                        <tbody>
                          <tr>
                            <th class="w-md-35"></th>
                            <th>{{ exercise_1 }}</th>
                            <th>{{ exercise_2 }}</th>
                            <th>{{ exercise_3 }}</th>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Recursos para Cobertura de Insuficiências
                              Financeiras
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrAporteCoberturaInsufFinan')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrAporteCoberturaInsufFinan')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrAporteCoberturaInsufFinan')
                                "
                              />
                            </td>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Recursos para Formação de Reserva
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrAporteFormacaoReserva')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrAporteFormacaoReserva')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrAporteFormacaoReserva')
                                "
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
                <nb-accordion-item>
                  <nb-accordion-item-header
                    >Receitas da Administração - RPPS</nb-accordion-item-header
                  >
                  <nb-accordion-item-body>
                    <div class="table-container">
                      <table
                        class="dx-datagrid-table dx-datagrid-table-fixed"
                        role="presentation"
                      >
                        <tbody>
                          <tr>
                            <th class="w-md-35"></th>
                            <th>{{ exercise_1 }}</th>
                            <th>{{ exercise_2 }}</th>
                            <th>{{ exercise_3 }}</th>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">Receitas correntes</td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrRecCorrenteAdministracao')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrRecCorrenteAdministracao')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrRecCorrenteAdministracao')
                                "
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
                <nb-accordion-item [disabled]="true">
                  <nb-accordion-item-header>
                    <table
                      class="dx-datagrid-table dx-datagrid-table-fixed"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <th class="w-md-35"></th>
                          <th>{{ exercise_1 }}</th>
                          <th>{{ exercise_2 }}</th>
                          <th>{{ exercise_3 }}</th>
                        </tr>
                        <tr>
                          <td>
                            Total das receitas da Administração RPPS - (XII)
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela2.totalReceita
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela2.totalReceita
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela2.totalReceita
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </nb-accordion-item-header>
                </nb-accordion-item>
                <nb-accordion-item>
                  <nb-accordion-item-header
                    >Despesas da Administração - RPPS</nb-accordion-item-header
                  >
                  <nb-accordion-item-body>
                    <div class="table-container">
                      <table
                        class="dx-datagrid-table dx-datagrid-table-fixed"
                        role="presentation"
                      >
                        <tbody>
                          <tr>
                            <th class="w-md-35"></th>
                            <th>{{ exercise_1 }}</th>
                            <th>{{ exercise_2 }}</th>
                            <th>{{ exercise_3 }}</th>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Despesas correntes (XIII)
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrDespAdmDespCorrente')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrDespAdmDespCorrente')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrDespAdmDespCorrente')
                                "
                              />
                            </td>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Despesas de capital (XIV)
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrDespAdmDespCapital')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrDespAdmDespCapital')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrDespAdmDespCapital')
                                "
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
                <nb-accordion-item [disabled]="true">
                  <nb-accordion-item-header>
                    <table
                      class="dx-datagrid-table dx-datagrid-table-fixed"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <th class="w-md-35"></th>
                          <th>{{ exercise_1 }}</th>
                          <th>{{ exercise_2 }}</th>
                          <th>{{ exercise_3 }}</th>
                        </tr>
                        <tr>
                          <td>
                            Total das despesas da Administração RPPS (XV) =
                            (XIII + XIV)
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela2.totalDespesa
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela2.totalDespesa
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela2.totalDespesa
                                  | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </nb-accordion-item-header>
                </nb-accordion-item>
                <nb-accordion-item [disabled]="true">
                  <nb-accordion-item-header>
                    <table
                      class="dx-datagrid-table dx-datagrid-table-fixed"
                      role="presentation"
                    >
                      <tbody>
                        <tr>
                          <th class="w-md-35"></th>
                          <th>{{ exercise_1 }}</th>
                          <th>{{ exercise_2 }}</th>
                          <th>{{ exercise_3 }}</th>
                        </tr>
                        <tr>
                          <td>
                            Resultado da administração RPPS (XVI) = (XII - XV)
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[0].tela2.resultado | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[1].tela2.resultado | currency: 'BRL':true
                              "
                            />
                          </td>
                          <td>
                            <input
                              type="text"
                              nbInput
                              fullWidth
                              fieldSize="tiny"
                              placeholder="00,00"
                              status="primary"
                              disabled
                              class="text-right"
                              [value]="
                                calcs[2].tela2.resultado | currency: 'BRL':true
                              "
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </nb-accordion-item-header>
                </nb-accordion-item>
              </nb-accordion>
            </ng-container>
            <ng-container
              *ngIf="selectedPrevType.nome == 'Plano previdenciário'"
            >
              <nb-accordion>
                <nb-accordion-item>
                  <nb-accordion-item-header
                    >Recursos RPPS arrecadados em exercícios
                    anteriores</nb-accordion-item-header
                  >
                  <nb-accordion-item-body>
                    <div class="table-container">
                      <table
                        class="dx-datagrid-table dx-datagrid-table-fixed"
                        role="presentation"
                      >
                        <tbody>
                          <tr>
                            <th class="w-md-35"></th>
                            <th>{{ exercise_1 }}</th>
                            <th>{{ exercise_2 }}</th>
                            <th>{{ exercise_3 }}</th>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">Valor</td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrRecursosArrecExeAnt')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrRecursosArrecExeAnt')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrRecursosArrecExeAnt')
                                "
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
                <nb-accordion-item>
                  <nb-accordion-item-header
                    >Reserva orçamentária do RPPS</nb-accordion-item-header
                  >
                  <nb-accordion-item-body>
                    <div class="table-container">
                      <table
                        class="dx-datagrid-table dx-datagrid-table-fixed"
                        role="presentation"
                      >
                        <tbody>
                          <tr>
                            <th class="w-md-35"></th>
                            <th>{{ exercise_1 }}</th>
                            <th>{{ exercise_2 }}</th>
                            <th>{{ exercise_3 }}</th>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">Valor</td>
                            <!-- mapear esse -->
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrDesPreservaOrcamentaria')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrDesPreservaOrcamentaria')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrDesPreservaOrcamentaria')
                                "
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
                <nb-accordion-item>
                  <nb-accordion-item-header
                    >Aportes de recursos para o plano previdenciário do
                    RPPS</nb-accordion-item-header
                  >
                  <nb-accordion-item-body>
                    <div class="table-container">
                      <table
                        class="dx-datagrid-table dx-datagrid-table-fixed"
                        role="presentation"
                      >
                        <tbody>
                          <tr>
                            <th class="w-md-35"></th>
                            <th>{{ exercise_1 }}</th>
                            <th>{{ exercise_2 }}</th>
                            <th>{{ exercise_3 }}</th>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Plano de Amortização - Contribuição Patronal
                              Suplementar
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrAporteContPatronal')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrAporteContPatronal')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrAporteContPatronal')
                                "
                              />
                            </td>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Plano de Amortização - Aporte Periódico de Valores
                              Predefinidos
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrAportePeriodicos')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrAportePeriodicos')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrAportePeriodicos')
                                "
                              />
                            </td>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Outros Aportes para o RPPS
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="models[0].get('vlrAporteOutros')"
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="models[1].get('vlrAporteOutros')"
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="models[2].get('vlrAporteOutros')"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Recursos para Cobertura de Déficit Financeiro
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[0].get('vlrAporteCoberturaDefFinan')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[1].get('vlrAporteCoberturaDefFinan')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  models[2].get('vlrAporteCoberturaDefFinan')
                                "
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
                <nb-accordion-item>
                  <nb-accordion-item-header
                    >Bens e Direitos do RPPS</nb-accordion-item-header
                  >
                  <nb-accordion-item-body>
                    <div class="table-container">
                      <table
                        class="dx-datagrid-table dx-datagrid-table-fixed"
                        role="presentation"
                      >
                        <tbody>
                          <tr>
                            <th class="w-md-35"></th>
                            <th>{{ exercise_1 }}</th>
                            <th>{{ exercise_2 }}</th>
                            <th>{{ exercise_3 }}</th>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Caixa e Equivalentes de Caixa
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[0].get('vlrBensDireitosCaixa')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[1].get('vlrBensDireitosCaixa')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[2].get('vlrBensDireitosCaixa')
                                "
                              />
                            </td>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">
                              Investimentos e Aplicações
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[0].get(
                                    'vlrBensDireitosInvestimentos'
                                  )
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[1].get(
                                    'vlrBensDireitosInvestimentos'
                                  )
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[2].get(
                                    'vlrBensDireitosInvestimentos'
                                  )
                                "
                              />
                            </td>
                          </tr>
                          <tr>
                            <td class="pl-0 pl-md-2">Outros Bens e Direitos</td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[0].get('vlrBensDireitosOutros')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[1].get('vlrBensDireitosOutros')
                                "
                              />
                            </td>
                            <td>
                              <input
                                type="text"
                                nbInput
                                fullWidth
                                fieldSize="tiny"
                                currencyMask
                                [options]="{
                                  prefix: 'R$ ',
                                  thousands: '.',
                                  decimal: ','
                                }"
                                placeholder="00,00"
                                [formControl]="
                                  this.models[2].get('vlrBensDireitosOutros')
                                "
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </nb-accordion-item-body>
                </nb-accordion-item>
              </nb-accordion>
            </ng-container>
          </nb-tab>
        </nb-tabset>
      </div>
    </div>
  </ng-container>
</eqp-standard-page>
