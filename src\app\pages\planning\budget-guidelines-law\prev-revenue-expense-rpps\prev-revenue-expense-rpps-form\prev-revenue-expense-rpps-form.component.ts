import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { FormBuilder, FormControl, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { Subject } from 'rxjs'
import {
  distinctUntilChanged,
  finalize,
  first,
  take,
  takeUntil,
} from 'rxjs/operators'
import { PrevidencialRevenueExpenseRpps2018Interface } from '../../interfaces/prev-revenue-expense-rpps'
import { PrevidencialTypeInterface } from '../../interfaces/previdencial-type'
import { PrevRevenueExpenseRppsService } from '../../services/prev-revenue-expense-rpps.service'
import { PrevidencialTypeService } from '../../services/previdencial-type.service'
import {
  calcAdminResultado,
  calcResultado,
  getCalculatedFields,
} from '../helpers/helper'
import { CalculatedFieldsInterface } from '../interfaces/calculated-fields-interface'

@Component({
  selector: 'eqp-prev-revenue-expense-rpps-form',
  templateUrl: './prev-revenue-expense-rpps-form.component.html',
  styleUrls: ['./prev-revenue-expense-rpps-form.component.scss'],
})
export class PrevRevenueExpenseRppsFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Receitas e despesas previdenciárias do RPPS'

  enableEditing = false

  models: FormGroup[]

  pensionData: PrevidencialRevenueExpenseRpps2018Interface[]
  tipoPrevidenciaData: DataSource<PrevidencialTypeInterface, string>

  calcs: CalculatedFieldsInterface[]

  loggedExercise: number
  selectedPension: string = 'PENSION_PLAN'
  selectedPrevType: PrevidencialTypeInterface

  togglePublication = new FormControl(false)

  unsub$ = new Subject<null>()

  publication = new FormControl()

  constructor(
    private service: PrevRevenueExpenseRppsService,
    private previdencialTypeService: PrevidencialTypeService,
    private toastr: ToastrService,
    public router: Router,
    public route: ActivatedRoute,
    public menuService: MenuService,
    private builder: FormBuilder,
    private userService: UserDataService,
  ) {
    super(menuService, router)
    this.permissao(
      '/lei-diretrizes-orcamentarias/receitas-despesas-previdenciarias-rpps',
    )
  }

  get exercise_1() {
    return this.loggedExercise - 4
  }

  get exercise_2() {
    return this.loggedExercise - 3
  }

  get exercise_3() {
    return this.loggedExercise - 2
  }

  ngOnInit(): void {
    this.model = this.builder.group({
      tipoPrevidencia: [''],
    })
    this.loggedExercise = +this.userService.userData?.exercicio
    this.models = [this.getNewModel(), this.getNewModel(), this.getNewModel()]
    this.calcs = [
      getCalculatedFields(),
      getCalculatedFields(),
      getCalculatedFields(),
    ]
    this.loadSelect()
    this.loadCalcHandlers()
    this.loadPageData()
  }

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      uuid: [''],
      exercicioReferencia: [0],
      tipoPrevidencia: this.builder.group({
        codigo: [],
        uuid: [''],
        nome: [''],
      }),
      vlrRecSegCivilAtivo: [0],
      vlrRecSegCivilInativo: [0],
      vlrRecSegCivilPensionista: [0],
      vlrRecSegMilitarAtivo: [0],
      vlrRecSegMilitarInativo: [0],
      vlrRecSegMilitarPensionista: [0],
      vlrRecCpatCivilAtivo: [0],
      vlrRecCpatCivilInativo: [0],
      vlrRecCpatCivilPensionista: [0],
      vlrRecCpatMilitarAtivo: [0],
      vlrRecCpatMilitarInativo: [0],
      vlrRecCpatMilitarPensionista: [0],
      vlrRecCpatRegimeParcDeb: [0],
      vlrRecPatRecImobiliaria: [0],
      vlrRecPatValImobiliario: [0],
      vlrRecPatOutrasReceitas: [0],
      vlrRecServicos: [0],
      vlrRecAportePeriodico: [0],
      vlrOutRecCompPrevidenciaria: [0],
      vlrOutRecDemaisReceitas: [0],
      vlrRecCapAlienacaoBens: [0],
      vlrRecCapAmortizacaoEmprest: [0],
      vlrRecCapOutrasReceita: [0],
      vlrRecursosArrecExeAnt: [0],
      vlrDespAdmDespCorrente: [0],
      vlrDespAdmDespCapital: [0],
      vlrDespPrevCivilAposentado: [0],
      vlrDespPrevCivilPensoes: [0],
      vlrDespPrevCivilOutros: [0],
      vlrDespPrevMilitarAposentado: [0],
      vlrDespPrevMilitarPensoes: [0],
      vlrDespPrevMilitarOutros: [0],
      vlrDespOutDespPrevidenciaria: [0],
      vlrDespOutDespDemaisDesp: [0],
      vlrDesPreservaOrcamentaria: [0],
      vlrAporteContPatronal: [0],
      vlrAportePeriodicos: [0],
      vlrAporteOutros: [0],
      vlrAporteCoberturaDefFinan: [0],
      vlrAporteCoberturaInsufFinan: [0],
      vlrAporteFormacaoReserva: [0],
      vlrBensDireitosCaixa: [0],
      vlrBensDireitosInvestimentos: [0],
      vlrBensDireitosOutros: [0],
      vlrRecCorrenteAdministracao: [0],
    })
  }

  private async loadSelect() {
    let prevData = await this.previdencialTypeService
      .getSingleData<PrevidencialTypeInterface[]>('tipo_previdencia')
      .toPromise()
    prevData.dados = prevData.dados.filter(item => item.nome != 'Não se aplica') // filtro pelo codigo
    this.tipoPrevidenciaData = new DataSource({
      store: {
        data: prevData?.dados || [],
        key: 'uuid',
        type: 'array',
      },
    })
    this.loadSelectHandler()
  }

  private loadSelectHandler() {
    this.model
      .get('tipoPrevidencia')
      .valueChanges.pipe(distinctUntilChanged(), takeUntil(this.unsub$))
      .subscribe(uuid => {
        const save = Boolean(this.selectedPrevType)
        this.selectedPrevType = this.tipoPrevidenciaData
          .items()
          .find(val => uuid == val.uuid)
        if (this.pensionData) {
          this.onChangeSelect(save)
        }
      })
  }

  private onChangeSelect(save: boolean) {
    if (save) {
      this.pensionData = this.pensionData.map(data => {
        if (data.tipoPrevidencia.nome != this.selectedPrevType.nome) {
          return this.models[
            data.exercicioReferencia - this.loggedExercise + 4
          ].getRawValue()
        }
        return data
      })
    }
    let ret: PrevidencialRevenueExpenseRpps2018Interface[] = []
    ret = this.pensionData.filter(data => {
      return data.tipoPrevidencia.nome == this.selectedPrevType.nome
    })
    ret.sort((a, b) => a.exercicioReferencia - b.exercicioReferencia)
    for (let i of [0, 1, 2]) {
      this.models[i].patchValue(ret[i])
    }
  }

  private loadCalcHandlers() {
    for (let i of [0, 1, 2]) {
      this.models[i].valueChanges
        .pipe(takeUntil(this.unsub$))
        .subscribe(val => {
          calcResultado(this.calcs[i], val)
          calcAdminResultado(this.calcs[i], val)
        })
    }
  }

  loadPageData() {
    this.loading = true
    this.service
      .get()
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        res => {
          if (res.dados.length > 0) {
            this.loadForm(res.dados)
          } else {
            this.criar()
          }
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  private criar() {
    this.loading = true
    this.service
      .postBatch([])
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(
        () => {
          this.loadPageData(),
            this.toastr.send({
              success: true,
              message:
                'Receitas e despesas previdenciárias do RPPS criadas com sucesso.',
            })
        },
        (resp: any) => this.toastr.bulkSend(resp.mensagens),
      )
  }

  private loadForm(data: PrevidencialRevenueExpenseRpps2018Interface[]) {
    this.pensionData = data
    this.publication.patchValue(data[0].publicacao)
    this.enableEditing = true
  }

  private prepare() {
    this.pensionData = this.pensionData.map(data => {
      if (data.tipoPrevidencia.nome == this.selectedPrevType.nome) {
        return this.models[
          data.exercicioReferencia - this.loggedExercise + 4
        ].getRawValue()
      }
      return data
    })
    for (let i = 0; i < this.pensionData.length; i++) {
      this.pensionData[i].publicacao = this.publication.value
    }
  }

  update(): void {
    this.prepare()
    this.loading = true
    this.service
      .putBatch(this.pensionData)
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(_ => {
        this.toastr.send({
          success: true,
          message: 'Atualizado(a) com sucesso.',
        })
      })
  }
}
