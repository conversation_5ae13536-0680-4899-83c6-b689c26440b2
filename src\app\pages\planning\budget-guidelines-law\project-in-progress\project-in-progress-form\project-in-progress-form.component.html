<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
>
  <div class="mb-5" [formGroup]="form">
    <div class="row mb-2">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [disabled]="true"
          name="Exercício referência"
          label="Exercício referência"
          placeholder="Exercício referência"
          formControlName="exercicio"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-6 mt-4">
        <eqp-publication-note-field
          formControlName="publicacao"
        ></eqp-publication-note-field>
      </div>
    </div>
    <div class="row mb-5">
      <div class="col-md-6 col-sm-12">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Projeto/Atividade"
          label="Projeto/Atividade"
          placeholder="Projeto/Atividade"
          [dataSource]="projetoAtividade"
          valueExpr="uuid"
          displayExpr="tela"
          formControlName="projetoAtividadeUuid"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-6 col-sm-12">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Unidade"
          label="Unidade"
          placeholder="Unidade"
          [dataSource]="unidade"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="unidadeMedidaUuid"
        ></eqp-nebular-select>
      </div>
    </div>
    <nb-card>
      <nb-card-header class="topo">Quantidade</nb-card-header>
      <nb-card-body>
        <div class="row">
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: '', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              (blur)="saldoQtd()"
              name="Previsão orçamentária"
              label="Previsão orçamentária"
              placeholder="Previsão orçamentária"
              formControlName="vlrPrevOrcQte"
            >
            </eqp-nebular-input>
          </div>
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: '', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              (blur)="saldoQtd()"
              name="Execução orçamentária"
              label="Execução orçamentária"
              placeholder="Execução orçamentária"
              formControlName="vlrExecOrcQte"
            >
            </eqp-nebular-input>
          </div>
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: '', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [disabled]="true"
              [shape]="'rectangle'"
              name="Saldo a executar"
              label="Saldo a executar"
              placeholder="Saldo a executar"
              formControlName="saldoQte"
            >
            </eqp-nebular-input>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
    <nb-card>
      <nb-card-header class="topo">Valor</nb-card-header>
      <nb-card-body>
        <div class="row" [formGroup]="form">
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: '', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              (blur)="saldoVlr()"
              name="Previsão orçamentária"
              label="Previsão orçamentária"
              placeholder="Previsão orçamentária"
              formControlName="vlrPrevOrcVlr"
            >
            </eqp-nebular-input>
          </div>
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: '', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [shape]="'rectangle'"
              (blur)="saldoVlr()"
              name="Execução orçamentária"
              label="Execução orçamentária"
              placeholder="Execução orçamentária"
              formControlName="vlrExecOrcVlr"
            >
            </eqp-nebular-input>
          </div>
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'currency'"
              [options]="{ prefix: '', thousands: '.', decimal: ',' }"
              [size]="'small'"
              [disabled]="true"
              [shape]="'rectangle'"
              name="Saldo a executar"
              label="Saldo a executar"
              placeholder="Saldo a executar"
              formControlName="saldoVlr"
            >
            </eqp-nebular-input>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
  <div class="row mt-4">
    <div class="col-md-12">
      <button type="button" class="btn btn-dark" (click)="back()">
        Cancelar
      </button>
      <button
        type="button"
        [disabled]="form.invalid"
        class="btn btn-success float-md-right"
        (click)="submit(form)"
      >
        Confirmar
      </button>
    </div>
  </div>
</eqp-standard-page>
