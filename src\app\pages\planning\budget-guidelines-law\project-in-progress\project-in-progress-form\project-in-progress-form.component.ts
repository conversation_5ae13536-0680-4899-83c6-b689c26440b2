import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { finalize, first } from 'rxjs/operators'
import { ProjectInProgressInterface } from '../../interfaces/project-in-progress.model'
import { ProjectInProgressService } from '../../services/project-in-progress.service'

@Component({
  selector: 'eqp-project-in-progress-form',
  templateUrl: './project-in-progress-form.component.html',
  styleUrls: ['./project-in-progress-form.component.scss'],
})
export class ProjectInProgressFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle: string = 'Projetos em andamento'
  public unidade: any
  public previousExercise: number
  public projetoAtividade: any

  public form: FormGroup

  public uuid: string

  public subscription: Subscription

  constructor(
    private formBuilder: FormBuilder,
    private service: ProjectInProgressService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/lei-diretrizes-orcamentarias/projetos-em-andamento')
  }

  public ngOnInit(): void {
    this.form = this.formBuilder.group({
      uuid: [''],
      projetoAtividadeUuid: ['', Validators.required],
      unidadeMedidaUuid: ['', Validators.required],
      vlrExecOrcQte: ['', Validators.required],
      vlrExecOrcVlr: ['', Validators.required],
      vlrPrevOrcQte: ['', Validators.required],
      vlrPrevOrcVlr: ['', Validators.required],
      exercicio: [''],
      saldoQte: [''],
      saldoVlr: [''],
      publicacao: [],
    })
    this.loadScreen()
    this.getExercise()
    this.saldoQtd()
    this.saldoVlr()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  getExercise() {
    const userData = localStorage.getItem('userData')
    const userDataParse = JSON.parse(userData)
    this.previousExercise = userDataParse.exercicio - 1
    this.form.get('exercicio').patchValue(this.previousExercise)
  }

  private loadSelects(): void {
    this.projetoAtividade = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        `projeto_atividade/paginado?ano=${this.previousExercise}`,
        10,
      ),
      paginate: true,
      pageSize: 10,
      map: item => {
        return {
          ...item,
          tela: `${item.tipo}${item.ordem} - ${item.nome}`,
        }
      },
    }

    this.unidade = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        `unidade_medida/paginado`,
        10,
      ),
      paginate: true,
      pageSize: 10
    }
  }

  private loadScreen(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) this.searchById(uuid)
      else
        setTimeout(() => {
          this.loadSelects()
        })
    })
  }

  private searchById(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe((res: any) => {
        this.uuid = res.dados.uuid
        this.form.patchValue({
          ...res.dados,
          projetoAtividadeUuid: res.dados.projetoAtividade?.uuid,
          unidadeMedidaUuid: res.dados.unidadeMedida?.uuid,
        })
        this.loadSelects()
        this.saldoQtd()
        this.saldoVlr()
      })
  }

  back() {
    this.router.navigate([`lei-diretrizes-orcamentarias/projetos-em-andamento`])
  }

  saldoQtd() {
    if (this.form.value.vlrPrevOrcQte && this.form.value.vlrExecOrcQte) {
      const result =
        this.form.value.vlrPrevOrcQte - this.form.value.vlrExecOrcQte
      if (result > 0) {
        this.form.get('saldoQte').patchValue(result)
      } else {
        this.form.get('saldoQte').patchValue('')
      }
    }
  }

  saldoVlr() {
    if (this.form.value.vlrPrevOrcVlr && this.form.value.vlrExecOrcVlr) {
      const result =
        this.form.value.vlrPrevOrcVlr - this.form.value.vlrExecOrcVlr
      if (result > 0) {
        this.form.get('saldoVlr').patchValue(result)
      } else {
        this.form.get('saldoVlr').patchValue('')
      }
    }
  }

  create(dto: ProjectInProgressInterface) {
    this.loading = true
    this.subscription = this.service
      .post(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Projeto inserido com sucesso.',
          })
          this.router.navigate([
            'lei-diretrizes-orcamentarias/projetos-em-andamento',
          ])
        },
        (err: any) => this.toastr.bulkSend(err.mensagens),
      )
  }

  update(dto: ProjectInProgressInterface) {
    this.loading = true
    this.subscription = this.service
      .put(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Projeto atualizado com sucesso.',
          })
          this.router.navigate([
            'lei-diretrizes-orcamentarias/projetos-em-andamento',
          ])
        },
        (err: any) => this.toastr.bulkSend(err.mensagens),
      )
  }

  submit({ value, valid }: { value: any; valid: boolean }) {
    if (!valid) {
      return
    }

    if (this.uuid) {
      value.publicacao = {
        ...value.publicacao,
      }
      this.update(value)
    } else {
      value.publicacao = {
        ...value.publicacao,
        flagPublicar: value.publicacao?.flagPublicar ? value.publicacao?.flagPublicar : 'N',
      }
      this.create(value)
    }
  }
}
