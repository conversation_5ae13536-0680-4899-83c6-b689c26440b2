<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="fetchGrid()"
>
  <ng-container>
    <dx-data-grid
      id="projectInProgressGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="false"
      [columnAutoWidth]="false"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="false"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="projetoAtividade.nome"
        caption="Projeto atividade"
      ></dxi-column>

      <dxi-column
        dataField="unidadeMedida.nome"
        caption="Unidade de medida"
      ></dxi-column>

      <dxi-column
        dataField="vlrPrevOrcQte"
        caption="Previsão orçamentária(quantidade)"
        alignment="left"
        [format]="{ minimumFractionDigits: 2, maximumFractionDigits: 3 }"
      ></dxi-column>

      <dxi-column
        dataField="vlrExecOrcQte"
        caption="Execução orçamentária(quantidade)"
        alignment="left"
        [format]="{ minimumFractionDigits: 2, maximumFractionDigits: 3 }"
      ></dxi-column>

      <dxi-column
        dataField="saldoQtd"
        caption="Saldo a executar(quantidade)"
        alignment="left"
        [format]="{ minimumFractionDigits: 2, maximumFractionDigits: 3 }"
      ></dxi-column>

      <dxi-column
        dataField="vlrPrevOrcVlr"
        caption="Previsão orçamentária(valor)"
        alignment="left"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        dataField="vlrExecOrcVlr"
        caption="Execução orçamentária(valor)"
        alignment="left"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        dataField="saldoVlr"
        caption="Saldo a executar(valor)"
        alignment="left"
        [format]="currencyFormat"
      ></dxi-column>

      <dxi-column
        dataField="publicacao.flagPublicar"
        caption="Publicar na internet"
      ></dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="80"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          title="Alterar"
          (click)="edit(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          *ngIf="nivelPermissao === 'FULL'"
          title="Remover"
          (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
