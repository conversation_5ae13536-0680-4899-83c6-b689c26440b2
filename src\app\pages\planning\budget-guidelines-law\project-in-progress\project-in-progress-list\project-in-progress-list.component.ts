import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { ProjectInProgressService } from '../../services/project-in-progress.service'

@Component({
  selector: 'eqp-project-in-progress-list',
  templateUrl: './project-in-progress-list.component.html',
  styleUrls: ['./project-in-progress-list.component.scss'],
})
export class ProjectInProgressListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }
  public loading: boolean = false
  public pageTitle: string = 'Projetos em andamento'
  public dataSource: any

  private subscription: Subscription

  constructor(
    private service: ProjectInProgressService,
    public menuService: MenuService,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao('/projetos_em_andamento')
  }

  public ngOnInit(): void {
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.dataSource = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        'projetos_em_andamento/paginado',
        10,
      ),
      map: item => {
        return {
          ...item,
          saldoQtd: item.vlrPrevOrcQte - item.vlrExecOrcQte,
          saldoVlr: item.vlrPrevOrcVlr - item.vlrExecOrcVlr,
        }
      },
    }
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo'
          item.options.hint = 'Novo'
          item.options.onClick = () => this.newRegister()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public newRegister(): void {
    this.gravarParametros()
    this.router.navigate([
      `lei-diretrizes-orcamentarias/projetos-em-andamento/novo`,
    ])
  }

  public edit(uuid: string): void {
    this.router.navigate([
      `/lei-diretrizes-orcamentarias/projetos-em-andamento/edit/${uuid}`,
    ])
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(retorno => {
      if (retorno === 'S') {
        this.service.delete(uuid).subscribe(() => {
          this.toastr.send({
            success: true,
            message: 'Projeto excluído com sucesso.',
          })
          this.fetchGrid()
        })
      }
    })
  }
}
