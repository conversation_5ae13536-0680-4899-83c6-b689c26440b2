import { RevenueEvolutionLdoInterface } from "./revenue-evolution-ldo"

export const MOCK: RevenueEvolutionLdoInterface[] = [
  {
    uuid: '1',
    planoReceita: {
      codigo: '1.1.0.0.00.0.0.00.00.00.00.00',
      nome: 'IMPOSTO<PERSON>, TAXAS E CONTRIBUIÇÕES DE MELHORIA'
    },
    vlrRealizadoAnoMenos3: 19999,
    vlrRealizadoAnoMenos2: 11111,
    vlrEstimadoAnoMenos1: 66666,
    vlrProjetadoAnoReferencia: 77777,
    vlrProjetadoAnoMais1: 222222,
    vlrProjetadoAnoMais2: 55555,
    metodologiaCalculo: ''
  } as RevenueEvolutionLdoInterface
] 