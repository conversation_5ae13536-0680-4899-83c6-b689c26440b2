<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row mb-4">
      <div class="col col-12 col-md-6">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="revenuePlanName"
          name="Conta de receita"
          label="Conta de receita"
          [readonly]="true"
        >
        </eqp-nebular-input>
      </div>

      <div class="col col-12 col-md-6 mt-4">
        <eqp-publication-note-field
          formControlName="publicacao"
        ></eqp-publication-note-field>
      </div>
    </div>

    <eqp-fieldset label="Realizada">
      <div class="row">
        <div class="col col-12 col-md-3">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="vlrRealizadoAnoMenos3"
            name="vlrRealizadoAnoMenos3"
            placeholder="R$00,00"
            [label]="exercicioLogado - 3"
          >
          </eqp-nebular-input>
        </div>

        <div class="col col-12 col-md-3">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="vlrRealizadoAnoMenos2"
            name="vlrRealizadoAnoMenos2"
            placeholder="R$00,00"
            [label]="exercicioLogado - 2"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </eqp-fieldset>

    <eqp-fieldset label="Estimada">
      <div class="row">
        <div class="col col-12 col-md-4">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="vlrEstimadoAnoMenos1"
            name="vlrEstimadoAnoMenos1"
            placeholder="R$00,00"
            [label]="exercicioLogado - 1"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </eqp-fieldset>

    <eqp-fieldset label="Projetada">
      <div class="row">
        <div class="col col-12 col-md-3">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="vlrProjetadoAnoReferencia"
            name="vlrProjetadoAnoReferencia"
            placeholder="R$00,00"
            [label]="exercicioLogado"
          >
          </eqp-nebular-input>
        </div>

        <div class="col col-12 col-md-3">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="vlrProjetadoAnoMais1"
            name="vlrProjetadoAnoMais1"
            placeholder="R$00,00"
            [label]="exercicioLogado + 1"
          >
          </eqp-nebular-input>
        </div>

        <div class="col col-12 col-md-3">
          <eqp-nebular-input
            [style]="'currency'"
            [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="vlrProjetadoAnoMais2"
            name="vlrProjetadoAnoMais2"
            placeholder="R$00,00"
            [label]="exercicioLogado + 2"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </eqp-fieldset>

    <div class="row espacamento-campo-texto">
      <div class="col-md-12 col-sm-12">
        <eqp-nebular-input
          [style]="'textArea'"
          [size]="'small'"
          [rows]="6"
          [shape]="'rectangle'"
          name="Descrição"
          [hasFullWidth]="true"
          label="Metodologia de cálculo para apuração da receita projetada"
          placeholder="Descrição"
          formControlName="metodologiaCalculo"
          class="campo-de-texto"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>

        <button
          *ngIf="
            (model.get('uuid').value && nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="update()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
