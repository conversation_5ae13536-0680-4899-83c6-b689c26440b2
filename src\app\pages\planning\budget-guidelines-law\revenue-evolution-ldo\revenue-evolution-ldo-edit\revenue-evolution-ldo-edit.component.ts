import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { MenuService } from '@pages/menu.service'
import { finalize, first, pluck } from 'rxjs/operators'
import { RevenueEvolutionLdoService } from '../services/revenue-evolution-ldo.service'

@Component({
  selector: 'eqp-revenue-evolution-ldo-edit',
  templateUrl: './revenue-evolution-ldo-edit.component.html',
  styleUrls: ['./revenue-evolution-ldo-edit.component.scss'],
})
export class RevenueEvolutionLdoEditComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle: string = 'Evolução da Receita1'
  public model: FormGroup
  public exercicioLogado: number

  public userData: {
    exercicio: string
  }

  constructor(
    private formBuilder: FormBuilder,
    private revenueEvolutionLdoService: RevenueEvolutionLdoService,
    private userService: UserDataService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/lei-diretrizes-orcamentarias/receita/evolucao')
  }

  ngOnInit(): void {
    const { exercicio } = this.userService.userData
    this.userData = {
      exercicio,
    }
    this.getNewForm()
    this.carregarTela()
    this.exercicioLogado = +this.userService.userData.exercicio
  }

  public getNewForm() {
    this.model = this.formBuilder.group({
      uuid: [''],
      revenuePlanUuid: [''],
      revenuePlanName: [''],
      vlrRealizadoAnoMenos3: [],
      vlrRealizadoAnoMenos2: [],
      vlrEstimadoAnoMenos1: [],
      vlrProjetadoAnoReferencia: [],
      vlrProjetadoAnoMais1: [],
      vlrProjetadoAnoMais2: [],
      metodologiaCalculo: [''],
      publicacao: [],
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) {
        this.buscar(uuid)
      }
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.revenueEvolutionLdoService
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
        pluck('dados'),
      )
      .subscribe(data => {
        this.loadForm(data)
      })
  }

  loadForm(data) {
    let dto = {
      ...data,
      uuid: data.uuid,
      revenuePlanUuid: data.planoReceita.uuid,
      revenuePlanName: data.planoReceita.nome,
    }

    this.model.patchValue(dto)
  }

  private prepare(formData: any): any {
    let res = {
      ...formData,
      planoReceita: {
        uuid: formData.revenuePlanUuid,
      },
    }
    return res
  }

  public update() {
    this.loading = true

    this.revenueEvolutionLdoService
      .put(this.prepare(this.model.getRawValue()))
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.toastr.send({
          success: true,
          message: 'Evolução da receita atualizada com sucesso.',
        })
      })
  }

  public cancelar(retorno): void {
    this.router.navigate([`lei-diretrizes-orcamentarias/receita/evolucao`])
  }
}
