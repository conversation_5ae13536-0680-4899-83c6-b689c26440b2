import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { RevenueEvolutionLdoSearchComponent } from '../revenue-evolution-ldo-search/revenue-evolution-ldo-search.component'
import { RevenueEvolutionLdoService } from '../services/revenue-evolution-ldo.service'

@Component({
  selector: 'eqp-revenue-evolution-ldo-list',
  templateUrl: './revenue-evolution-ldo-list.component.html',
  styleUrls: ['./revenue-evolution-ldo-list.component.scss'],
})
export class RevenueEvolutionLdoListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading: boolean = false
  public pageTitle: string = 'Evolução da Receita'

  public dataSource: DataSource

  public currencyFormat = {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 3,
  }

  public userData: {
    exercicio: string
    exercicioUuid: string
  }

  constructor(
    private revenueEvolutionLdoservice: RevenueEvolutionLdoService,
    private userService: UserDataService,
    private dialogService: NbDialogService,
    private toastrService: ToastrService,
    public router: Router,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/lei-diretrizes-orcamentarias/receita/evolucao')
  }

  ngOnInit(): void {
    const { exercicio, exercicioUuid } = this.userService.userData
    this.userData = {
      exercicio,
      exercicioUuid,
    }
    this.fetchGrid()
  }

  public fetchGrid(): void {
    this.dataSource = new DataSource({
      store: this.revenueEvolutionLdoservice.getDataSourceFiltro(
        'uuid',
        'evolucao_receita_ldo/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Evolução da receita'
          item.options.hint = 'Nova Evolução da receita'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public alterar(value) {
    this.router.navigate([
      `lei-diretrizes-orcamentarias/receita/evolucao/edit/${value}`,
    ])
  }

  public remove(value): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.revenueEvolutionLdoservice
          .delete(value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(() => {
            this.toastrService.send({
              success: true,
              message: 'Evolução de receita excluída com sucesso.',
            })
            this.fetchGrid()
          })
      }
    })
  }

  public novoRegistro(): void {
    const dialogRef = this.dialogService.open(
      RevenueEvolutionLdoSearchComponent,
      {
        context: {},
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.onClose.subscribe(res => {
      if (res) {
        this.fetchGrid()
      }
    })
  }
}
