import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { RevenuePlanInterface } from '@pages/planning/multi-year-plan/interfaces/revenue-plan'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { RevenueEvolutionLdoService } from '../services/revenue-evolution-ldo.service'

@Component({
  selector: 'eqp-revenue-evolution-ldo-search',
  templateUrl: './revenue-evolution-ldo-search.component.html',
  styleUrls: ['./revenue-evolution-ldo-search.component.scss'],
})
export class RevenueEvolutionLdoSearchComponent
  extends BaseTelasComponent
  implements OnInit
{
  pageTitle: string = 'Selecionar Receita'
  loading: boolean

  selectedRowKeys: string[] = []

  dataSource: DataSource<RevenuePlanInterface, string>

  constructor(
    public router: Router,
    public menuService: MenuService,
    private revenueEvolutionLdoService: RevenueEvolutionLdoService,
    private toastrService: ToastrService,
    private dialogRef: NbDialogRef<RevenueEvolutionLdoSearchComponent>,
  ) {
    super(menuService, router)
    this.permissao('/lei-diretrizes-orcamentarias/receita/evolucao')
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  fetchGrid() {
    this.dataSource = new DataSource({
      store: this.revenueEvolutionLdoService.getDataSourceFiltroComposto(
        'uuid',
        'evolucao_receita_ldo/plano_receita',
        10,
        '["nivel","=","2"]',
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  confirm() {
    this.loading = true
    this.revenueEvolutionLdoService
      .postBatch(
        this.selectedRowKeys.map(value => ({
          metodologiaCalculo: '',
          planoReceita: {
            uuid: value,
          },
          vlrEstimadoAnoMenos1: 0,
          vlrProjetadoAnoMais1: 0,
          vlrProjetadoAnoMais2: 0,
          vlrProjetadoAnoReferencia: 0,
          vlrRealizadoAnoMenos2: 0,
          vlrRealizadoAnoMenos3: 0,
          publicacao: {
            flagPublicar: 'N',
          },
        })),
      )
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.toastrService.send({
          success: true,
          message: 'Planos de receita selecionados foram importados.',
        })
        this.dialogRef.close(res.status)
      })
  }

  import() {
    this.loading = true
    this.revenueEvolutionLdoService
      .importar()
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(res => {
        this.toastrService.send({
          success: true,
          message: 'Todos os planos de receita foram importados.',
        })
        this.dialogRef.close(res.status)
      })
  }

  cancel() {
    this.dialogRef.close(null)
  }
}
