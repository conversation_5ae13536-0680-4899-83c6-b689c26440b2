import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { RevenueEvolutionLdoInterface } from '../interfaces/revenue-evolution-ldo';

@Injectable({
  providedIn: 'root'
})
export class RevenueEvolutionLdoService extends BaseService<ResponseDto<RevenueEvolutionLdoInterface>,
  RevenueEvolutionLdoInterface
>{
  constructor(protected httpParameter: HttpClient) {
    super(httpParameter, 'evolucao_receita_ldo')
   }


   public getPaginado(filters?: any) {
    const headers = new HttpHeaders()
    let params = new HttpParams()

    if (filters) {
      Object.keys(filters).forEach(p => (params = params.append(p, filters[p])))
    }

    return this.http.get<any>(
      `evolucao_receita_ldo/paginado`,
      {
        headers,
        params,
      },
    )
  }


   public importar(){
		const headers = new HttpHeaders()
		return this.http.post<null>(
			'evolucao_receita_ldo/importar', null, {
				headers,
				observe: 'response'
			}
		)
	}

}
