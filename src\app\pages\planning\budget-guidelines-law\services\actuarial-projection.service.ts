import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import {
  ActuarialProjectionInterface,
  ActuarialProjectionReturnInterface,
} from '../interfaces/actuarial-projection.model'

@Injectable({
  providedIn: 'root',
})
export class ActuarialProjectionService extends BaseService<
  ActuarialProjectionReturnInterface,
  ActuarialProjectionInterface
> {
  constructor(http: HttpClient) {
    super(http, 'projecao_atuarial_rpps')
  }

  getList(tipoPrevidenciaUuid?: string) {
    const headers = new HttpHeaders()
    return this.http.get(
      `projecao_atuarial_rpps/paginado?take=0&sort=exercicioReferencia",false&filter="tipoPrevidencia","=",${tipoPrevidenciaUuid}`,
      {
        headers,
      },
    )
  }
}
