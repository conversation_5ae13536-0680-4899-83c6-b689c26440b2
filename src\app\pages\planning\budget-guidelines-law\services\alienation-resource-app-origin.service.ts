import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import { IPagedList } from '@design-tools/wrapped-dx-grid/models'
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto'
import { Observable } from 'rxjs'
import { AlienationResourceApplicationOriginInterface } from '../interfaces/alienation-resource-app-origin'

@Injectable({
  providedIn: 'root',
})
export class AlienationResourceAppOriginService {
  constructor(protected http: HttpClient) {}

  public get(filters?: any) {
    const headers = new HttpHeaders()
    let params = new HttpParams()
    if (filters) {
      Object.keys(filters).forEach(p => (params = params.append(p, filters[p])))
    }
    return this.http.get<
      IPagedList<AlienationResourceApplicationOriginInterface>
    >('origem_aplicacao_recurso_alienacao_bem', {
      headers,
      params,
    })
  }

  public postBatch(
    batch: AlienationResourceApplicationOriginInterface[],
  ): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.post<AlienationResourceApplicationOriginInterface[]>(
      `origem_aplicacao_recurso_alienacao_bem/lote`,
      batch,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public putBatch(
    batch: AlienationResourceApplicationOriginInterface[],
  ): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.put<AlienationResourceApplicationOriginInterface[]>(
      `origem_aplicacao_recurso_alienacao_bem/lote`,
      batch,
      {
        headers,
        observe: 'response',
      },
    )
  }
}
