import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import {
  EvolutionOfEquityInterface,
  EvolutionOfEquityReturnInterface,
} from '../interfaces/evolution-of-equity.model'

@Injectable({
  providedIn: 'root',
})
export class EvolutionOfEquityService extends BaseService<
  EvolutionOfEquityReturnInterface,
  EvolutionOfEquityInterface
> {
  constructor(http: HttpClient) {
    super(http, 'evolucao_patrimonio_liquido')
  }

  getList() {
    const headers = new HttpHeaders()
    return this.http.get(
      'evolucao_patrimonio_liquido/paginado?take=0&sort=exercicioReferencia",false',
      {
        headers,
      },
    )
  }
}
