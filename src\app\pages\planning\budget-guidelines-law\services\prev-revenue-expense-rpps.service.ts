import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { Observable } from 'rxjs';
import { PrevidencialRevenueExpenseRpps2018Interface } from '../interfaces/prev-revenue-expense-rpps';

@Injectable({
  providedIn: 'root'
})
export class PrevRevenueExpenseRppsService extends BaseService<
	ResponseDto<PrevidencialRevenueExpenseRpps2018Interface[]>,
	PrevidencialRevenueExpenseRpps2018Interface
> {

  constructor(protected http: HttpClient) {
		super(http, 'receitas_despesas_previdenciarias_rpps')
	}

	public putBatch(
    batch: PrevidencialRevenueExpenseRpps2018Interface[],
  ): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.put<PrevidencialRevenueExpenseRpps2018Interface[]>(
      `receitas_despesas_previdenciarias_rpps/lote`,
      batch,
      {
        headers,
        observe: 'response',
      },
    )
  }
}
