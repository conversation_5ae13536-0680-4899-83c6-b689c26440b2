import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '@common/services/base/base.service';
import { ResponseDto } from '@pages/planning/shared/dtos/response-dto';
import { PrevidencialTypeInterface } from '../interfaces/previdencial-type';

@Injectable({
  providedIn: 'root'
})
export class PrevidencialTypeService extends BaseService<
	ResponseDto<PrevidencialTypeInterface>,
	PrevidencialTypeInterface
> {

  constructor(protected http: HttpClient) {
		super(http, 'tipo_previdencia')
	}
}
