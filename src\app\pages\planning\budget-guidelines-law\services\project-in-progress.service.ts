import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BaseService } from '@common/services/base/base.service'
import {
  ProjectInProgressInterface,
  ProjectInProgressReturnInterface,
} from '../interfaces/project-in-progress.model'

@Injectable({
  providedIn: 'root',
})
export class ProjectInProgressService extends BaseService<
  ProjectInProgressReturnInterface,
  ProjectInProgressInterface
> {
  constructor(protected http: HttpClient) {
    super(http, 'projetos_em_andamento')
  }
}
