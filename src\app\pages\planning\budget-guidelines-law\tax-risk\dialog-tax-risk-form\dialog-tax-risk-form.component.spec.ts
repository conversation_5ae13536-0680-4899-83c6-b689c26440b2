import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogTaxRiskComponent } from './dialog-tax-risk-form.component';

describe('DialogTaxRiskComponent', () => {
  let component: DialogTaxRiskComponent;
  let fixture: ComponentFixture<DialogTaxRiskComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DialogTaxRiskComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogTaxRiskComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
