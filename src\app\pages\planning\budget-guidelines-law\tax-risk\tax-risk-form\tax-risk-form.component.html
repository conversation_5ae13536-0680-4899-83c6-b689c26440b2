<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>

    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="taxRisk">
      <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
        [style]="'basic'"
        [size]="'small'"
        [shape]="'rectangle'"
        formControlName="nome"
        name="Nome"
        label="Nome"
        placeholder="Nome"
        errorMessage="É obrigatório preencher o nome"
        maxlength="100"
        required="true"
      >
      </eqp-nebular-input>
      </div>
      <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Risco fiscal tce" label="Risco fiscal tce"
          placeholder="Risco fiscal tce" formControlName="riscoFiscalTceUuid" [dataSource]="riscoFiscalTceData"
          valueExpr="uuid" displayExpr="nome" required="true"></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button
        type="button"
        class="btn btn-dark"
        (click)="cancelar(null)"
      >Voltar
      </button>
      <button
        *ngIf="formulario.get('uuid')?.value"
        type="button"
        class="btn btn-danger ml-3 float-md-right"
        (click)="remover()"
      >Apagar
      </button>
      <button
        type="button"
        [disabled]="formulario.invalid"
        class="btn btn-success float-md-right"
        (click)="gravar()"
      >Gravar
      </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>