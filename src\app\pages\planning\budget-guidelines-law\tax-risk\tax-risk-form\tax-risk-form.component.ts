import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import {
  ModalConfirmarComponent,
} from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'

import {
  ToastrService,
} from '../../../../../@common/services/toastr/toastr.service'
import { MenuService } from '../../../../menu.service'
import { TaxRiskService } from '../tax-risk.service'

@Component({
  selector: 'eqp-tax-risk-form',
  templateUrl: './tax-risk-form.component.html',
  styleUrls: ['./tax-risk-form.component.scss'],
})
export class TaxRiskFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Tipo risco fiscal'
  public formulario: FormGroup
  public nomeData: any
  public riscoFiscalTceData: any
  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<TaxRiskFormComponent>

  constructor(
    private formBuilder: FormBuilder,
    private service: TaxRiskService,
    private route: ActivatedRoute,
    public router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/tipo-risco-fiscal')
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  public ngOnDestroy(): void {}

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      taxRisk: this.formBuilder.group({
        uuid: [''],
        nome: [null, Validators.required],
        riscoFiscalTceUuid: [null, Validators.required],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid && !this.modal) this.buscar(uuid)
      else this.loadSelects()
    })
  }

  private loadSelects(): void {
    this.riscoFiscalTceData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'risco_fiscal_tce/paginado',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('taxRisk').patchValue(data.dados)
        this.loadSelects()
      })
  }

  public cancelar(retorno): void {
    if (!this.modal) {
      this.gravarParametros()
      this.router.navigate([`tipo-risco-fiscal`])
    } else {
      this.ref.close(retorno)
    }
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.formulario.get('taxRisk.uuid').value)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Tipo risco fiscal excluído com sucesso.',
              })
              this.cancelar(null)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      if (this.formulario.get('taxRisk.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getRiscoFiscalDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Tipo risco fiscal criado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getRiscoFiscalDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Tipo risco fiscal atualizado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private getRiscoFiscalDto(): any {
    const dto = this.formulario.getRawValue()

    if (dto.taxRisk.uuid !== '') {
      dto.taxRisk.riscoFiscalTceUuid = dto.taxRisk.riscoFiscalTceUuid.uuid;
    }

    return dto.taxRisk
  }
}
