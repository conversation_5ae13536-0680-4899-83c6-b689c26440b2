import { Component, OnDestroy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { ModalConfirmarComponent } from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogService } from '@nebular/theme'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import { MenuService } from '../../../../menu.service'
import { TaxRiskService } from '../tax-risk.service'

@Component({
  selector: 'eqp-tax-risk-list',
  templateUrl: './tax-risk-list.component.html',
  styleUrls: ['./tax-risk-list.component.scss'],
})
export class TaxRiskListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Tipo risco fiscal'

  public gridData: any

  private subscription: Subscription
  constructor(
    private service: TaxRiskService,
    private toastr: ToastrService,
    public router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService, router)
    this.permissao('/tipo-risco-fiscal')
  }

  public ngOnInit(): void {
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'risco_fiscal/paginado',
      10,
    )
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Tipo risco fiscal'
          item.options.hint = 'Novo tipo risco fiscal'
          item.options.onClick = () => this.novoRegistro()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`tipo-risco-fiscal/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([`tipo-risco-fiscal/edit/${uuid}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Tipo risco fiscal excluído com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
