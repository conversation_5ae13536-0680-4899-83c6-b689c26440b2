<eqp-nebular-dialog
  [dialogTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonId]="'voltar-conta-despesa'"
  [bottomLeftButtonTitle]="'Voltar'"
  (bottomLeftButtonEmitter)="dismiss()"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonId]="'confirmar-conta-despesa'"
  [rightFirstButtonDisabled]="form?.invalid"
  (rightFirstButtonEmitter)="save(form)"
>
  <form [formGroup]="form">
    <div class="row">
      <div [class]="'col-md-12 col-sm-12 mb-4'">
        <eqp-search-field
          formControlName="previsaoInicialDespesaFonte"
          name="Conta de despesa"
          label="Conta de despesa"
          uri="previsao_inicial_despesa_fonte/paginado"
          dialogTitle="Conta de despesa"
          searchColumnsType="initialExpenseForecastSourceColumns"
          messageNotFound="Conta de despesa não encontrada."
          [multipleNames]="[
            'previsaoInicialDespesa.funcional',
            'previsaoInicialDespesa.planoDespesa.codigo',
            'fonteRecurso.codigoEhNome'
          ]"
          [returnAllData]="true"
          waitingTime="1000"
          [onlyNumberKey]="true"
        ></eqp-search-field>
      </div>
    </div>
  </form>
</eqp-nebular-dialog>
