import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { finalize } from 'rxjs/operators'
import { ExpenseGroupInterface } from '../../interface/expense-group.model'
import { ExpenseAccountGroupService } from '../../services/expense-account-group.service'

@Component({
  selector: 'eqp-load-standard-plan',
  styleUrls: ['./expense-account-group-form.component.scss'],
  templateUrl: './expense-account-group-form.component.html',
})
export class ExpenseAccountGroupFormComponent
  extends BaseTelasComponent
  implements On<PERSON><PERSON><PERSON>, OnD<PERSON>roy
{
  @Input() grupoDespesa: ExpenseGroupInterface
  public loading: boolean = false
  public pageTitle: string = 'Conta de despesa'
  public dataSource: any
  public previsaoInicialUuid: any
  public grupoDespesaUuid: string

  public form: FormGroup

  private subscription: Subscription

  constructor(
    private service: ExpenseAccountGroupService,
    public menuService: MenuService,
    protected ref: NbDialogRef<ExpenseAccountGroupFormComponent>,
    public router: Router,
    private fb: FormBuilder,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('/grupo_despesa_fonte')
  }

  public ngOnInit(): void {
    this.form = this.fb.group({
      grupoDespesaUuid: [''],
      previsaoInicialDespesaFonte: ['', Validators.required],
    })
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public dismiss(): void {
    this.ref.close(null)
  }

  save({ value, valid }: { value: any; valid: boolean }) {
    if (!valid) return
    value.grupoDespesaUuid = this.grupoDespesa.uuid
    value.previsaoInicialDespesaFonteUuid = value.previsaoInicialDespesaFonte?.uuid

    this.loading = true
    this.subscription = this.service
      .post(value)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Conta de despesa cadastrada com sucesso.',
          })
          this.ref.close('S')
        },
        (err: any) => this.toastr.bulkSend(err.mensagens),
      )
  }
}
