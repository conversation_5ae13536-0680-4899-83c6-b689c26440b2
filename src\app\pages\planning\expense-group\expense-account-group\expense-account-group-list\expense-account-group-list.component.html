<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="fetchGrid()"
>
  <ng-container>
    <dx-data-grid
      id="expenseAccountGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="false"
      [columnAutoWidth]="false"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="false"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparing($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL'"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="previsaoInicialDespesaFonte.codigo"
        caption="Código"
        alignment="left"
      ></dxi-column>

      <dxi-column
        dataField="previsaoInicialDespesaFonte.previsaoInicialDespesa.funcional"
        caption="Funcional"
      ></dxi-column>

      <dxi-column
        dataField="previsaoInicialDespesaFonte.previsaoInicialDespesa.planoDespesa.codigo"
        caption="Natureza"
      ></dxi-column>

      <dxi-column
        dataField="previsaoInicialDespesaFonte.fonteRecurso.codigoEhNome"
        caption="Fonte"
      ></dxi-column>

      <dxi-column
        dataField="previsaoInicialDespesaFonte.valorAutorizado"
        caption="Valor Autorizado"
        alignment="left"
        [format]="currencyFormat"
        [calculateDisplayValue]="calculateValorAutorizado"
      ></dxi-column>
      <dxi-column
        dataField="uuid"
        caption=""
        [width]="80"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>
      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          *ngIf="nivelPermissao === 'FULL'"
          title="Remover"
          (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
