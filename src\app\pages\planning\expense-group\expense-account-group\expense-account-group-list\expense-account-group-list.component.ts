import { Component, Input, OnInit } from '@angular/core'
import { FormGroup } from '@angular/forms'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'
import { ExpenseGroupInterface } from '../../interface/expense-group.model'
import { ExpenseAccountGroupService } from '../../services/expense-account-group.service'
import { ExpenseAccountGroupFormComponent } from '../expense-account-group-form/expense-account-group-form.component'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'

@Component({
  selector: 'eqp-expense-account-group-list',
  templateUrl: './expense-account-group-list.component.html',
  styleUrls: ['./expense-account-group-list.component.scss'],
})
export class ExpenseAccountGroupListComponent
  extends BaseTelasComponent
  implements OnInit
{
  @Input() grupoDespesa: ExpenseGroupInterface
  public loading: boolean = false
  public pageTitle: string = 'Conta de despesa'
  public dataSource: any
  public previsaoInicialUuid: any
  public grupoDespesaUuid: string
  public currencyFormat = currencyFormat

  // Função para calcular o valor autorizado como número
  public calculateValorAutorizado(rowData: any): number {
    if (!rowData || !rowData.previsaoInicialDespesaFonte || rowData.previsaoInicialDespesaFonte.valorAutorizado === undefined) {
      return 0;
    }

    const value = rowData.previsaoInicialDespesaFonte.valorAutorizado;

    if (value === null || value === undefined) return 0;
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      // Remove caracteres não numéricos, exceto ponto decimal
      const numericValue = value.replace(/[^0-9.]/g, '');
      return parseFloat(numericValue) || 0;
    }
    return 0;
  }

  public form: FormGroup

  private subscription: Subscription

  constructor(
    private service: ExpenseAccountGroupService,
    public menuService: MenuService,
    private dialogService: NbDialogService,
    public router: Router,
    private toastr: ToastrService,
  ) {
    super(menuService, router)
    this.permissao('/grupo_despesa_fonte')
  }

  public ngOnInit(): void {
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  fetchGrid() {
    this.dataSource = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        'grupo_despesa_fonte/paginado',
        10,
        'grupoDespesa',
        this.grupoDespesa.uuid,
      ),
    }
  }

  public onToolbarPreparing(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo'
          item.options.hint = 'Novo'
          item.options.onClick = () => this.openExpenseAccountModal()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public openExpenseAccountModal(): void {
    const dialogRef = this.dialogService.open(
      ExpenseAccountGroupFormComponent,
      {
        context: {
          grupoDespesa: this.grupoDespesa,
        },
        closeOnEsc: false,
        closeOnBackdropClick: false,
      },
    )
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        this.fetchGrid()
      }
    })
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(retorno => {
      if (retorno === 'S') {
        this.service.delete(uuid).subscribe(() => {
          this.toastr.send({
            success: true,
            message: 'Conta de despesa excluída com sucesso.',
          })
          this.fetchGrid()
        })
      }
    })
  }
}
