<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="acao">
      <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          readonly="true"
          name="Número"
          label="Número"
          placeholder="Número"
          formControlName="numero"
        >
        </eqp-nebular-input>
      </div>

      <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo de controle"
          label="Tipo de controle"
          placeholder="Tipo de controle"
          [dataSource]="tipoControleData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="tipoControleAcaoUuid"
          required="true"
        ></eqp-nebular-select>
      </div>

      <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
        <eqp-search-field
          label="Ação"
          name="Ação"
          dialogTitle="Ação"
          formControlName="ppaAcaoUuid"
          searchColumnsType="codeNameColumns"
          uri="ppa_acao_nao_correlacionada/ppa/ppa_acao/{{ppaUuid}}/1"
          [returnAllData]="true"
        >
        </eqp-search-field>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nota explicativa"
          label="Nota explicativa"
          placeholder="Nota explicativa"
          formControlName="notaExplicativa"
          maxlength="250"
          required="true"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Inclusão no TCE"
          label="Inclusão no TCE"
          placeholder="Inclusão no TCE"
          formControlName="dataInclusaoTce"
          required="true"
          [minDate]="menorData"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <!-- </nb-tab>
    </nb-tabset> -->
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>
        <button
          *ngIf="formulario.get('acao.uuid').value && nivelPermissao === 'FULL'"
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          *ngIf="
            (formulario.get('acao.uuid').value &&
              nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
