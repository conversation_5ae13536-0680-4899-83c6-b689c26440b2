<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row align">
      <div class="col-md-4">
        <h5>{{ pageTitle }}</h5>
      </div>
      <div class="col-md-8 d-flex" [formGroup]="formulario">
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Plano Plurianual"
            placeholder=""
            readonly="true"
            formControlName="planoPlurianual"
          >
          </eqp-nebular-input>
        </div>
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Versão/Situação"
            placeholder=""
            readonly="true"
            formControlName="versaoLabel"
          >
          </eqp-nebular-input>
        </div>
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Lei"
            placeholder=""
            readonly="true"
            formControlName="leiLabel"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <nb-tabset formGroupName="acao">
      <nb-tab tabTitle="Ação">
        <div class="row">
          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              [readonly]="formulario.get('acao.uuid').value"
              name="Código"
              label="Código"
              placeholder="Código"
              formControlName="codigo"
              primaryMask="0999"
            >
            </eqp-nebular-input>
          </div>

          <div [class]="'col-md-6 col-sm-12 mb-4'">
            <eqp-nebular-select
              *ngIf="planoPlurianual"
              [size]="'small'"
              [shape]="'rectangle'"
              name="Plano PMS/ECA"
              label="Plano PMS/ECA"
              placeholder=""
              valueExpr="uuid"
              displayExpr="numeroVersao"
              [dataSource]="ppaVersionData"
              formControlName="ppaVersaoPmsEcaUuid"
              [searchExprOption]="[
                'situacaoVersaoId.nome',
                'ppaEscopoDocumentoId.nome'
              ]"
            ></eqp-nebular-select>
          </div>

          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <eqp-nebular-select
              [size]="'small'"
              [shape]="'rectangle'"
              name="Região"
              label="Região"
              placeholder="Região"
              [dataSource]="regiaoData"
              valueExpr="uuid"
              displayExpr="nome"
              formControlName="regiaoUuid"
            ></eqp-nebular-select>
          </div>

          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <eqp-nebular-select
              [size]="'small'"
              [shape]="'rectangle'"
              name="Area de atuação"
              label="Area de atuação"
              placeholder="Area de atuação"
              [dataSource]="areaAtuacaoData"
              valueExpr="uuid"
              displayExpr="nome"
              formControlName="areaAtuacaoUuid"
            ></eqp-nebular-select>
          </div>
          <!-- //TODO: Verificar se é necessário -->
          <!-- <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [style]="'date'"
              [size]="'small'"
              [shape]="'rectangle'"
              name="Inclusão no TCE"
              label="Inclusão no TCE"
              placeholder="Inclusão no TCE"
              formControlName="dataInclusaoTce"
              [disabled]="!formulario.get('acao.ppaEscopoDocumentoUuid').value"
              [required]="formulario.get('acao.ppaEscopoDocumentoUuid').value"
            >
            </eqp-nebular-input>
          </div> -->
        </div>
      </nb-tab>
      <nb-tab tabTitle="Movimento">
        <div class="label-codigo">
          {{ 'Ação: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #acaoMovimentoGrid
          id="acaoMovimentoGrid"
          [dataSource]="movimentoData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingMovimento($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar movimento"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="ppaAcaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaAcaoNome"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="numero"
            caption="Número"
            alignment="left"
            sortOrder="asc"
          ></dxi-column>
          <dxi-column
            dataField="tipoMovimentoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoMovimentoNome"
            caption="Tipo de movimento"
          ></dxi-column>
          <dxi-column
            dataField="dataMovimento"
            caption="Data do movimento"
            cellTemplate="dataTela"
          ></dxi-column>
          <dxi-column dataField="nomeAcao" caption="Nome"></dxi-column>
          <dxi-column
            dataField="dataInicial"
            caption="Data inicial"
            cellTemplate="dataTela"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="dataFinal"
            caption="Data final"
            cellTemplate="dataTela"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="naturezaAcaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="naturezaAcaoNome"
            caption="Natureza da ação"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoExecucaoAcaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoExecucaoAcaoNome"
            caption="tipo de execução da ação"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoAcaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoAcaoNome"
            caption="tipo da ação"
          ></dxi-column>
          <dxi-column
            dataField="funcaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="funcaoNome"
            caption="Função"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="subfuncaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="subfuncaoNome"
            caption="Subfunção"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="unidadeMedidaUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="unidadeMedidaNome"
            caption="Unidade de medida"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="unidadeMedidaTceUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="produtoacaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="produtoacaoNome"
            caption="Produto da ação"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="flagAcaoContinua"
            caption="Ação continua"
          ></dxi-column>
          <dxi-column
            dataField="entidadeExecutorUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="entidadeExecutorNome"
            caption="Entidade executor"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="entidadeExecutorOrgaoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="entidadeExecutorOrgaoNome"
            caption="Orgão executor"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="notaExplicativa"
            caption="Nota explicativa"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="descricaoComplementar"
            caption="Descrição complementar"
            [visible]="false"
          ></dxi-column>

          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarMovimento(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === movimentoData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerMovimento(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
          <div *dxTemplate="let data of 'dataTela'">
            {{ dataTela(data.value) }}
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Vincular programa">
        <div class="label-codigo">
          {{ 'Ação: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #programaGrid
          id="programaGrid"
          [dataSource]="programaData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingPrograma($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar programa"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="uuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaProgramaUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaProgramaNome"
            caption="Programa"
          ></dxi-column>
          <dxi-column
            dataField="ppaProgramaObjetivoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaProgramaObjetivoNome"
            caption="Objetivo"
          ></dxi-column>
          <dxi-column
            dataField="dataCancelamento"
            caption="Data do cancelamento"
            cellTemplate="dataTela"
          ></dxi-column>
          <dxi-column
            dataField="notaExplicativa"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="leiUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarPrograma(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === programaData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerPrograma(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
          <div *dxTemplate="let data of 'dataTela'">
            {{ dataTela(data.value) }}
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Aplicação anual">
        <div class="label-codigo">
          {{ 'Ação: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #anoGrid
          id="anoGrid"
          [dataSource]="anoData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingAno($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar ano"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="uuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ano"
            caption="Ano"
            alignment="left"
            sortOrder="asc"
          ></dxi-column>
          <dxi-column
            dataField="movimentos"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="avaliacao"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="acoesAnuaisCorrelacionadas"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarAno(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === anoData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerAno(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Iniciativa">
        <div class="label-codigo">
          {{ 'Ação: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #iniciativaGrid
          id="iniciativaGrid"
          [dataSource]="iniciativaData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingIniciativa($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar iniciativa"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="uuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="codigo"
            caption="Número"
            alignment="left"
            sortOrder="asc"
          ></dxi-column>
          <dxi-column dataField="descricao" caption="Descrição"></dxi-column>
          <dxi-column
            dataField="unidadeMedidaUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="unidadeMedidaNome"
            caption="Unidade de medida"
          ></dxi-column>
          <dxi-column
            dataField="notaExplicativa"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="exercicios"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarIniciativa(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === iniciativaData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerIniciativa(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Correlação" *ngIf="planoPlurianual">
        <div class="label-codigo">
          {{ 'Ação: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #correlacaoGrid
          id="correlacaoGrid"
          [dataSource]="correlacaoData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingCorrelacao($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>

          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            caption="Código"
            dataField="ppaAcaoCorrelacao.codigo"
            alignment="left"
          ></dxi-column>

          <dxi-column
            caption="Nome"
            dataField="ppaAcaoCorrelacao.nome"
          ></dxi-column>

          <dxi-column
            caption="Lei/Ato"
            dataField="ppaAcaoCorrelacao.ppaVersaoUuid.leiPpa.codigo"
            alignment="left"
          ></dxi-column>

          <dxi-column
            caption="Tipo Documento Lei/Ato"
            dataField="ppaAcaoCorrelacao.ppaVersaoUuid.leiPpa.tipoDocumento.nome"
          ></dxi-column>

          <dxi-column caption="Escopo Lei/Ato" dataField="escopo"></dxi-column>

          <dxi-column
            caption="N° Lei/Ato"
            dataField="ppaAcaoCorrelacao.ppaVersaoUuid.leiPpa.numero"
            alignment="left"
          ></dxi-column>

          <dxi-column
            caption="Ano Lei/Ato"
            dataField="ppaAcaoCorrelacao.ppaVersaoUuid.leiPpa.ano"
            alignment="left"
          ></dxi-column>

          <dxi-column
            caption="Status"
            dataField="status"
            alignment="left"
          ></dxi-column>

          <dxi-column
            caption=""
            dataField="uuid"
            [width]="120"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Cancelar"
              (click)="cancel(data.value)"
              class="dx-link dx-link-edit fas fa-undo dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              title="Alterar"
              (click)="edit(data.value)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              title="Remover"
              (click)="remove(data.value)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>
        <button
          *ngIf="formulario.get('acao.uuid').value && nivelPermissao === 'FULL'"
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          *ngIf="
            (formulario.get('acao.uuid').value &&
              nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
