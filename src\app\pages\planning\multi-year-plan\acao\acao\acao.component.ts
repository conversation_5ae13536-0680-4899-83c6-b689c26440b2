import { Component, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import {
  getMomentDate,
  getParsedDate,
  getParsedDateTela,
} from '@common/helpers/parsers'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { DxDataGridComponent } from 'devextreme-angular'
import DataSource from 'devextreme/data/data_source'
import { distinctUntilChanged, finalize, first, map } from 'rxjs/operators'
import { AcaoProgramaComponent } from './../acao-programa/acao-programa.component'
import { AcaoService } from './../acao.service'
import { AnoAcaoComponent } from './../ano-acao/ano-acao.component'
import { IniciativaAcaoComponent } from './../iniciativa-acao/iniciativa-acao.component'
import { MovimentoAcaoComponent } from './../movimento-acao/movimento-acao.component'
import { CriarCorrelacaoModalComponent } from '../criar-correlacao-modal/criar-correlacao-modal.component'

@Component({
  selector: 'eqp-acao',
  templateUrl: './acao.component.html',
  styleUrls: ['./acao.component.scss'],
})
export class AcaoComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Ação'
  public acaoRouterUuid: ''
  public formulario: FormGroup
  private versaoPpaUuid: string
  private ppaUuid: string
  public ppaEscopoDocumentoData: any
  public regiaoData: any
  public areaAtuacaoData: any
  public programaData: any
  public movimentoData: any
  public iniciativaData: any
  public anoData: any
  public leiData: any
  public planoPlurianual: boolean
  public correlacaoData: DataSource
  public ppaVersionData: DataSource

  @ViewChild('acaoMovimentoGrid', { static: false })
  acaoMovimentoGrid: DxDataGridComponent
  @ViewChild('programaGrid', { static: false })
  programaGrid: DxDataGridComponent
  @ViewChild('iniciativaGrid', { static: false })
  iniciativaGrid: DxDataGridComponent
  @ViewChild('anoGrid', { static: false })
  anoGrid: DxDataGridComponent

  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<AcaoComponent>

  private versao: any
  public ppaDescricao: string
  public codigoEhNome: string

  constructor(
    private formBuilder: FormBuilder,
    public menuService: MenuService,
    private route: ActivatedRoute,
    public router: Router,
    private service: AcaoService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao('/acao')

    this.versaoPpaUuid = JSON.parse(localStorage.getItem('acaoVersaoPpaUuid'))
    this.ppaUuid = JSON.parse(localStorage.getItem('acaoPpaUuid'))
    localStorage.removeItem('acaoVersaoPpaUuid')
    localStorage.removeItem('acaoPpaUuid')
    if (this.versaoPpaUuid == null || this.ppaUuid == null) {
      this.cancelar(null)
    } else {
      this.service
        .getVersionIndividual(this.versaoPpaUuid)
        .pipe(first())
        .subscribe(data => {
          this.planoPlurianual =
            data.dados.ppaEscopoDocumentoId.nome === 'Plano Plurianual'
          this.formulario.patchValue({
            planoPlurianual: data.dados.ppaId.descricao,
            versaoLabel:
              data.dados.numeroVersao +
              ' - ' +
              data.dados.situacaoVersaoId.nome +
              ' - ' +
              data.dados.ppaEscopoDocumentoId.nome,
            leiLabel: data.dados?.leiPpa
              ? `${data.dados?.leiPpa?.tipoDocumento.nome} - ${data.dados.leiPpa?.numero} / ${data.dados.leiPpa?.ano}`
              : ' - ',
          })
          this.ppaDescricao = data.dados.ppaId?.descricao
          this.versao = data.dados
        })
    }
  }

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
    this.changePmsEca()
  }

  ngOnDestroy(): void {
    localStorage.removeItem('ppaUuid')
    localStorage.removeItem('ppaVersaoPmsEca')
  }

  changePmsEca() {
    this.formulario
      .get('acao')
      .get('ppaVersaoPmsEcaUuid')
      .valueChanges.subscribe(value => {
        if (value) {
          localStorage.setItem('ppaVersaoPmsEca', JSON.stringify(value))
        }
      })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      this.changeEscopo()
      this.change()
      const uuid = params['uuid']
      this.acaoRouterUuid = uuid
      if (uuid && !this.modal) this.buscar(uuid)
      else {
        this.movimentoData = new DataSource({
          store: [],
          paginate: false,
        })
        this.programaData = new DataSource({
          store: [],
          paginate: false,
        })
        this.iniciativaData = new DataSource({
          store: [],
          paginate: false,
        })
        this.anoData = new DataSource({
          store: [],
          paginate: false,
        })
        this.loadSelects()
      }
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        map(data => {
          this.codigoEhNome = data.dados.codigo + ' - ' + data.dados.nome
          if (this.planoPlurianual) {
            this.carregarCorrelacao(data.dados?.uuid)
          }
          const movimentos = []
          data.dados.movimentos.forEach(item => {
            movimentos.push({
              ...item,
              ppaAcaoUuid: item.ppaAcaoUuid ? item.ppaAcaoUuid.uuid : null,
              ppaAcaoNome: item.ppaAcaoUuid ? item.ppaAcaoUuid.nome : null,
              tipoMovimentoUuid: item.tipoMovimentoUuid
                ? item.tipoMovimentoUuid.uuid
                : null,
              tipoMovimentoNome: item.tipoMovimentoUuid
                ? item.tipoMovimentoUuid.nome
                : null,
              naturezaAcaoUuid: item.naturezaAcaoUuid
                ? item.naturezaAcaoUuid.uuid
                : null,
              naturezaAcaoNome: item.naturezaAcaoUuid
                ? item.naturezaAcaoUuid.nome
                : null,
              tipoExecucaoAcaoUuid: item.tipoExecucaoAcaoUuid
                ? item.tipoExecucaoAcaoUuid.uuid
                : null,
              tipoExecucaoAcaoNome: item.tipoExecucaoAcaoUuid
                ? item.tipoExecucaoAcaoUuid.nome
                : null,
              tipoAcaoUuid: item.tipoAcaoUuid ? item.tipoAcaoUuid.uuid : null,
              tipoAcaoNome: item.tipoAcaoUuid ? item.tipoAcaoUuid.nome : null,
              funcaoUuid: item.funcaoUuid ? item.funcaoUuid.uuid : null,
              funcaoNome: item.funcaoUuid ? item.funcaoUuid.nome : null,
              subfuncaoUuid: item.subfuncaoUuid
                ? item.subfuncaoUuid.uuid
                : null,
              subfuncaoNome: item.subfuncaoUuid
                ? item.subfuncaoUuid.nome
                : null,
              unidadeMedidaUuid: item.unidadeMedidaUuid
                ? item.unidadeMedidaUuid.uuid
                : null,
              unidadeMedidaNome: item.unidadeMedidaUuid
                ? item.unidadeMedidaUuid.nome
                : null,
              unidadeMedidaTceUuid: item.unidadeMedidaUuid
                ? item.unidadeMedidaUuid.unidadeMedidaTceUuid
                : null,
              produtoacaoUuid: item.produtoacaoUuid
                ? item.produtoacaoUuid.uuid
                : null,
              produtoacaoNome: item.produtoacaoUuid
                ? item.produtoacaoUuid.nome
                : null,
              entidadeExecutorOrgaoUuid: item.entidadeExecutorOrgaoUuid
                ? item.entidadeExecutorOrgaoUuid.uuid
                : null,
              entidadeExecutorOrgaoNome: item.entidadeExecutorOrgaoUuid
                ? item.entidadeExecutorOrgaoUuid.nome
                : null,
              entidadeExecutorUuid: item.entidadeExecutorUuid
                ? item.entidadeExecutorUuid.uuid
                : null,
              entidadeExecutorNome: item.entidadeExecutorUuid
                ? item.entidadeExecutorUuid.nome
                : null,
              dataMovimento: item.dataMovimento
                ? getMomentDate(item.dataMovimento)
                : null,
              dataInicial: item.dataInicial
                ? getMomentDate(item.dataInicial)
                : null,
              dataFinal: item.dataFinal ? getMomentDate(item.dataFinal) : null,
              flagAcaoContinua: item.flagAcaoContinua === 'S',
            })
          })

          const programas = []
          if (data.dados.programas) {
            data.dados.programas.forEach(item => {
              programas.push({
                ...item,
                ppaProgramaUuid: item.ppaPrograma.uuid,
                ppaProgramaNome: item.ppaPrograma.nome,
                ppaProgramaObjetivoUuid: item.ppaProgramaObjetivo
                  ? item.ppaProgramaObjetivo.uuid
                  : null,
                ppaProgramaObjetivoNome: item.ppaProgramaObjetivo
                  ? item.ppaProgramaObjetivo.ppaObjetivo.nome
                  : null,
                dataCancelamento: item.dataCancelamento
                  ? getMomentDate(item.dataCancelamento)
                  : null,
              })
            })
          }

          const iniciativas = []
          if (data.dados.iniciativas) {
            data.dados.iniciativas.forEach(item => {
              const exercicios = []
              if (item.exercicios) {
                item.exercicios.forEach(element => {
                  exercicios.push({
                    ...element,
                  })
                })
              }

              iniciativas.push({
                ...item,
                unidadeMedidaUuid: item.unidadeMedida.uuid,
                unidadeMedidaNome: item.unidadeMedida.nome,
                exercicios: exercicios,
              })
            })
          }

          const anos = []
          if (data.dados.anos) {
            data.dados.anos.forEach(item => {
              const mov = []
              if (item.movimentos) {
                item.movimentos.forEach(element => {
                  mov.push({
                    ...element,
                    tipoMovimentoUuid: element.tipoMovimento.uuid,
                    tipoMovimentoNome: element.tipoMovimento.nome,
                  })
                })
              }
              item.movimentos = mov

              const acoesAnuaisCorrelacionadas = []
              if (item.acoesAnuaisCorrelacionadas) {
                item.acoesAnuaisCorrelacionadas.forEach(element => {
                  acoesAnuaisCorrelacionadas.push({
                    ...element,
                    ppaAcaoAnoCorrelacionadaUuid:
                      element.ppaAcaoAnoCorrelacionada.uuid,
                    ppaAcaoAnoCorrelacionadaNome:
                      element.ppaAcaoAnoCorrelacionada.ppaAcao.nome,
                  })
                })
              }
              item.acoesAnuaisCorrelacionadas = acoesAnuaisCorrelacionadas

              const projetos = []
              if (item.projetosAtividade) {
                item.projetosAtividade.forEach(element => {
                  const projetoAtividadeNome = element.projetoAtividadeUuid
                  .exercicio.entidade
                  ? element.projetoAtividadeUuid.exercicio.entidade.codigo +
                    ' - ' +
                    element.projetoAtividadeUuid.exercicio.entidade.nome
                  : 'Entidade não definida'                  
                  projetos.push({
                    ...element,
                    projetoAtividadeUuid: element.projetoAtividadeUuid.uuid,
                    projetoAtividadeNome:
                      projetoAtividadeNome +
                      ' - ' +
                      element.projetoAtividadeUuid.tipoOrdemNome,
                    ppaAcaoAnoUuid: element.ppaAcaoAnoUuid
                      ? element.ppaAcaoAnoUuid.uuid
                      : null,
                  })
                })
              }
              item.projetosAtividade = projetos

              anos.push({
                ...item,
              })
            })
          }

          data.dados = {
            ...data.dados,
            ppaEscopoDocumentoUuid: data.dados.ppaEscopoDocumentoUuid
              ? data.dados.ppaEscopoDocumentoUuid.uuid
              : null,
            regiaoUuid: data.dados.regiaoUuid
              ? data.dados.regiaoUuid.uuid
              : null,
            areaAtuacaoUuid: data.dados.areaAtuacaoUuid
              ? data.dados.areaAtuacaoUuid.uuid
              : null,
            codigoPmsEca: data.dados.codigopmseca,
            ppaVersaoUuid: data.dados.ppaVersaoUuid.uuid,
            movimentos: movimentos,
            programas: programas,
            iniciativas: iniciativas,
            anos: anos,
          }
          return data
        }),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        if (data.dados.dataInclusaoTce) {
          data.dados.dataInclusaoTce = data.dados.dataInclusaoTce
            ? getMomentDate(data.dados.dataInclusaoTce)
            : null
        }
        if (
          data.dados.acaoCorrelacionadaCancelada &&
          data.dados.acaoCorrelacionadaCancelada.dataCancelamento
        ) {
          data.dados.acaoCorrelacionadaCancelada.dataCancelamento = data.dados
            .acaoCorrelacionadaCancelada.dataCancelamento
            ? getMomentDate(
                data.dados.acaoCorrelacionadaCancelada.dataCancelamento,
              )
            : null
        }
        this.formulario.get('acao').patchValue(data.dados)
        this.formulario
          .get('acao.acaoCorrelacionadaCancelada')
          .patchValue(data.dados.acaoCorrelacionadaCancelada)

        this.movimentoData = new DataSource({
          store: data.dados.movimentos ? data.dados.movimentos : [],
          paginate: false,
        })

        this.programaData = new DataSource({
          store: data.dados.programas ? data.dados.programas : [],
          paginate: false,
        })

        this.iniciativaData = new DataSource({
          store: data.dados.iniciativas ? data.dados.iniciativas : [],
          paginate: false,
        })

        this.anoData = new DataSource({
          store: data.dados.anos ? data.dados.anos : [],
          paginate: false,
        })

        this.loadSelects()
      })
  }

  carregarCorrelacao(acaoUuid: string) {
    this.correlacaoData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_acao_correlacao',
        10,
        'ppaAcaoUuid',
        acaoUuid,
      ),
      paginate: true,
      pageSize: 10,
      map: item => {
        return {
          ...item,
          escopo: item.ppaAcaoCorrelacao.ppaVersaoUuid?.leiPpa ? `${item.ppaAcaoCorrelacao.ppaVersaoUuid?.leiPpa?.escopoDocumentoTce.codigo} - ${item.ppaAcaoCorrelacao.ppaVersaoUuid?.leiPpa?.escopoDocumentoTce.nome}` : '-',
        }
      },
    })
  }

  private loadSelects(): void {
    this.ppaEscopoDocumentoData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_acao/ppa_escopo_documento',
        10,
        'flagVersao',
        'N',
      ),
      paginate: true,
      pageSize: 10,
    })
    this.regiaoData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'ppa_acao/regiao', 10),
      paginate: true,
      pageSize: 10,
    })
    this.areaAtuacaoData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_acao/area_atuacao',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.leiData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'ppa_acao/leis', 10),
      paginate: true,
      pageSize: 10,
    })

    this.ppaVersionData = new DataSource({
      store: this.service.getDataSourceFiltroComposto(
        'uuid',
        'ppa_acao/ppa_versao',
        10,
        `["ppaEscopoDocumentoId.nome","=","Plano Municipal de Saúde","or","ppaEscopoDocumentoId.nome","=","Plano de Ação dos Direitos da Criança e do Adolescente","and","ppaUuid","=",${this.ppaUuid}]`,      ),
      paginate: true,
      pageSize: 10,
      map: data => {
        data.numeroVersao =
          data.numeroVersao +
          ' - ' +
          data.situacaoVersaoId.nome +
          ' - ' +
          data.ppaEscopoDocumentoId.nome
        return data
      },
    })
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      planoPlurianual: [],
      versaoLabel: [],
      leiLabel: [],
      acao: this.formBuilder.group({
        uuid: [null],
        versaoPpaUuid: [null],
        ppaVersaoUuid: [this.versaoPpaUuid],
        ppaVersaoPmsEcaUuid: [null],
        codigo: [null],
        ppaEscopoDocumentoUuid: [null],
        codigoPmsEca: [null],
        regiaoUuid: [null],
        areaAtuacaoUuid: [null],
        dataInclusaoTce: [null],
        acaoCorrelacionadaCancelada: this.formBuilder.group({
          dataCancelamento: [null],
          notaExplicativa: [null],
          leiUuid: [null],
        }),
      }),
    })
  }

  private change(): void {
    this.formulario
      .get('acao.acaoCorrelacionadaCancelada.dataCancelamento')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.validarCancelamento()
      })
    this.formulario
      .get('acao.acaoCorrelacionadaCancelada.notaExplicativa')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.validarCancelamento()
      })
    this.formulario
      .get('acao.acaoCorrelacionadaCancelada.leiUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.validarCancelamento()
      })
  }

  private validarCancelamento(): void {
    const dataCancelamento = this.formulario.get(
      'acao.acaoCorrelacionadaCancelada.dataCancelamento',
    ).value
    const notaExplicativa = this.formulario.get(
      'acao.acaoCorrelacionadaCancelada.notaExplicativa',
    ).value
    const leiUuid = this.formulario.get(
      'acao.acaoCorrelacionadaCancelada.leiUuid',
    ).value
    if (dataCancelamento || notaExplicativa || leiUuid) {
      this.formulario
        .get('acao.acaoCorrelacionadaCancelada.dataCancelamento')
        .setValidators([Validators.required])
      this.formulario
        .get('acao.acaoCorrelacionadaCancelada.notaExplicativa')
        .setValidators([Validators.required])
      this.formulario
        .get('acao.acaoCorrelacionadaCancelada.leiUuid')
        .setValidators([Validators.required])
    } else {
      this.formulario
        .get('acao.acaoCorrelacionadaCancelada.dataCancelamento')
        .setValidators([])
      this.formulario
        .get('acao.acaoCorrelacionadaCancelada.notaExplicativa')
        .setValidators([])
      this.formulario
        .get('acao.acaoCorrelacionadaCancelada.leiUuid')
        .setValidators([])
    }
    this.formulario
      .get('acao.acaoCorrelacionadaCancelada.dataCancelamento')
      .patchValue(dataCancelamento)
    this.formulario
      .get('acao.acaoCorrelacionadaCancelada.notaExplicativa')
      .patchValue(notaExplicativa)
    this.formulario
      .get('acao.acaoCorrelacionadaCancelada.leiUuid')
      .patchValue(leiUuid)
  }

  public cancelar(retorno: any): void {
    if (!this.modal) {
      this.gravarParametros()
      localStorage.setItem('acaoPpaUuid', JSON.stringify(this.ppaUuid))
      localStorage.setItem(
        'acaoVersaoPpaUuid',
        JSON.stringify(this.versaoPpaUuid),
      )
      this.router.navigate([`acao`])
    } else {
      this.ref.close(retorno)
    }
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.formulario.get('acao.uuid').value)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Ação excluída com sucesso.',
              })
              this.cancelar(null)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      if (this.formulario.get('acao.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getAcaoDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Ação criada com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getAcaoDto())
      .pipe(first())
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Ação atualizada com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private getAcaoDto(): any {
    const dto = this.formulario.getRawValue()

    dto.acao.dataInclusaoTce = dto.acao.dataInclusaoTce
      ? getParsedDate(dto.acao.dataInclusaoTce)
      : null

    dto.acao.acaoCorrelacionadaCancelada.dataCancelamento = dto.acao
      .acaoCorrelacionadaCancelada.dataCancelamento
      ? getParsedDate(dto.acao.acaoCorrelacionadaCancelada.dataCancelamento)
      : null

    dto.acao.movimentos = []
    this.movimentoData.items().forEach(item => {
      dto.acao.movimentos.push({
        id: item.id,
        ppaAcaoUuid: item.ppaAcaoUuid,
        numero: item.numero,
        tipoMovimentoUuid: item.tipoMovimentoUuid,
        dataMovimento: item.dataMovimento
          ? getParsedDate(item.dataMovimento)
          : null,
        nomeAcao: item.nomeAcao,
        dataInicial: item.dataInicial ? getParsedDate(item.dataInicial) : null,
        dataFinal: item.dataFinal ? getParsedDate(item.dataFinal) : null,
        naturezaAcaoUuid: item.naturezaAcaoUuid,
        tipoExecucaoAcaoUuid: item.tipoExecucaoAcaoUuid,
        tipoAcaoUuid: item.tipoAcaoUuid,
        funcaoUuid: item.funcaoUuid,
        subfuncaoUuid: item.subfuncaoUuid,
        unidadeMedidaUuid: item.unidadeMedidaUuid,
        produtoacaoUuid: item.produtoacaoUuid,
        entidadeExecutorUuid: item.entidadeExecutorUuid,
        entidadeExecutorOrgaoUuid: item.entidadeExecutorOrgaoUuid,
        notaExplicativa: item.notaExplicativa,
        descricaoComplementar: item.descricaoComplementar,
        flagAcaoContinua: item.flagAcaoContinua ? 'S' : 'N',
      })
    })

    dto.acao.programas = []
    this.programaData.items().forEach(item => {
      dto.acao.programas.push({
        id: item.id,
        uuid: item.uuid,
        ppaProgramaUuid: item.ppaProgramaUuid,
        ppaProgramaObjetivoUuid: item.ppaProgramaObjetivoUuid,
        dataCancelamento: item.dataCancelamento
          ? getParsedDate(item.dataCancelamento)
          : null,
        notaExplicativa: item.notaExplicativa,
        leiUuid: item.leiUuid,
      })
    })

    dto.acao.iniciativas = []
    this.iniciativaData.items().forEach(item => {
      dto.acao.iniciativas.push({
        id: item.id,
        uuid: item.uuid,
        codigo: item.codigo,
        descricao: item.descricao,
        unidadeMedidaUuid: item.unidadeMedidaUuid,
        notaExplicativa: item.notaExplicativa,
        exercicios: item.exercicios,
      })
    })

    dto.acao.anos = []
    this.anoData.items().forEach(item => {
      dto.acao.anos.push({
        id: item.id,
        uuid: item.uuid,
        ano: item.ano,
        movimentos: item.movimentos,
        avaliacao: item.avaliacao,
        acoesAnuaisCorrelacionadas: item.acoesAnuaisCorrelacionadas,
        projetosAtividade: item.projetosAtividade,
      })
    })

    return dto.acao
  }

  public dataTela(data): string {
    if (data) return getParsedDateTela(data)
    return ''
  }

  private changeEscopo(): void {
    this.formulario
      .get('acao.ppaEscopoDocumentoUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        const valor = this.formulario.get('acao.dataInclusaoTce').value
        if (value) {
          this.formulario
            .get('acao.dataInclusaoTce')
            .setValidators([Validators.required])
          this.formulario.get('acao.dataInclusaoTce').patchValue(valor)
        } else {
          this.formulario.get('acao.dataInclusaoTce').setValidators([])
          this.formulario.get('acao.dataInclusaoTce').patchValue('')
        }

        this.formulario
          .get('acao.acaoCorrelacionadaCancelada.dataCancelamento')
          .patchValue('')
        this.formulario
          .get('acao.acaoCorrelacionadaCancelada.notaExplicativa')
          .patchValue('')
        this.formulario
          .get('acao.acaoCorrelacionadaCancelada.leiUuid')
          .patchValue('')
      })
  }

  //------------Movimento------------------
  public onToolbarPreparingMovimento(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo movimento'
          item.options.hint = 'Novo movimento'
          item.options.onClick = () => this.onInitNewRow()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRow(): void {
    const lista = []
    this.movimentoData.items().forEach(item => {
      lista.push(item)
    })
    let ultimo
    if (lista.length > 0) {
      ultimo = lista[lista.length - 1]
    }
    const dialogRef = this.dialogService.open(MovimentoAcaoComponent, {
      context: {
        dados: {
          nomeAcao: ultimo?.nomeAcao ? ultimo.nomeAcao : null,
        },
        numeroAtual: lista.length,
        menorData: ultimo?.dataMovimento ? ultimo.dataMovimento : null,
        versao: this.versao,
        podeGravar: this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista.push(retorno)
        this.movimentoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarMovimento(e) {
    const lista = []
    this.movimentoData.items().forEach(item => {
      lista.push(item)
    })

    const movimento: any = {
      id: e.data.id,
      ppaAcaoUuid: e.data.ppaAcaoUuid,
      ppaAcaoNome: e.data.ppaAcaoNome,
      numero: e.data.numero,
      tipoMovimentoUuid: e.data.tipoMovimentoUuid,
      tipoMovimentoNome: e.data.tipoMovimentoNome,
      dataMovimento: e.data.dataMovimento,
      nomeAcao: e.data.nomeAcao,
      dataInicial: e.data.dataInicial,
      dataFinal: e.data.dataFinal,
      naturezaAcaoUuid: e.data.naturezaAcaoUuid,
      naturezaAcaoNome: e.data.naturezaAcaoNome,
      tipoExecucaoAcaoUuid: e.data.tipoExecucaoAcaoUuid,
      tipoExecucaoAcaoNome: e.data.tipoExecucaoAcaoNome,
      tipoAcaoUuid: e.data.tipoAcaoUuid,
      tipoAcaoNome: e.data.tipoAcaoNome,
      funcaoUuid: e.data.funcaoUuid,
      funcaoNome: e.data.funcaoNome,
      subfuncaoUuid: e.data.subfuncaoUuid,
      subfuncaoNome: e.data.subfuncaoNome,
      unidadeMedidaUuid: e.data.unidadeMedidaUuid,
      unidadeMedidaNome: e.data.unidadeMedidaNome,
      produtoacaoUuid: e.data.produtoacaoUuid,
      produtoacaoNome: e.data.produtoacaoNome,
      flagAcaoContinua: e.data.flagAcaoContinua,
      entidadeExecutorUuid: e.data.entidadeExecutorUuid,
      entidadeExecutorNome: e.data.entidadeExecutorNome,
      entidadeExecutorOrgaoUuid: e.data.entidadeExecutorOrgaoUuid,
      entidadeExecutorOrgaoNome: e.data.entidadeExecutorOrgaoNome,
      notaExplicativa: e.data.notaExplicativa,
      descricaoComplementar: e.data.descricaoComplementar,
      unidadeMedidaTceUuid: e.data.unidadeMedidaTceUuid,
    }

    let ultimo
    if (e.rowIndex - 1 >= 0) {
      ultimo = lista[e.rowIndex - 1]
    }

    const dialogRef = this.dialogService.open(MovimentoAcaoComponent, {
      context: {
        dados: movimento,
        numeroAtual: lista.length,
        menorData: ultimo?.dataMovimento ? ultimo.dataMovimento : null,
        versao: this.versao,
        podeGravar:
          (this.formulario.get('acao.uuid').value &&
            this.nivelPermissao === 'EDITOR') ||
          this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista[e.rowIndex] = retorno
        this.movimentoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerMovimento(e) {
    this.acaoMovimentoGrid.instance.deleteRow(e.rowIndex)
  }

  //------------Programa------------------
  public onToolbarPreparingPrograma(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo Programa'
          item.options.hint = 'Novo Programa'
          item.options.onClick = () => this.onInitNewRowPrograma()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRowPrograma(): void {
    const lista = []
    this.programaData.items().forEach(item => {
      lista.push(item)
    })

    const dialogRef = this.dialogService.open(AcaoProgramaComponent, {
      context: {
        dados: {},
        versaoPpaUuid: this.versaoPpaUuid,
        podeGravar: this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        let achou = false
        lista.forEach(item => {
          if (item.ppaProgramaUuid === retorno.ppaProgramaUuid && !achou) {
            achou = true
          }
        })

        if (achou) {
          this.toastr.send({
            error: true,
            message: 'Programa já vinculado na ação',
          })
        } else {
          lista.push(retorno)
        }
        this.programaData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarPrograma(e) {
    const lista = []
    this.programaData.items().forEach(item => {
      lista.push(item)
    })

    const programa: any = {
      uuid: e.data.uuid,
      id: e.data.id,
      ppaProgramaUuid: e.data.ppaProgramaUuid,
      ppaProgramaNome: e.data.ppaProgramaNome,
      ppaProgramaObjetivoUuid: e.data.ppaProgramaObjetivoUuid,
      ppaProgramaObjetivoNome: e.data.ppaProgramaObjetivoNome,
      dataCancelamento: e.data.dataCancelamento,
      notaExplicativa: e.data.notaExplicativa,
      leiUuid: e.data.leiUuid,
    }

    const dialogRef = this.dialogService.open(AcaoProgramaComponent, {
      context: {
        dados: programa,
        versaoPpaUuid: this.versaoPpaUuid,
        podeGravar:
          (this.formulario.get('acao.uuid').value &&
            this.nivelPermissao === 'EDITOR') ||
          this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        let achou = false
        lista.forEach(function (item, i) {
          if (
            item.ppaProgramaUuid === retorno.ppaProgramaUuid &&
            e.rowIndex !== i &&
            !achou
          ) {
            achou = true
          }
        })

        if (achou) {
          this.toastr.send({
            error: true,
            message: 'Programa já vinculado na ação',
          })
        } else {
          lista[e.rowIndex] = retorno
        }
        this.programaData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerPrograma(e) {
    this.programaGrid.instance.deleteRow(e.rowIndex)
  }

  //------------Iniciativa------------------
  public onToolbarPreparingIniciativa(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo iniciativa'
          item.options.hint = 'Novo iniciativa'
          item.options.onClick = () => this.onInitNewRowIniciativa()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRowIniciativa(): void {
    const lista = []
    this.iniciativaData.items().forEach(item => {
      lista.push(item)
    })
    let ultimo
    if (lista.length > 0) {
      ultimo = lista[lista.length - 1]
    }
    const dialogRef = this.dialogService.open(IniciativaAcaoComponent, {
      context: {
        dados: {},
        numeroAtual: lista.length,
        versao: this.versao,
        podeGravar: this.nivelPermissao === 'FULL',
        podeEditar: this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista.push(retorno)
        this.iniciativaData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarIniciativa(e) {
    const lista = []
    this.iniciativaData.items().forEach(item => {
      lista.push(item)
    })

    const iniciativa: any = {
      id: e.data.id,
      uuid: e.data.uuid,
      codigo: e.data.codigo,
      descricao: e.data.descricao,
      unidadeMedidaUuid: e.data.unidadeMedidaUuid,
      notaExplicativa: e.data.notaExplicativa,
      exercicios: e.data.exercicios,
    }

    let ultimo
    if (e.rowIndex - 1 >= 0) {
      ultimo = lista[e.rowIndex - 1]
    }

    const dialogRef = this.dialogService.open(IniciativaAcaoComponent, {
      context: {
        dados: iniciativa,
        numeroAtual: lista.length,
        versao: this.versao,
        podeGravar: this.nivelPermissao === 'FULL',
        podeEditar:
          (this.formulario.get('acao.uuid').value &&
            this.nivelPermissao === 'EDITOR') ||
          this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista[e.rowIndex] = retorno
        this.iniciativaData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerIniciativa(e) {
    this.iniciativaGrid.instance.deleteRow(e.rowIndex)
  }

  //------------ano------------------
  public onToolbarPreparingAno(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo ano'
          item.options.hint = 'Novo ano'
          item.options.onClick = () => this.onInitNewRowAno()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRowAno(): void {
    const lista = []
    this.anoData.items().forEach(item => {
      lista.push(item)
    })

    const dialogRef = this.dialogService.open(AnoAcaoComponent, {
      context: {
        dados: {},
        versao: this.versao,
        podeGravar: this.nivelPermissao === 'FULL',
        podeEditar: this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        let achou = false
        lista.forEach(item => {
          if (item.ano === retorno.ano && !achou) {
            achou = true
          }
        })

        if (achou) {
          this.toastr.send({
            error: true,
            message: 'Ano já vinculado na ação',
          })
        } else {
          lista.push(retorno)
        }
        this.anoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarAno(e) {
    const lista = []
    this.anoData.items().forEach(item => {
      lista.push(item)
    })

    const ano: any = {
      id: e.data.id,
      uuid: e.data.uuid,
      ano: e.data.ano,
      movimentos: e.data.movimentos,
      avaliacao: e.data.avaliacao,
      acoesAnuaisCorrelacionadas: e.data.acoesAnuaisCorrelacionadas,
      projetosAtividade: e.data.projetosAtividade,
    }

    const dialogRef = this.dialogService.open(AnoAcaoComponent, {
      context: {
        dados: ano,
        versao: this.versao,
        podeGravar: this.nivelPermissao === 'FULL',
        podeEditar:
          (this.formulario.get('acao.uuid').value &&
            this.nivelPermissao === 'EDITOR') ||
          this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        let achou = false
        lista.forEach(function (item, i) {
          if (item.ano === retorno.ano && e.rowIndex !== i && !achou) {
            achou = true
          }
        })

        if (achou) {
          this.toastr.send({
            error: true,
            message: 'Ano já vinculado na ação',
          })
        } else {
          lista[e.rowIndex] = retorno
        }
        this.anoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerAno(e) {
    this.anoGrid.instance.deleteRow(e.rowIndex)
  }

  //------------Correlação------------------

  public onToolbarPreparingCorrelacao(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo'
          item.options.hint = 'Novo'
          item.options.onClick = () => this.openCorrelacaoModal()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public openCorrelacaoModal(): void {
    const dialogRef = this.dialogService.open(CriarCorrelacaoModalComponent, {
      context: {
        acaoRouterUuid: this.acaoRouterUuid,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        this.carregarCorrelacao(this.acaoRouterUuid)
      }
    })
  }

  public edit(uuid: string): void {
    const dialogRef = this.dialogService.open(CriarCorrelacaoModalComponent, {
      context: {
        acaoRouterUuid: this.acaoRouterUuid,
        ppaAcaoUuid: uuid,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(() => {
      this.carregarCorrelacao(this.acaoRouterUuid)
    })
  }

  public cancel(uuid: string): void {
    const dialogRef = this.dialogService.open(CriarCorrelacaoModalComponent, {
      context: {
        pageTitle: 'Cancelamento Correlação Ação',
        ppaAcaoUuid: uuid,
        ehCancelar: true,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(() => {
      this.carregarCorrelacao(this.acaoRouterUuid)
    })
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(retorno => {
      if (retorno === 'S') {
        this.service.excluirCorrelacao(uuid).subscribe(() => {
          this.toastr.send({
            success: true,
            message: 'Excluído com sucesso.',
          })
          this.carregarCorrelacao(this.acaoRouterUuid)
        })
      }
    })
  }
}
