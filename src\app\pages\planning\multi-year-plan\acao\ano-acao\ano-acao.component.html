<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'" class="modal-width">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row">
      <div class="col-sm-12 col-md-4 mb-4">
        <eqp-nebular-select [size]="'small'" [shape]="'rectangle'" name="Ano" label="Ano" placeholder="Ano"
          [dataSource]="anoData" valueExpr="chave" displayExpr="nome" formControlName="ano" required="true"
          [disabled]="correlacaoData.items().length > 0"></eqp-nebular-select>
      </div>
    </div>
    <nb-tabset>
      <nb-tab tabTitle="Movimento">
        <dx-data-grid #anoMovimentoGrid id="anoMovimentoGrid" [dataSource]="movimentoData" [allowColumnResizing]="true"
          [showColumnLines]="false" [showRowLines]="false" [showBorders]="false" [wordWrapEnabled]="true"
          [loadPanel]="false" keyExpr="uuid" (onToolbarPreparing)="onToolbarPreparingMovimento($event)">
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel [visible]="true" placeholder="Buscar movimento"></dxo-search-panel>

          <dxo-editing mode="form" [allowUpdating]="false" [allowDeleting]="false" [allowAdding]="podeGravar"
            [useIcons]="true">
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="uuid" caption="" [visible]="false"></dxi-column>

          <dxi-column dataField="tipoMovimentoUuid" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="numero" caption="Número" sortOrder="asc" alignment="left"></dxi-column>
          <dxi-column dataField="tipoMovimentoNome" caption="Tipo do movimento"></dxi-column>
          <dxi-column dataField="dataMovimento" caption="data" cellTemplate="dataTela"></dxi-column>
          <dxi-column dataField="vlrMetaFisicaPrevista" caption="Meta fisica prevista" alignment="left"
            cellTemplate="expectedPhysicalGoalColumn"></dxi-column>
          <div *dxTemplate="let data of 'expectedPhysicalGoalColumn'">
            {{data.value | currency: 'BRL'}}
          </div>
          <dxi-column dataField="vlrRecursoPrevistoLivre" caption="Recurso previsto livre" alignment="left"
            cellTemplate="resourceProvidedFreeColumn"></dxi-column>
          <div *dxTemplate="let data of 'resourceProvidedFreeColumn'">
            {{data.value | currency: 'BRL'}}
          </div>
          <dxi-column dataField="vlrRecursoPrevistoVinculado" caption="Recurso previsto vinculado" alignment="left"
            cellTemplate="forecastCumulativeResourceColumn"></dxi-column>
          <div *dxTemplate="let data of 'forecastCumulativeResourceColumn'">
            {{data.value | currency: 'BRL'}}
          </div>
          <dxi-column dataField="vlrRecursoPrevistoTotal" caption="Recurso previsto total" alignment="left"
            cellTemplate="totalForecastedResourceColumn"></dxi-column>
          <div *dxTemplate="let data of 'totalForecastedResourceColumn'">
            {{data.value | currency: 'BRL'}}
          </div>
          <dxi-column dataField="notaExplicativa" caption="Nota explicativa"></dxi-column>

          <dxi-column caption="" [width]="80" [allowFiltering]="false" [allowSorting]="false"
            cellTemplate="acaoColumn"></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a title="Alterar" (click)="alterarMovimento(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid">
            </a>
            <a *ngIf="
                data.rowIndex === movimentoData.items().length - 1 && podeGravar
              " title="Remover" (click)="removerMovimento(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid">
            </a>
          </div>
          <div *dxTemplate="let data of 'dataTela'">
            {{ dataTela(data.value) }}
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Avaliação" formGroupName="avaliacao">
        <div class="row">
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input [style]="'currency'" [size]="'small'" [shape]="'rectangle'" name="Meta fisica realizada"
              label="Meta fisica realizada" placeholder="Meta fisica realizada" formControlName="vlrMetaFisicaRealizada"
              [options]="{
                prefix: '',
                decimal: ',',
                precision: 2,
                thousands: '.',
                align: 'left',
                allowNegative: false
              }">
            </eqp-nebular-input>
          </div>
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input [style]="'currency'" [size]="'small'" [shape]="'rectangle'" name="Realizado"
              label="Realizado" placeholder="Realizado" formControlName="vlrRealizado" [options]="{
                prefix: '',
                decimal: ',',
                precision: 2,
                thousands: '.',
                align: 'left',
                allowNegative: false
              }">
            </eqp-nebular-input>
          </div>

          <div class="col-sm-12 mb-4">
            <eqp-nebular-input [style]="'basic'" [size]="'small'" [shape]="'rectangle'" name="Nota explicativa"
              label="Nota explicativa" placeholder="Nota explicativa" formControlName="notaExplicativa">
            </eqp-nebular-input>
          </div>
        </div>
      </nb-tab>
      <nb-tab tabTitle="Correlação" *ngIf="this.formulario.get('ano').value">
        <dx-data-grid #correlacaoAnualGrid id="correlacaoAnualGrid" [dataSource]="correlacaoData"
          [allowColumnResizing]="true" [showColumnLines]="false" [showRowLines]="false" [showBorders]="false"
          [wordWrapEnabled]="true" [loadPanel]="false" keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingCorrelacao($event)">
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-search-panel [visible]="true" placeholder="Buscar correlação"></dxo-search-panel>

          <dxo-editing mode="form" [allowUpdating]="false" [allowDeleting]="false" [allowAdding]="podeGravar"
            [useIcons]="true">
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="uuid" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="ppaAcaoAnoCorrelacionadaUuid" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="ppaAcaoAnoCorrelacionadaNome" caption="Ação"></dxi-column>
          <dxi-column dataField="dataInclusaoTce" caption="Data de inclusão TCE" cellTemplate="dataTela"></dxi-column>
          <dxi-column dataField="dataCancelamento" caption="Data do cancelamento" cellTemplate="dataTela"></dxi-column>
          <dxi-column dataField="notaExplicativa" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="leiUuid" caption="" [visible]="false"></dxi-column>
          <dxi-column caption="" [width]="80" [allowFiltering]="false" [allowSorting]="false"
            cellTemplate="acaoColumn"></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a title="Alterar" (click)="alterarCorrelacao(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid">
            </a>
            <a *ngIf="podeGravar" title="Remover" (click)="removerCorrelacao(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid">
            </a>
          </div>
          <div *dxTemplate="let data of 'dataTela'">
            {{ dataTela(data.value) }}
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Projeto Atividade" *ngIf="this.versao.ppaEscopoDocumentoId.id === 2">
        <dx-data-grid #projetoAtividadeGrid id="projetoAtividadeGrid" [dataSource]="projetoAtividadeData"
          [allowColumnResizing]="true" [showColumnLines]="false" [showRowLines]="false" [showBorders]="false"
          [wordWrapEnabled]="true" [loadPanel]="false" keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingProjetoAtividade($event)">
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel [visible]="true" placeholder="Buscar Projeto Atividade"></dxo-search-panel>

          <dxo-editing mode="form" [allowUpdating]="false" [allowDeleting]="false" [allowAdding]="podeGravar"
            [useIcons]="true">
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="uuid" caption="" [visible]="false"></dxi-column>

          <dxi-column dataField="projetoAtividadeUuid" caption="" [visible]="false"></dxi-column>
          <dxi-column dataField="projetoAtividadeNome" caption="Nome"></dxi-column>
          <dxi-column dataField="dataInclusaoTce" caption="" [visible]="false"></dxi-column>

          <dxi-column caption="" [width]="80" [allowFiltering]="false" [allowSorting]="false"
            cellTemplate="acaoColumn"></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a title="Alterar" (click)="alterarProjetoAtividade(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid">
            </a>
            <a *ngIf="podeGravar" title="Remover" (click)="removerProjetoAtividade(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid">
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button *ngIf="podeGravar || (podeEditar && formulario.get('uuid').value)" type="button"
          class="btn btn-success float-md-right" (click)="gravar()">
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
