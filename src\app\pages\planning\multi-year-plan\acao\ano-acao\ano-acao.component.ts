import { Component, Input, OnInit, ViewChild } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import {
  getMomentDate,
  getParsedDate,
  getParsedDateTela,
} from '@common/helpers/parsers'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { DxDataGridComponent } from 'devextreme-angular'
import DataSource from 'devextreme/data/data_source'
import { first } from 'rxjs/operators'
import { ProjetoAtividadeComponent } from '../projeto-atividade/projeto-atividade.component'
import { ToastrService } from './../../../../../@common/services/toastr/toastr.service'
import { CorrelacaoAnualAcaoComponent } from './../correlacao-anual-acao/correlacao-anual-acao.component'
import { MovimentoAnoComponent } from './../movimento-ano/movimento-ano.component'
import { currencyFormat } from '@pages/planning/shared/helpers/format.helper'

@Component({
  selector: 'eqp-ano-acao',
  templateUrl: './ano-acao.component.html',
  styleUrls: ['./ano-acao.component.scss'],
})
export class AnoAcaoComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Ano'
  public formulario: FormGroup
  public anoData: any
  public movimentoData: any
  public correlacaoData: any
  public projetoAtividadeData: any

  @Input() public dados: any
  @Input() public versao: any
  @Input() public podeGravar: boolean = false
  @Input() public podeEditar: boolean = false

  @ViewChild('anoMovimentoGrid', { static: false })
  anoMovimentoGrid: DxDataGridComponent
  @ViewChild('correlacaoAnualGrid', { static: false })
  correlacaoAnualGrid: DxDataGridComponent
  @ViewChild('projetoAtividadeGrid', { static: false })
  projetoAtividadeGrid: DxDataGridComponent

  constructor(
    private formBuilder: FormBuilder,
    public ref: NbDialogRef<AnoAcaoComponent>,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.anoData = new DataSource({
      store: [
        {
          chave: this.versao.ppaId.exercicioInicial,
          nome: this.versao.ppaId.exercicioInicial,
        },
        {
          chave: this.versao.ppaId.exercicioInicial + 1,
          nome: this.versao.ppaId.exercicioInicial + 1,
        },
        {
          chave: this.versao.ppaId.exercicioInicial + 2,
          nome: this.versao.ppaId.exercicioInicial + 2,
        },
        {
          chave: this.versao.ppaId.exercicioInicial + 3,
          nome: this.versao.ppaId.exercicioInicial + 3,
        },
      ],
      paginate: false,
    })

    if (this.dados.movimentos) {
      this.dados.movimentos.forEach(item => {
        item.dataMovimento = getMomentDate(item.dataMovimento)
      })
    }

    if (this.dados.projetosAtividade) {
      this.dados.projetosAtividade.forEach(item => {
        item.dataInclusaoTce = item.dataInclusaoTce
          ? getMomentDate(item.dataInclusaoTce)
          : null
        item.dataCancelamentoTce = item.dataCancelamentoTce
          ? getMomentDate(item.dataCancelamentoTce)
          : null
      })
    }

    if (this.dados.acoesAnuaisCorrelacionadas) {
      this.dados.acoesAnuaisCorrelacionadas.forEach(item => {
        item.dataInclusaoTce = item.dataInclusaoTce
          ? getMomentDate(item.dataInclusaoTce)
          : null
        item.dataCancelamento = item.dataCancelamento
          ? getMomentDate(item.dataCancelamento)
          : null
      })
    }

    this.movimentoData = new DataSource({
      store: this.dados.movimentos ? this.dados.movimentos : [],
      paginate: false,
    })

    this.projetoAtividadeData = new DataSource({
      store: this.dados.projetosAtividade ? this.dados.projetosAtividade : [],
      paginate: false,
    })

    this.correlacaoData = new DataSource({
      store: this.dados.acoesAnuaisCorrelacionadas
        ? this.dados.acoesAnuaisCorrelacionadas
        : [],
      paginate: false,
    })

    this.formulario.patchValue(this.dados)
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      id: [null],
      uuid: [null],
      ano: [null, Validators.required],
      avaliacao: this.formBuilder.group({
        id: [null],
        uuid: [null],
        vlrMetaFisicaRealizada: [null],
        vlrRealizado: [null],
        notaExplicativa: [null],
      }),
    })
  }

  public cancelar(): void {
    this.ref.close(null)
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      const ano = this.formulario.getRawValue()

      ano.movimentos = []
      this.movimentoData.items().forEach(item => {
        ano.movimentos.push({
          id: item.id,
          uuid: item.uuid,
          numero: item.numero,
          tipoMovimentoUuid: item.tipoMovimentoUuid,
          tipoMovimentoNome: item.tipoMovimentoNome,
          dataMovimento: item.dataMovimento
            ? getParsedDate(item.dataMovimento)
            : null,
          vlrMetaFisicaPrevista: item.vlrMetaFisicaPrevista,
          vlrRecursoPrevistoLivre: item.vlrRecursoPrevistoLivre,
          vlrRecursoPrevistoVinculado: item.vlrRecursoPrevistoVinculado,
          vlrRecursoPrevistoTotal: item.vlrRecursoPrevistoTotal,
          notaExplicativa: item.notaExplicativa,
        })
      })

      ano.acoesAnuaisCorrelacionadas = []
      this.correlacaoData.items().forEach(item => {
        ano.acoesAnuaisCorrelacionadas.push({
          id: item.id,
          uuid: item.uuid,
          ppaAcaoAnoCorrelacionadaUuid: item.ppaAcaoAnoCorrelacionadaUuid,
          ppaAcaoAnoCorrelacionadaNome: item.ppaAcaoAnoCorrelacionadaNome,
          dataInclusaoTce: item.dataInclusaoTce
            ? getParsedDate(item.dataInclusaoTce)
            : null,
          dataCancelamento: item.dataCancelamento
            ? getParsedDate(item.dataCancelamento)
            : null,
          leiUuid: item.leiUuid,
          notaExplicativa: item.notaExplicativa,
        })
      })

      ano.projetosAtividade = []
      this.projetoAtividadeData.items().forEach(item => {
        ano.projetosAtividade.push({
          id: item.id,
          uuid: item.uuid,
          projetoAtividadeUuid: item.projetoAtividadeUuid,
          projetoAtividadeNome: item.projetoAtividadeNome,
          dataInclusaoTce: item.dataInclusaoTce
            ? getParsedDate(item.dataInclusaoTce)
            : null,
          dataCancelamentoTce: item.dataCancelamentoTce
            ? getParsedDate(item.dataCancelamentoTce)
            : null,
          notaExplicativa: item.notaExplicativa,
          leiUuid: item.leiUuid,
        })
      })

      this.ref.close(ano)
    }
  }

  //------------Movimento------------------
  public onToolbarPreparingMovimento(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo movimento'
          item.options.hint = 'Novo movimento'
          item.options.onClick = () => this.onInitNewRow()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRow(): void {
    const lista = []
    this.movimentoData.items().forEach(item => {
      lista.push(item)
    })
    let ultimo
    if (lista.length > 0) {
      ultimo = lista[lista.length - 1]
    }
    const dialogRef = this.dialogService.open(MovimentoAnoComponent, {
      context: {
        numeroAtual: lista.length,
        menorData: ultimo?.dataMovimento ? ultimo.dataMovimento : null,
        versao: this.versao,
        podeGravar: this.podeGravar,
        podeEditar: this.podeEditar,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista.push(retorno)
        this.movimentoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarMovimento(e) {
    const lista = []
    this.movimentoData.items().forEach(item => {
      lista.push(item)
    })

    const movimento: any = {
      id: e.data.id,
      uuid: e.data.uuid,
      tipoMovimentoUuid: e.data.tipoMovimentoUuid,
      numero: e.data.numero,
      tipoMovimentoNome: e.data.tipoMovimentoNome,
      dataMovimento: e.data.dataMovimento,
      vlrMetaFisicaPrevista: e.data.vlrMetaFisicaPrevista,
      vlrRecursoPrevistoLivre: e.data.vlrRecursoPrevistoLivre,
      vlrRecursoPrevistoVinculado: e.data.vlrRecursoPrevistoVinculado,
      vlrRecursoPrevistoTotal: e.data.vlrRecursoPrevistoTotal,
      notaExplicativa: e.data.notaExplicativa,
    }

    let ultimo
    if (e.rowIndex - 1 >= 0) {
      ultimo = lista[e.rowIndex - 1]
    }

    const dialogRef = this.dialogService.open(MovimentoAnoComponent, {
      context: {
        dados: movimento,
        numeroAtual: lista.length,
        menorData: ultimo?.dataMovimento ? ultimo.dataMovimento : null,
        versao: this.versao,
        podeGravar: this.podeGravar,
        podeEditar: this.podeEditar,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista[e.rowIndex] = retorno
        this.movimentoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerMovimento(e) {
    this.anoMovimentoGrid.instance.deleteRow(e.rowIndex)
  }

  public dataTela(data): string {
    if (data) return getParsedDateTela(data)
    return ''
  }

  //------------correlação------------------
  public onToolbarPreparingCorrelacao(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Nova correlação'
          item.options.hint = 'Novo correlação'
          item.options.onClick = () => this.onInitNewRowCorrelacao()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRowCorrelacao(): void {
    const lista = []
    this.correlacaoData.items().forEach(item => {
      lista.push(item)
    })

    const dialogRef = this.dialogService.open(CorrelacaoAnualAcaoComponent, {
      context: {
        dados: {},
        versaoPpaUuid: this.versao.uuid,
        ano: this.formulario.get('ano').value,
        podeGravar: this.podeGravar,
        podeEditar: this.podeEditar,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista.push(retorno)
        this.correlacaoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarCorrelacao(e) {
    const lista = []
    this.correlacaoData.items().forEach(item => {
      lista.push(item)
    })

    const correlacao: any = {
      uuid: e.data.uuid,
      id: e.data.id,
      ppaAcaoAnoCorrelacionadaUuid: e.data.ppaAcaoAnoCorrelacionadaUuid,
      ppaAcaoAnoCorrelacionadaNome: e.data.ppaAcaoAnoCorrelacionadaNome,
      dataInclusaoTce: e.data.dataInclusaoTce,
      dataCancelamento: e.data.dataCancelamento,
      notaExplicativa: e.data.notaExplicativa,
      leiUuid: e.data.leiUuid,
    }

    const dialogRef = this.dialogService.open(CorrelacaoAnualAcaoComponent, {
      context: {
        dados: correlacao,
        versaoPpaUuid: this.versao.uuid,
        ano: this.formulario.get('ano').value,
        podeGravar: this.podeGravar,
        podeEditar: this.podeEditar,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista[e.rowIndex] = retorno
        this.correlacaoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerCorrelacao(e) {
    this.correlacaoAnualGrid.instance.deleteRow(e.rowIndex)
  }

  //------------Projeto Atividade------------------
  public onToolbarPreparingProjetoAtividade(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo Projeto Atividade'
          item.options.hint = 'Novo Projeto Atividade'
          item.options.onClick = () => this.onInitNewRowProjetoAtividade()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRowProjetoAtividade(): void {
    const lista = []
    this.projetoAtividadeData.items().forEach(item => {
      lista.push(item)
    })

    const dialogRef = this.dialogService.open(ProjetoAtividadeComponent, {
      context: {
        ano: this.formulario.get('ano').value,
        podeGravar: this.podeGravar,
        podeEditar: this.podeEditar,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista.push(retorno)
        this.projetoAtividadeData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarProjetoAtividade(e) {
    const lista = []
    this.projetoAtividadeData.items().forEach(item => {
      lista.push(item)
    })

    const projeto: any = {
      id: e.data.id,
      uuid: e.data.uuid,
      projetoAtividadeUuid: e.data.projetoAtividadeUuid,
      projetoAtividadeNome: e.data.projetoAtividadeNome,
      dataInclusaoTce: e.data.dataInclusaoTce,
      dataCancelamentoTce: e.data.dataCancelamentoTce,
      notaExplicativa: e.data.notaExplicativa,
      leiUuid: e.data.leiUuid,
    }

    let ultimo
    if (e.rowIndex - 1 >= 0) {
      ultimo = lista[e.rowIndex - 1]
    }

    const dialogRef = this.dialogService.open(ProjetoAtividadeComponent, {
      context: {
        dados: projeto,
        ano: this.formulario.get('ano').value,
        podeGravar: this.podeGravar,
        podeEditar: this.podeEditar,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista[e.rowIndex] = retorno
        this.projetoAtividadeData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerProjetoAtividade(e) {
    this.projetoAtividadeGrid.instance.deleteRow(e.rowIndex)
  }
}
