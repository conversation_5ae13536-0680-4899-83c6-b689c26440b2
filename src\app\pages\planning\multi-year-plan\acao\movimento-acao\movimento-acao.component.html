<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'" class="max-height">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row">
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [readonly]="true"
          name="Número"
          label="Número"
          placeholder="Número"
          formControlName="numero"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo de movimento"
          label="Tipo de movimento"
          placeholder="Tipo de movimento"
          [dataSource]="tipoMovimentoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="tipoMovimentoUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data do movimento"
          label="Data do movimento"
          placeholder="Data do movimento"
          formControlName="dataMovimento"
          [minDate]="menorData"
          required="true"
          [disabled]="disabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data inicial"
          label="Data inicial"
          placeholder="Data inicial"
          formControlName="dataInicial"
          required="true"
          [disabled]="disabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data final"
          label="Data final"
          placeholder="Data final"
          formControlName="dataFinal"
          [disabled]="disabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nome"
          label="Nome"
          placeholder="Nome"
          formControlName="nomeAcao"
          required="true"
          [disabled]="disabled"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Natureza da ação"
          label="Natureza da ação"
          placeholder="Natureza da ação"
          [dataSource]="naturezaAcaoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="naturezaAcaoUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo de execução da ação"
          label="Tipo de execução da ação"
          placeholder="Tipo de execução da ação"
          [dataSource]="tipoExecucaoAcaoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="tipoExecucaoAcaoUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo da ação"
          label="Tipo da ação"
          placeholder="Tipo da ação"
          [dataSource]="tipoAcaoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="tipoAcaoUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Função"
          label="Função"
          placeholder="Função"
          [dataSource]="funcaoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="funcaoUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Subfunção"
          label="Subfunção"
          placeholder="Subfunção"
          [dataSource]="subfuncaoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="subfuncaoUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Unidade de medida"
          label="Unidade de medida"
          placeholder="Unidade de medida"
          [dataSource]="unidadeMedidaData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="unidadeMedidaUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Produto da ação"
          label="Produto da ação"
          placeholder="Produto da ação"
          [dataSource]="produtoacaoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="produtoacaoUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4 margin-top">
        <eqp-nebular-checkbox
          label="Ação continua"
          name="Ação continua"
          formControlName="flagAcaoContinua"
          [disabled]="disabled"
        ></eqp-nebular-checkbox>
      </div>
      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Orgão executor"
          label="Orgão executor"
          placeholder="Orgão executor"
          [dataSource]="entidadeExecutorOrgaoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="entidadeExecutorOrgaoUuid"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Entidade executora"
          label="Entidade executora"
          placeholder="Entidade executora"
          [dataSource]="entidadeExecutorData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="entidadeExecutorUuid"
          required="true"
          [disabled]="disabled"
        ></eqp-nebular-select>
      </div>

      <div class="col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nota explicativa"
          label="Nota explicativa"
          placeholder="Nota explicativa"
          formControlName="notaExplicativa"
          [required]="notaObrigatoria"
          [disabled]="disabled"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Descrição complementar"
          label="Descrição complementar"
          placeholder="Descrição complementar"
          formControlName="descricaoComplementar"
          [disabled]="disabled"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="podeGravar"
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
