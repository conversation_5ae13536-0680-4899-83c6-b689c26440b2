import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { distinctUntilChanged, first } from 'rxjs/operators'
import { AcaoService } from './../acao.service'

@Component({
  selector: 'eqp-movimento-acao',
  templateUrl: './movimento-acao.component.html',
  styleUrls: ['./movimento-acao.component.scss'],
})
export class MovimentoAcaoComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Movimento da Ação'
  public formulario: FormGroup
  public tipoMovimentoData: any
  public naturezaAcaoData: any
  public tipoExecucaoAcaoData: any
  public tipoAcaoData: any
  public funcaoData: any
  public subfuncaoData: any
  public unidadeMedidaData: DataSource
  public produtoacaoData: any
  public entidadeExecutorOrgaoData: any
  public entidadeExecutorData: any
  public notaObrigatoria: boolean = false
  public disabled: boolean = false

  @Input() public numeroAtual: number
  @Input() public dados: any
  @Input() public menorData: any
  @Input() public versao: any
  @Input() public podeGravar: boolean = false

  constructor(
    private formBuilder: FormBuilder,
    private service: AcaoService,
    public ref: NbDialogRef<MovimentoAcaoComponent>,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.changeTipoMovimento()
    this.tipoMovimentoData = new DataSource({
      store: [],
      paginate: true,
      pageSize: 10,
    })
    this.formulario.patchValue(this.dados)

    if (this.dados.id) {
      this.service
        .verificaSeMovimentoFoiCriadoEmVersaoAnterior(this.dados.id)
        .pipe(first())
        .subscribe(data => {
          this.disabled =
            (this.dados.id != null &&
              this.versao.situacaoVersaoId.nome === 'Aprovado') ||
            data.dados
        })
    }
    this.loadSelects()
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      id: [null],
      ppaAcaoUuid: [null],
      ppaAcaoNome: [null],
      numero: [this.numeroAtual + 1],
      tipoMovimentoUuid: [null, Validators.required],
      tipoMovimentoNome: [null],
      dataMovimento: [null, Validators.required],
      nomeAcao: [null, Validators.required],
      dataInicial: [null, Validators.required],
      dataFinal: [null],
      naturezaAcaoUuid: [null, Validators.required],
      naturezaAcaoNome: [null],
      tipoExecucaoAcaoUuid: [null, Validators.required],
      tipoExecucaoAcaoNome: [null],
      tipoAcaoUuid: [null, Validators.required],
      tipoAcaoNome: [null],
      funcaoUuid: [null, Validators.required],
      funcaoNome: [null],
      subfuncaoUuid: [null, Validators.required],
      subfuncaoNome: [null],
      unidadeMedidaUuid: [null, Validators.required],
      unidadeMedidaNome: [null],
      produtoacaoUuid: [null, Validators.required],
      produtoacaoNome: [null],
      flagAcaoContinua: [false],
      entidadeExecutorUuid: [null, Validators.required],
      entidadeExecutorNome: [null],
      entidadeExecutorOrgaoUuid: [null],
      entidadeExecutorOrgaoNome: [null],
      notaExplicativa: [null],
      descricaoComplementar: [null],
      unidadeMedidaTceUuid: [null],
    })
  }

  private changeTipoMovimento(): void {
    this.formulario
      .get('tipoMovimentoUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.notaObrigatoria = false
        const nota = this.formulario.get('notaExplicativa').value
        this.formulario.get('notaExplicativa').setValidators([])
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === value) {
            if (item.tipoMovimentoTceId.codigo !== 1) {
              this.formulario
                .get('notaExplicativa')
                .setValidators([Validators.required])
              this.notaObrigatoria = true
            }
          }
        })
        this.formulario.get('notaExplicativa').patchValue('')
        this.formulario.get('notaExplicativa').patchValue(nota)
      })
  }

  private loadSelects(): void {
    this.tipoMovimentoData = new DataSource({
      store:
        this.versao.situacaoVersaoId.nome === 'Aprovado'
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_acao/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '3',
            )
          : this.numeroAtual === 0 || (this.dados && this.dados.numero === 1)
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_acao/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '1',
            )
          : this.service.getDataSourceFiltro(
              'uuid',
              'ppa_acao/tipo_movimento',
              10,
            ),
      paginate: true,
      pageSize: 10,
    })
    this.naturezaAcaoData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_acao/natureza_acao',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.tipoExecucaoAcaoData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_acao/tipo_execucao_acao',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.tipoAcaoData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'ppa_acao/tipo_acao', 10),
      paginate: true,
      pageSize: 10,
    })
    this.funcaoData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'ppa_acao/funcao', 10),
      paginate: true,
      pageSize: 10,
      map: data => {
        data.nome = data.codigo + ' - ' + data.nome
        return data
      },
    })
    this.subfuncaoData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'ppa_acao/subfuncao', 10),
      paginate: true,
      pageSize: 10,
      map: data => {
        data.nome = data.codigo + ' - ' + data.nome
        return data
      },
    })
    this.unidadeMedidaData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_acao/unidade_medida',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.produtoacaoData = new DataSource({
      store: this.formulario.get('unidadeMedidaTceUuid').value
        ? this.service.getDataSourceFiltro(
            'uuid',
            'ppa_acao/produto_acao',
            10,
            'unidadeMedidaTceUuid',
            this.formulario.get('unidadeMedidaTceUuid').value,
          )
        : [],
      paginate: true,
      pageSize: 10,
    })
    this.entidadeExecutorOrgaoData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'ppa_acao/orgao', 10),
      paginate: true,
      pageSize: 10,
    })
    this.entidadeExecutorData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'ppa_acao/entidade', 10),
      paginate: true,
      pageSize: 10,
    })
    this.changeUnidadeMedida()
  }

  public cancelar(): void {
    this.ref.close(null)
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      let movimento = this.formulario.getRawValue()
      if (this.tipoMovimentoData.items().length > 0) {
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === movimento.tipoMovimentoUuid) {
            movimento.tipoMovimentoNome = item.nome
          }
        })
      }

      if (this.naturezaAcaoData.items().length > 0) {
        this.naturezaAcaoData.items().forEach(item => {
          if (item.uuid === movimento.naturezaAcaoUuid) {
            movimento.naturezaAcaoNome = item.nome
          }
        })
      }

      if (this.tipoExecucaoAcaoData.items().length > 0) {
        this.tipoExecucaoAcaoData.items().forEach(item => {
          if (item.uuid === movimento.tipoExecucaoAcaoUuid) {
            movimento.tipoExecucaoAcaoNome = item.nome
          }
        })
      }

      if (this.tipoAcaoData.items().length > 0) {
        this.tipoAcaoData.items().forEach(item => {
          if (item.uuid === movimento.tipoAcaoUuid) {
            movimento.tipoAcaoNome = item.nome
          }
        })
      }

      if (this.funcaoData.items().length > 0) {
        this.funcaoData.items().forEach(item => {
          if (item.uuid === movimento.funcaoUuid) {
            movimento.funcaoNome = item.nome
          }
        })
      }

      if (this.subfuncaoData.items().length > 0) {
        this.subfuncaoData.items().forEach(item => {
          if (item.uuid === movimento.subfuncaoUuid) {
            movimento.subfuncaoNome = item.nome
          }
        })
      }

      if (this.unidadeMedidaData.items().length > 0) {
        this.unidadeMedidaData.items().forEach(item => {
          if (item.uuid === movimento.unidadeMedidaUuid) {
            movimento.unidadeMedidaNome = item.nome
          }
        })
      }

      if (this.produtoacaoData.items().length > 0) {
        this.produtoacaoData.items().forEach(item => {
          if (item.uuid === movimento.produtoacaoUuid) {
            movimento.produtoacaoNome = item.nome
          }
        })
      }

      if (this.entidadeExecutorOrgaoData.items().length > 0) {
        this.entidadeExecutorOrgaoData.items().forEach(item => {
          if (item.uuid === movimento.entidadeExecutorOrgaoUuid) {
            movimento.entidadeExecutorOrgaoNome = item.nome
          }
        })
      }

      if (this.entidadeExecutorData.items().length > 0) {
        this.entidadeExecutorData.items().forEach(item => {
          if (item.uuid === movimento.entidadeExecutorUuid) {
            movimento.entidadeExecutorNome = item.nome
          }
        })
      }

      this.ref.close(movimento)
    }
  }

  private changeUnidadeMedida(): void {
    this.formulario
      .get('unidadeMedidaUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.formulario.get('unidadeMedidaTceUuid').patchValue('')

        if (value) {
          this.service
            .getUnidadeMedidaIndividual(value)
            .pipe(first())
            .subscribe(data => {
              this.formulario
                .get('unidadeMedidaTceUuid')
                .patchValue(data.dados.unidadeMedidaTceUuid)
              this.produtoacaoData = new DataSource({
                store: this.formulario.get('unidadeMedidaTceUuid').value
                  ? this.service.getDataSourceFiltro(
                      'uuid',
                      'ppa_acao/produto_acao',
                      10,
                      'unidadeMedidaTceUuid',
                      this.formulario.get('unidadeMedidaTceUuid').value,
                    )
                  : [],
                paginate: true,
                pageSize: 10,
              })
            })
        } else {
          this.produtoacaoData = new DataSource({
            store: [],
            paginate: true,
            pageSize: 10,
          })
        }
      })
  }
}
