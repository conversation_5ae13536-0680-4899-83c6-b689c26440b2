<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row">
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [readonly]="true"
          name="Número"
          label="Número"
          placeholder="Número"
          formControlName="numero"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo de movimento"
          label="Tipo de movimento"
          placeholder="Tipo de movimento"
          [dataSource]="tipoMovimentoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="tipoMovimentoUuid"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data do movimento"
          label="Data do movimento"
          placeholder="Data do movimento"
          formControlName="dataMovimento"
          [minDate]="menorData"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Meta fisica prevista"
          label="Meta fisica prevista"
          placeholder="Meta fisica prevista"
          formControlName="vlrMetaFisicaPrevista"
          [options]="{
            prefix: '',
            decimal: ',',
            precision: 2,
            thousands: '.',
            align: 'left',
            allowNegative: false
          }"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Recurso previsto livre"
          label="Recurso previsto livre"
          placeholder="Recurso previsto livre"
          formControlName="vlrRecursoPrevistoLivre"
          [options]="{
            prefix: '',
            decimal: ',',
            precision: 2,
            thousands: '.',
            align: 'left',
            allowNegative: false
          }"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Recurso previsto vinculado"
          label="Recurso previsto vinculado"
          placeholder="Recurso previsto vinculado"
          formControlName="vlrRecursoPrevistoVinculado"
          [options]="{
            prefix: '',
            decimal: ',',
            precision: 2,
            thousands: '.',
            align: 'left',
            allowNegative: false
          }"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Recurso previsto total"
          label="Recurso previsto total"
          placeholder="Recurso previsto total"
          formControlName="vlrRecursoPrevistoTotal"
          [options]="{
            prefix: '',
            decimal: ',',
            precision: 2,
            thousands: '.',
            align: 'left',
            allowNegative: false
          }"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nota explicativa"
          label="Nota explicativa"
          placeholder="Nota explicativa"
          formControlName="notaExplicativa"
          [required]="notaObrigatoria"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="podeGravar || (podeEditar && formulario.get('uuid').value)"
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
