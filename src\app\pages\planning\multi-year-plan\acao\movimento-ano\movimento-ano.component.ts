import { Component, Input, OnDestroy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { distinctUntilChanged, map, startWith, takeUntil } from 'rxjs/operators'
import { ToastrService } from './../../../../../@common/services/toastr/toastr.service'
import { AcaoService } from './../acao.service'
import { combineLatest, Subject } from 'rxjs'

@Component({
  selector: 'eqp-movimento-ano',
  templateUrl: './movimento-ano.component.html',
  styleUrls: ['./movimento-ano.component.scss'],
})
export class MovimentoAnoComponent implements OnInit, OnDestroy {
  public loading: boolean = false
  public pageTitle: string = 'Movimento'
  public formulario: FormGroup
  public notaObrigatoria: boolean = false

  @Input() public numeroAtual: number
  @Input() public dados: any
  @Input() public menorData: any
  @Input() public versao: any
  @Input() public podeGravar: boolean = false
  @Input() public podeEditar: boolean = false

  public tipoMovimentoData: any
  private unsub$ = new Subject

  constructor(
    private formBuilder: FormBuilder,
    private service: AcaoService,
    public ref: NbDialogRef<MovimentoAnoComponent>,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.tipoMovimentoData = new DataSource({
      store: [],
      paginate: true,
      pageSize: 10,
    })
    this.changeTipoMovimento()
    
    this.loadSelects()
    this.sugerirRecursoPrevistoTotal()
    this.formulario.patchValue(this.dados)
  }

  ngOnDestroy(): void {
    this.unsub$.next()
    this.unsub$.complete()
  }

  public cancelar(): void {
    this.ref.close(null)
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      let movimento = this.formulario.getRawValue()
      movimento.vlrRecursoPrevistoLivre = +movimento.vlrRecursoPrevistoLivre
      movimento.vlrRecursoPrevistoVinculado = +movimento.vlrRecursoPrevistoVinculado
      movimento.vlrRecursoPrevistoTotal = +movimento.vlrRecursoPrevistoTotal

      if (this.tipoMovimentoData.items().length > 0) {
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === movimento.tipoMovimentoUuid) {
            movimento.tipoMovimentoNome = item.nome
          }
        })
      }

      this.ref.close(movimento)
    }
  }

  private sugerirRecursoPrevistoTotal() {
    const {vlrRecursoPrevistoLivre, vlrRecursoPrevistoVinculado} = this.formulario.getRawValue()

    const vlrRecursoPrevistoLivre$ = this.formulario.get(
      'vlrRecursoPrevistoLivre',
    ).valueChanges.pipe(startWith(vlrRecursoPrevistoLivre))
    const vlrRecursoPrevistoVinculado$ = this.formulario.get(
      'vlrRecursoPrevistoVinculado',
    ).valueChanges.pipe(startWith(vlrRecursoPrevistoVinculado))

    combineLatest([vlrRecursoPrevistoLivre$, vlrRecursoPrevistoVinculado$])
      .pipe(
        map(
          ([vlrRecursoPrevistoLivre, vlrRecursoPrevistoVinculado]: number[]) =>
            +(vlrRecursoPrevistoLivre) + (+vlrRecursoPrevistoVinculado),
        ),
        takeUntil(this.unsub$),
      )
      .subscribe((vlrTotal: number) => {
        this.formulario.get('vlrRecursoPrevistoTotal').patchValue(+vlrTotal.toFixed(2))
      })
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      id: [null],
      uuid: [null],
      numero: [this.numeroAtual + 1],
      tipoMovimentoUuid: [null, Validators.required],
      tipoMovimentoNome: [null],
      dataMovimento: [null],
      vlrMetaFisicaPrevista: [0, Validators.required],
      vlrRecursoPrevistoLivre: [0, Validators.required],
      vlrRecursoPrevistoVinculado: [0, Validators.required],
      vlrRecursoPrevistoTotal: [0, Validators.required],
      notaExplicativa: [null],
    })
  }

  private loadSelects(): void {
    this.tipoMovimentoData = new DataSource({
      store:
        this.versao.situacaoVersaoId.nome === 'Aprovado'
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_acao/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '3',
            )
          : this.numeroAtual === 0 || (this.dados && this.dados.numero === 1)
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_acao/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '1',
            )
          : this.service.getDataSourceFiltro(
              'uuid',
              'ppa_acao/tipo_movimento',
              10,
            ),
      paginate: true,
      pageSize: 10,
    })
  }

  private changeTipoMovimento(): void {
    this.formulario
      .get('tipoMovimentoUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.notaObrigatoria = false
        const nota = this.formulario.get('notaExplicativa').value
        this.formulario.get('notaExplicativa').setValidators([])
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === value) {
            if (item.tipoMovimentoTceId.codigo !== 1) {
              this.formulario
                .get('notaExplicativa')
                .setValidators([Validators.required])
              this.notaObrigatoria = true
            }
          }
        })
        this.formulario.get('notaExplicativa').patchValue('')
        this.formulario.get('notaExplicativa').patchValue(nota)
      })
  }
}
