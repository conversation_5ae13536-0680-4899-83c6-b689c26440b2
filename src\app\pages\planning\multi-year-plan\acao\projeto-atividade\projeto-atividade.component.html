<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row">
      <div class="col-md-12 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Projeto Atividade"
          label="Projeto Atividade"
          placeholder="Projeto Atividade"
          [dataSource]="projetoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="projetoAtividadeUuid"
          required="true"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Inclusão no TCE"
          label="Inclusão no TCE"
          placeholder="Inclusão no TCE"
          formControlName="dataInclusaoTce"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data do cancelamento"
          label="Data do cancelamento"
          placeholder="Data do cancelamento"
          formControlName="dataCancelamentoTce"
          [minDate]="formulario.get('dataInclusaoTce').value"
          [required]="obrigatorio"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Lei"
          label="Lei"
          placeholder="Lei"
          [dataSource]="leiData"
          valueExpr="uuid"
          displayExpr="descricaoTipoDocumento"
          formControlName="leiUuid"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-12 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nota explicativa"
          label="Nota explicativa"
          placeholder="Nota explicativa"
          formControlName="notaExplicativa"
          maxlength="250"
          [required]="obrigatorio"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="podeGravar || (podeEditar && formulario.get('uuid').value)"
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
