import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { distinctUntilChanged, first } from 'rxjs/operators'
import { AcaoService } from '../acao.service'

@Component({
  selector: 'eqp-projeto-atividade',
  templateUrl: './projeto-atividade.component.html',
  styleUrls: ['./projeto-atividade.component.scss'],
})
export class ProjetoAtividadeComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Projeto Atividade'
  public formulario: FormGroup
  public projetoData: any
  public leiData: any

  @Input() public dados: any
  @Input() public ano: number
  @Input() public podeGravar: boolean = false
  @Input() public podeEditar: boolean = false

  public obrigatorio = false

  constructor(
    public ref: NbDialogRef<ProjetoAtividadeComponent>,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private service: AcaoService,
  ) {}

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.change()
    this.loadSelects()
    this.formulario.patchValue(this.dados)
  }

  private loadSelects(): void {
    this.projetoData = new DataSource({
      store: this.service.getDataSourceFiltroComposto(
        'uuid',
        `ppa_acao/projeto_atividade/ano/${this.ano}`,
        10,
      ),
      paginate: true,
      pageSize: 10,
      map: data => {
        data.nome =
          data.exercicio.entidade.codigo +
          ' - ' +
          data.exercicio.entidade.nome +
          ' - ' +
          data.tipoOrdemNome
        return data
      },
    })
    this.leiData = new DataSource({
      store: this.service.getDataSourceFiltroComposto(
        'uuid',
        'ppa_acao/leis',
        10,
        '["escopoCodigo","=",1],"or",["escopoCodigo","=",2]',
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      uuid: [null],
      id: [null],
      projetoAtividadeUuid: [null, Validators.required],
      projetoAtividadeNome: [null],
      dataInclusaoTce: [null, Validators.required],
      dataCancelamentoTce: [null],
      leiUuid: [null],
      notaExplicativa: [null],
    })
  }

  public cancelar(): void {
    this.ref.close(null)
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      let projeto = this.formulario.getRawValue()

      this.service
        .getProjetoAtividadeIndividual(projeto.projetoAtividadeUuid)
        .pipe(first())
        .subscribe(data => {
          projeto.projetoAtividadeNome = data.dados.nome =
            data.dados.exercicio.entidade.codigo +
            ' - ' +
            data.dados.exercicio.entidade.nome +
            ' - ' +
            data.dados.tipoOrdemNome
          this.ref.close(projeto)
        })
    }
  }

  private change(): void {
    this.formulario
      .get('dataCancelamentoTce')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.validarCancelamento()
      })
    this.formulario
      .get('notaExplicativa')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.validarCancelamento()
      })
    this.formulario
      .get('leiUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.validarCancelamento()
      })
  }

  private validarCancelamento(): void {
    const dataCancelamento = this.formulario.get('dataCancelamentoTce').value
    const notaExplicativa = this.formulario.get('notaExplicativa').value
    const leiUuid = this.formulario.get('leiUuid').value
    if (dataCancelamento || notaExplicativa || leiUuid) {
      this.obrigatorio = true
      this.formulario
        .get('dataCancelamentoTce')
        .setValidators([Validators.required])
      this.formulario
        .get('notaExplicativa')
        .setValidators([Validators.required])
    } else {
      this.obrigatorio = false
      this.formulario.get('dataCancelamentoTce').setValidators([])
      this.formulario.get('notaExplicativa').setValidators([])
    }
    this.formulario.get('dataCancelamentoTce').patchValue(dataCancelamento)
    this.formulario.get('notaExplicativa').patchValue(notaExplicativa)
  }
}
