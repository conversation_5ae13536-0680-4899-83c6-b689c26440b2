<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="form">
    <div class="row">
      <div [class]="'col-md-5' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          (blur)="padCode()"
          name="codigo"
          label="Código"
          placeholder="Código"
          formControlName="codigo"
        >
        </eqp-nebular-input>
      </div>

      <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nome"
          label="Nome"
          placeholder="Nome"
          formControlName="nome"
        >
        </eqp-nebular-input>
      </div>

      <div [class]="'col-md-3' + ' col-sm-12 mb-4'">
        <eqp-nebular-input
          [size]="'small'"
          [style]="'date'"
          [shape]="'rectangle'"
          name="Inclusão TCE"
          label="Inclusão TCE"
          placeholder="Data inclusão"
          formControlName="dataInclusaoTce"
        ></eqp-nebular-input>
      </div>
    </div>
    <div class="row">
      <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
        <eqp-nebular-checkbox
          class="mr-2"
          name="Analítica"
          label="Analítica"
          formControlName="analitica"
        >
        </eqp-nebular-checkbox>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="back()">
          Voltar
        </button>
        <button
          *ngIf="form.get('uuid').value && nivelPermissao === 'FULL'"
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover(form.get('uuid').value)"
        >
          Apagar
        </button>
        <button
          *ngIf="
            (form.get('uuid').value && nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="submit(form)"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
