<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <nb-tabset (changeTab)="onChangeTab($event)">
      <nb-tab tabTitle="PLano de receita" class="pb-0">
        <div class="row">
          <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              (blur)="padCode()"
              name="codigo"
              label="Código"
              placeholder="Código"
              formControlName="codigo"
            >
            </eqp-nebular-input>
          </div>

          <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              name="Nome"
              label="Nome"
              placeholder="Nome"
              formControlName="nome"
            >
            </eqp-nebular-input>
          </div>

          <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [size]="'small'"
              [style]="'date'"
              [shape]="'rectangle'"
              name="Inclusão TCE"
              label="Inclusão TCE"
              placeholder="Data inclusão"
              formControlName="dataInclusaoTce"
            ></eqp-nebular-input>
          </div>
        </div>

        <div class="row">
          <div [class]="'col-md-4' + ' col-sm-12 mb-4'">
            <eqp-nebular-select
            [size]="'small'"
            [shape]="'rectangle'"
            name="tp diario arrecadação"
            label="Tipo diário arrecadação"
            placeholder="Tipo diário arrecadação"
            [dataSource]="collectionType"
            valueExpr="uuid"
            displayExpr="nome"
            formControlName="tipoDiarioArrecadacaoUuid"
            ></eqp-nebular-select>
          </div>
          <div [class]="'col-md-2' + ' col-sm-12 mb-4'" class="position">
            <eqp-nebular-checkbox
              name="Analítica"
              label="Analítica"
              formControlName="analitica"
            >
            </eqp-nebular-checkbox>
          </div>
        </div>
        <div class="d-flex justify-content-between mt-3">
          <button type="button" class="btn btn-dark" (click)="back()">
            Voltar
          </button>
          <button
            *ngIf="nivelPermissao === 'EDITOR' || nivelPermissao === 'FULL'"
            type="button"
            [disabled]="!model.valid"
            class="btn btn-success float-md-right"
            (click)="submit(model)"
          >
            Confirmar
          </button>
        </div>
      </nb-tab>
      <nb-tab
        tabId="resource-source"
        [disabled]="!uuid || !analitica"
        tabTitle="Fontes de recurso"
        class="pb-0"
      >
        <div *ngIf="uuid && tabWasTouched('resource-source')">
          <div class="d-flex">
            <eqp-nebular-toggle
              formControlName="flagTemRateio"
              label="Tem rateio"
              title="Tem rateio"
              [disabled]="!apportionmentFlagToggleEnable"
              (click)="apportionmentFlagToggleChange()"
            ></eqp-nebular-toggle>
          </div>
          <eqp-revenue-plan-source-list
            [model]="model"
            [enablePercentage]="flagTemRateio"
            [parentUuid]="uuid"
          ></eqp-revenue-plan-source-list>
        </div>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
  <nb-card-footer>
  </nb-card-footer>
</nb-card>
