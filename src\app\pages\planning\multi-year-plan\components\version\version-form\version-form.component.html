<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="form">
    <div class="row">
      <div [class]="(modal ? 'col-md-12' : 'col-md-3') + ' col-sm-12 mb-4'">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          readonly="true"
          name="Versão"
          label="Versão"
          placeholder="Versão"
          formControlName="numeroVersao"
        >
        </eqp-nebular-input>
      </div>

      <div [class]="(modal ? 'col-md-12' : 'col-md-6') + ' col-sm-12 mb-4'">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Escopo"
          label="Escopo"
          placeholder="Escopo"
          [dataSource]="escopeData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="ppaEscopoDocumentoUuid"
          required="true"
          [disabled]="bloquearCampos || form.get('uuid').value"
        ></eqp-nebular-select>
      </div>

      <div [class]="(modal ? 'col-md-12' : 'col-md-3') + ' col-sm-12 mb-4'">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Situação"
          label="Situação"
          placeholder="Situação"
          [dataSource]="situationData"
          valueExpr="id"
          displayExpr="nome"
          formControlName="situacaoVersaoId"
          required="true"
          [disabled]="bloquearCampos"  
        ></eqp-nebular-select>
      </div>

      <div
        [class]="(modal ? 'col-md-12' : 'col-md-9') + ' col-sm-12 mb-4'">
        <eqp-nebular-search-field
          label="Lei"
          nameLabel="Lei"
          [disabled]="bloquearCampos"
          (onButtonClick)="onDecreeSearchDialog()"
          (onInputChange)="onDecreeSearchInput($event)"
          formControlName="leiPpaUuid"
          [required]="situacaoVersaoId === 2"
        >
        </eqp-nebular-search-field>
      </div>
      <div
        [class]="(modal ? 'col-md-12' : 'col-md-3') + ' col-sm-12 mb-4'">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Protocolo"
          [disabled]="bloquearCampos"
          label="Protocolo"
          placeholder="Protocolo"
          formControlName="protocolo"
        >
        </eqp-nebular-input>
      </div>
    </div>
    <div class="d-flex flex-row" style="gap: 0.5rem">
      <button class="btn btn-success" [disabled]="bloquearCampos" (click)="abrirEditorTexto()">Objetivos Macros</button>
      <ng-container *ngIf="exibirBotaoCopiar">
        <button *ngIf="ppaVersionUuid; else toggleCopiar" class="btn btn-primary" [disabled]="bloquearCampos" (click)="copiar()">Copiar dados da versão anterior</button>
        <ng-template #toggleCopiar>
          <eqp-nebular-checkbox
            class="mt-2"
            label="Copiar dados da versão anterior"
            name="Copiar dados da versão anterior"
            [disabled]="bloquearCampos"
            formControlName="copiar"
          ></eqp-nebular-checkbox>
        </ng-template>
      </ng-container>
      <ng-container *ngIf="habilitarControleLeiAto">
        <eqp-nebular-checkbox
          class="mt-2"
          label="Controle Lei/Ato PPA"
          name="Controle Lei/Ato PPA"
          [disabled]="bloquearCampos"
          formControlName="flagControleLeiAto"
        ></eqp-nebular-checkbox>
      </ng-container>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="back()">
          Voltar
        </button>
        <button
          *ngIf="
            (form.get('uuid').value && nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          [disabled]="form.invalid || bloquearFormulario"
          (click)="submit()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
