import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'
import { DecreeSearchComponent } from '@common/dialogs/decree-search/decree-search.component'
import { EditorDocComponent } from '@common/dialogs/editor-doc/editor-doc.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { UserDataService } from '@guards/services/user-data.service'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { DecreeInterface } from '@pages/planning/multi-year-plan/interfaces/decree'
import { VersionsInterface } from '@pages/planning/multi-year-plan/interfaces/versions'
import { MultiYearPlanService } from '@pages/planning/multi-year-plan/services/multi-year-plan.service'
import DataSource from 'devextreme/data/data_source'
import { from, Subject, Subscription } from 'rxjs'
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  first,
  switchMap,
  take,
  takeUntil
} from 'rxjs/operators'

@Component({
  selector: 'eqp-version-form',
  templateUrl: './version-form.component.html',
  styleUrls: ['./version-form.component.scss'],
})
export class VersionFormComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Versão plano plurianual'
  public form: FormGroup

  @Input() public ppaUuid: string
  @Input() public ppaVersionUuid: string

  public situationData: {
    id: number,
    uuid: string,
    nome: string,
    versao: number
  }[] = []
  public escopeData: any
  public leisPpa: any
  public leisPms: any
  public leisEca: any
  public leisVisible: Boolean = false
  public bloquearCampos: Boolean = false
  public bloquearPMS: Boolean = false
  public bloquearECA: Boolean = false
  public modal: Boolean = false
  private escopo: number = 0
  private escopoDocumentoTceUuid = ''
  public bloquearFormulario: boolean
  public exibirBotaoCopiar: boolean
  public exercicioLogado: number
  
  public subscription: Subscription
  private destroy$ = new Subject()

  constructor(
    private formBuilder: FormBuilder,
    private service: MultiYearPlanService,
    public router: Router,
    private toastr: ToastrService,
    public menuService: MenuService,
    public ref: NbDialogRef<VersionFormComponent>,
    private dialogService: NbDialogService,
    private crudService: CrudService,
    private userDataService: UserDataService,
  ) {
    super(menuService, router)
    this.permissao('/plano-plurianual')
  }

  public ngOnInit(): void {
    this.form = this.formBuilder.group({
      uuid: [null],
      leiPmsUuid: [null],
      leiPpaUuid: [null],
      leiUuidEcaUuid: [null],
      numeroVersao: [null],
      ppaEscopoDocumentoUuid: [null, Validators.required],
      ppaUuid: [null],
      situacaoVersaoUuid: [null],
      situacaoVersaoId: [null, Validators.required],
      protocolo: [],
      objetivoMacro: [],
      copiar: [true]
    })
    
    this.exercicioLogado = parseInt(this.userDataService.userData.exercicio)
    this.loadScreen()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
    this.destroy$.next()
    this.destroy$.complete()
  }

  get situacaoVersaoId() {
    return this.form.get('situacaoVersaoId')?.value
  }

  get uuid() {
    return this.form.get('uuid')?.value
  }

  private validarVersaoPPA() {
    this.form.get('ppaEscopoDocumentoUuid')
      .valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(100),
        switchMap(value => this.service.verificarSePrimeiraVersaoPPA(this.ppaUuid, value)),
        takeUntil(this.destroy$)
      )
      .subscribe(res => {
        this.exibirBotaoCopiar = res.dados
        if(!res.dados) {
          this.form.get('copiar').patchValue(null)
        } else {
          this.form.get('copiar').patchValue(true)
        }
      })
  }

  private async loadSelects() {
    this.situacaoChange()
    this.escopoChange()

    this.situationData = await new DataSource({
      store: this.service.getDataSourceFiltro('id', 'ppa/situacao_versao', 0),
      paginate: true,
      pageSize: 10,
    }).load()
    
    if(!this.ppaVersionUuid) {
      const situacaoPropostaLeiAtoId = 1
      const situacaoPropostaLeiAto = this.situationData.find(
        item => item.id === situacaoPropostaLeiAtoId,
      )
      this.form.get('situacaoVersaoId').patchValue(situacaoPropostaLeiAto?.id)
    }

    this.escopeData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa/ppa_escopo_documento',
        10,
        //TODO: remover após validação de QA
        // 'flagVersao',
        // 'S',
      ),
      paginate: true,
      pageSize: 10,
    })

    this.escopeData.load().then(res => {
      const ecopeData = res.find(
        item => item.uuid === this.form.get('ppaEscopoDocumentoUuid').value,
      )
      this.escopoDocumentoTceUuid = ecopeData?.escopoDocumentoTceUuid
    })

    this.leisPms = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa/leis',
        10,
        'escopoCodigo',
        '4',
      ),
      paginate: true,
      pageSize: 10,
    })
    this.leisEca = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa/leis',
        10,
        'escopoCodigo',
        '5',
      ),
      paginate: true,
      pageSize: 10,
    })

    this.leisPpa = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa/leis',
        10,
        'escopo',
        this.escopoDocumentoTceUuid,
      ),
      paginate: true,
      pageSize: 10,
      map: item => {
        return {
          ...item,
          leiExercicio: `${item.tipoDocumento?.nome} - ${item.numero} / ${item.ano}`,
        }
      },
    })
  }

  private loadScreen(): void {
    if (this.ppaVersionUuid) this.searchById(this.ppaVersionUuid)
    else {
      this.loadSelects()
      this.validarVersaoPPA()
    }
  }

  private searchById(uuid: string): void {
    this.loading = true
    this.service
      .getVersionIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe((res: any) => {
        this.exibirBotaoCopiar = res.dados.numeroVersao === 1
        this.bloquearFormulario = res.dados.situacaoVersaoId.id === 2
        this.leisVisible =
          res.dados.situacaoVersaoId.nome === 'Aprovado' &&
          res.dados.ppaEscopoDocumentoId.id === 1
        this.bloquearCampos = res.dados.situacaoVersaoId.nome === 'Aprovado'
        this.bloquearPMS = res.dados.leiUuidPms
        this.bloquearECA = res.dados.leiUuidEca
        this.escopo = res.dados.ppaEscopoDocumentoId.id
        this.form.patchValue({
          ...res.dados,
          leiPpaUuid: res.dados.leiUuidPpa,
          leiPmsUuid: res.dados.leiUuidPms,
          leiUuidEcaUuid: res.dados.leiUuidEca,
          ppaEscopoDocumentoUuid: res.dados.ppaEscopoDocumentoId.uuid,
          situacaoVersaoUuid: res.dados.situacaoVersaoId.uuid,
          situacaoVersaoId: res.dados.situacaoVersaoId.id,
          ppaUuid: this.ppaUuid,
        })
        if (res.dados.leiPpa) this.setDecreeFields(res.dados.leiPpa)
        this.loadSelects()
      })
  }

  public back() {
    this.ref.close('N')
  }

  public create(dto: VersionsInterface) {
    return this.service.postVersion(dto)
  }

  private validarCadastroVersao(dto: any) {
    const { ppaEscopoDocumentoUuid } = this.form.getRawValue()

    this.loading = true

    this.crudService
      .getSingleObject<boolean>(
        'ppa/validar_existe_versao_com_exercicio_posterior',
        {
          ppaUuid: this.ppaUuid,
          ppaDocumentScopeUuid: ppaEscopoDocumentoUuid,
        },
      )
      .pipe(
        switchMap(validacao => {
          if (validacao.dados) {
            return from(this.modalAviso()).pipe(
              filter(respostaUsuario => respostaUsuario), 
              switchMap(() => this.create(dto)), 
            )
          } else {
            return this.create(dto)
          }
        }),
        finalize(() => (this.loading = false)),
      )
      .subscribe({
        next: resultado => {
          if (resultado) {
            this.toastr.send({
              success: true,
              message: 'Versão cadastrada com sucesso.',
            })
            this.ref.close('S')
          } 
        },
      })
  }

  public async modalAviso() {
    const ref = this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          body: `Já existe um plano LDO para o próximo exercício (${
            this.exercicioLogado + 1
          }). Deseja criar um novo para o exercício atual?
          A cópia será feita com base nos dados da última versão aprovada do exercício atual (${
            this.exercicioLogado
          }).`,
        },
        exiberIcones: false,
        dialogSize: 'medium',
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })

    return ref.onClose.pipe(take(1)).toPromise()
  }

  public update(dto: VersionsInterface, fecharDialogo: boolean = true) {
    this.subscription = this.service
      .putVersion(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Versão atualizada com sucesso.',
          })
          if (fecharDialogo) this.ref.close('S')
        },
        (err: any) => this.toastr.bulkSend(err.mensagens),
      )
  }

  public submit() {
    if (this.form.invalid) {
      this.toastr.send({
        error: true,
        message: 'Verifique os campos obrigatórios.',
      })
      this.form.markAllAsTouched()
    } else {
      this.loading = true
      this.form.patchValue({
        ppaUuid: this.ppaUuid,
      })
      const dto = this.form.getRawValue()
      dto.leiPpaUuid = dto.leiPpaUuid?.uuid
      if (this.form.get('uuid').value) {
        this.update(dto)
      } else {
        this.validarCadastroVersao(dto)
      }
    }
  }

  private situacaoChange(): void {
    this.form
      .get('situacaoVersaoId')
      .valueChanges.pipe(distinctUntilChanged(), debounceTime(100))
      .subscribe(value => {
        const leiPpaUuid = this.form.get('leiPpaUuid').value

        this.situationData.forEach(element => {
          if (element.id === value) {
            this.form.get('situacaoVersaoUuid').patchValue(element.uuid)
          }
        })
        this.escopeData.forEach(element => {
          if (element.uuid === this.form.get('ppaEscopoDocumentoUuid').value) {
            this.escopo = element.id
          }
        })

        if (value === 2) {
          this.leisVisible = true
        } else {
          this.leisVisible = false
        }
        this.form.get('leiPpaUuid').patchValue(leiPpaUuid)
      })
  }

  private escopoChange(): void {
    this.form
      .get('ppaEscopoDocumentoUuid')
      .valueChanges.pipe(distinctUntilChanged(), debounceTime(100))
      .subscribe(value => {
        const leiPpaUuid = this.form.get('leiPpaUuid').value

        this.escopeData.items().forEach(element => {
          if (element.uuid === value) {
            this.escopo = element.id
          }
        })

        this.form.get('leiPpaUuid').patchValue(leiPpaUuid)
      })
  }

  onDecreeSearchDialog() {
    const ref = this.dialogService.open(DecreeSearchComponent, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })

    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.setDecreeFields(res)
      })
  }

  private setDecreeFields(decree: DecreeInterface) {
    this.form.get('leiPpaUuid').patchValue({
      uuid: decree.uuid,
      codigo: decree.codigo,
      nome: decree.escopoDocumentoTce?.nome,
    })
  }

  onDecreeSearchInput(value: string) {
    if (value === '' || !value || !(/^\d+$/.test(value))) {
      this.form.get('leiPpaUuid').reset()
      return
    }

    this.loading = true
    this.crudService
      .getDataSourceFiltro('uuid', 'lei/paginado', 1, 'codigo', value)
      .load()
      .then(
        res => {
          this.loading = false
          if (res.length === 0) {
            this.toastr.send({
              error: true,
              message: 'Lei não encontrada.',
            })
            this.form.get('leiPpaUuid').reset()
          } else {
            this.setDecreeFields(res[0])
          }
        },
        err => {
          this.toastr.send({
            error: true,
            message: err,
          })
        },
      )
  }

  public abrirEditorTexto() {
    const ref = this.dialogService.open(EditorDocComponent, {
      context: {
        pageTitle: 'Objetivos Macros',
        dados: this.form.get('objetivoMacro').value,
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    })

    ref.onClose
      .pipe(
        take(1),
        filter(res => res),
      )
      .subscribe(res => {
        this.form.get('objetivoMacro').patchValue(res)
      })
  }

  public copiar() {
    this.loading = true
    this.service.copiarVersao(this.ppaVersionUuid)
      .pipe(
        finalize(() => (this.loading = false)),
      )
      .subscribe({
        next: resultado => {
          if (resultado) {
            this.toastr.send({
              success: true,
              message: 'Versão copiada com sucesso.',
            })
            this.ref.close('S')
          } 
        },
      })
  }
}
