<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
        <eqp-breadcrumb></eqp-breadcrumb>
      </div>
      <div class="col-md-4 pt-2">
        <p>{{ ppaDescricao }}</p>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <dx-data-grid
      id="placeGrid"
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="false"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onToolbarPreparing)="onToolbarPreparingVersao($event)"
    >
      <dxo-export
        [enabled]="true"
        [excelWrapTextEnabled]="true"
        [excelFilterEnabled]="true"
        [fileName]="pageTitle"
      ></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar versão"
      ></dxo-search-panel>

      <dxo-editing
        *ngIf="!desabilitarCriacao"
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="nivelPermissao === 'FULL' && initNewVersion"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="numeroVersao"
        caption="Versão"
        [width]="110"
        alignment="left"
      ></dxi-column>

      <dxi-column
        dataField="situacaoVersaoId.nome"
        caption="Situação"
      ></dxi-column>

      <dxi-column
        dataField="ppaEscopoDocumentoId.nome"
        caption="Escopo"
      ></dxi-column>

      <dxi-column
        dataField="leiExercicio"
        caption="Lei/Exercício"
        [allowSorting]="false"
        [allowFiltering]="false"
      ></dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="80"
        [allowFiltering]="false"
        [allowSorting]="false"
        cellTemplate="acaoColumn"
      ></dxi-column>

      <div *dxTemplate="let data of 'acaoColumn'">
        <a
          *ngIf="!desabilitarCriacao"
          title="Visualizar"
          (click)="visualizar(data.data)"
          class="dx-link dx-link-edit fas fa-eye dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          *ngIf="!desabilitarCriacao"
          title="Alterar"
          (click)="edit(data.value)"
          class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
        >
        </a>
        <a
          *ngIf="
            nivelPermissao === 'FULL' &&
            data.data.numeroVersao !== 1 &&
            data.data.situacaoVersaoId.nome !== 'Aprovado'
          "
          title="Remover"
          (click)="remove(data.value)"
          class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
        >
        </a>
      </div>
    </dx-data-grid>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="back()">
          Voltar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
