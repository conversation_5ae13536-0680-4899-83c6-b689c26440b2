import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { ModalConfirmarComponent } from '@dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { MultiYearPlanService } from '@pages/planning/multi-year-plan/services/multi-year-plan.service'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import { UserDataService } from '@guards/services/user-data.service'
import DataSource from 'devextreme/data/data_source'
import { VersionFormComponent } from './../version-form/version-form.component'
import { ppaExerciseInterface } from '@pages/planning/multi-year-plan/revenue-projection-ppa-ldo/components/helpers/ppa-exercises'

@Component({
  selector: 'eqp-version-list',
  templateUrl: './version-list.component.html',
  styleUrls: ['./version-list.component.scss'],
})
export class VersionListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public pageTitle: string = 'Versão Plano Plurianual'
  public loading: boolean = false
  public dataSource: DataSource
  public lawList: any[] = []
  public columns: DxColumnInterface[] = []
  public ppaDescricao: string
  private subscription: Subscription

  public initNewVersion: boolean = true
  public desabilitarCriacao: boolean = true
  public habilitarControleLeiAto: boolean = true

  private ppaUuid: string

  constructor(
    private service: MultiYearPlanService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public router: Router,
    public menuService: MenuService,
    private route: ActivatedRoute,
    private userDataService: UserDataService
  ) {
    super(menuService, router)
    this.permissao('/plano-plurianual')
  }

  public ngOnInit(): void {
    this.route.params.pipe(first()).subscribe(params => {
      this.ppaUuid = params['uuid']
      if (this.ppaUuid) {
        this.obterPPA()
        this.fetchGrid()
      }
    })
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  private obterPPA() {
    this.service
      .obterPpa(this.ppaUuid)
      .pipe(first())
      .subscribe(ppa => {
        const {exercicioInicial, exercicioFinal} = ppa.dados
        this.ocultarBotao(exercicioInicial, exercicioFinal)
      })
  }

  public async fetchGrid() {
    this.dataSource = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa/ppa_versao',
        10,
        'ppaUuid',
        this.ppaUuid,
      ),
      paginate: true,
      pageSize: 10,
      sort: [{selector: 'ppaEscopoDocumentoId.nome', desc: false}],
      map: item => {
        this.habilitarControleLeiAto = false
        let descricaoEscopoDocumento = item.ppaEscopoDocumentoId?.nome || ''
        if(item.ppaEscopoDocumentoId.id !== 1) descricaoEscopoDocumento += ` ${item.ppaEscopoDocumentoId?.exer}`
        this.ppaDescricao = item.ppaId?.descricao

        const { ppaEscopoDocumentoId, leiPpa, exercicio } = item;
        return {
          ...item,
          ppaEscopoDocumentoId: {
            nome: ppaEscopoDocumentoId.id === 2 
              ? `${ppaEscopoDocumentoId.nome}/ ${exercicio}` 
              : ppaEscopoDocumentoId.nome
          },
          leiExercicio: leiPpa
            ? `${leiPpa.tipoDocumento?.nome ?? ''} - ${leiPpa.numero ?? ''} / ${leiPpa.ano ?? ''}`
            : '-',
        };
      },
    })
  }

  public onToolbarPreparingVersao(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Nova versão'
          item.options.hint = 'Nova versão'
          item.options.onClick = () => this.onInitNewRow()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public ocultarBotao(inicial: number, final: number) {
    const exercicioLogado: number = parseInt(this.userDataService.userData?.exercicio)

    const exercicioInicial = inicial
    const exercicioFinal = final

    if (exercicioLogado >= exercicioInicial && exercicioLogado <= exercicioFinal) {
      this.desabilitarCriacao = false
    } else {
      this.desabilitarCriacao = true
    }
  }

  public onInitNewRow(): void {
    const dialogRef = this.dialogService.open(VersionFormComponent, {
      context: { 
        ppaUuid: this.ppaUuid, 
        habilitarControleLeiAto: this.habilitarControleLeiAto 
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.fetchGrid()
      }
    })
  }

  public edit(uuid: string): void {
    const dialogRef = this.dialogService.open(VersionFormComponent, {
      context: { ppaVersionUuid: uuid, ppaUuid: this.ppaUuid, habilitarControleLeiAto: this.habilitarControleLeiAto },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.fetchGrid()
      }
    })
  }

  public remove(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.subscribe(retorno => {
      if (retorno === 'S') {
        this.service.deleteVerion(uuid).subscribe(
          () => {
            this.toastr.send({
              success: true,
              message: 'Versão excluída com sucesso.',
            })
            this.fetchGrid()
          },
          (resp: any) => this.toastr.bulkSend(resp.mensagens),
        )
      }
    })
  }

  back() {
    this.gravarParametros()
    this.router.navigate([`/plano-plurianual`])
  }

  visualizar(ppa: ppaExerciseInterface): void {

  }
}
