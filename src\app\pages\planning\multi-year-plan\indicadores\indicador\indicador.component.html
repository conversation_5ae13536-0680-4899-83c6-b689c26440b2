<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row align">
      <div class="col-md-4">
        <h5>{{ pageTitle }}</h5>
      </div>
      <div class="col-md-8 d-flex" [formGroup]="formulario">
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Plano Plurianual"
            placeholder=""
            readonly="true"
            formControlName="planoPlurianual"
          >
          </eqp-nebular-input>
        </div>
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Versão/Situação"
            placeholder=""
            readonly="true"
            formControlName="versaoLabel"
          >
          </eqp-nebular-input>
        </div>
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Lei"
            placeholder=""
            readonly="true"
            formControlName="leiLabel"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <nb-tabset formGroupName="indicador">
      <nb-tab tabTitle="Indicador">
        <div class="row">
          <div [class]="(modal ? 'col-md-12' : 'col-md-3') + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              [readonly]="formulario.get('indicador.uuid').value"
              name="Código"
              label="Código"
              placeholder="Código"
              formControlName="codigo"
            >
            </eqp-nebular-input>
          </div>
          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              name="Metodologia"
              label="Metodologia"
              placeholder="Metodologia"
              formControlName="metodologia"
              maxlength="100"
            >
            </eqp-nebular-input>
          </div>
          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mt-4'">
            <button class="btn btn-success" (click)="abrirEditorTexto()">Avaliação</button>
          </div>
          <div class="col-md-12">
            <table class="table table-hover min-width-650">
              <thead>
                <tr class="d-flex">
                  <th [class]="'col-4 '">Ano</th>
                  <th [class]="'col-4 '">Esperado</th>
                  <th [class]="'col-4 '">Realizado</th>
                </tr>
              </thead>
              <tbody>
                <tr class="d-flex">
                  <td [class]="'col-md-4 '">
                    {{ formulario.get('indicador.ano1').value }}
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="esperado1"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="realizado1"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                </tr>
                <tr class="d-flex">
                  <td [class]="'col-md-4 '">
                    {{ formulario.get('indicador.ano2').value }}
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="esperado2"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="realizado2"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                </tr>
                <tr class="d-flex">
                  <td [class]="'col-md-4 '">
                    {{ formulario.get('indicador.ano3').value }}
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="esperado3"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="realizado3"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                </tr>
                <tr class="d-flex">
                  <td [class]="'col-md-4 '">
                    {{ formulario.get('indicador.ano4').value }}
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="esperado4"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                  <td [class]="'col-4 '">
                    <eqp-nebular-input
                      [style]="'currency'"
                      [size]="'small'"
                      [shape]="'rectangle'"
                      formControlName="realizado4"
                      [options]="{
                        prefix: 'R$ ',
                        decimal: ',',
                        precision: 2,
                        thousands: '.',
                        align: 'left',
                        allowNegative: false
                      }"
                    >
                    </eqp-nebular-input>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </nb-tab>
      <nb-tab tabTitle="Movimento">
        <div class="label-codigo">
          {{ 'Indicador: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #indicadorMovimentoGrid
          id="indicadorMovimentoGrid"
          [dataSource]="movimentoData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingMovimento($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar movimento"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="uuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="numero"
            caption="Número"
            alignment="left"
            sortOrder="asc"
          ></dxi-column>
          <dxi-column
            dataField="tipoMovimentoUuid"
            caption="Tipo de movimento"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoMovimentoNome"
            caption="Tipo de movimento"
          ></dxi-column>
          <dxi-column dataField="nome" caption="Nome"></dxi-column>
          <dxi-column
            dataField="dataMovimento"
            caption="Data do movimento"
            dataType="date"
          ></dxi-column>
          <dxi-column
            dataField="dataBase"
            caption="Data base"
            dataType="date"
          ></dxi-column>
          <dxi-column
            dataField="ppaTipoIndicadorUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaTipoIndicadorNome"
            caption="Tipo de indicador"
          ></dxi-column>
          <dxi-column
            dataField="ppaNaturezaIndicadorUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaNaturezaIndicadorNome"
            caption="Natureza do indicador"
          ></dxi-column>
          <dxi-column
            dataField="ppaTipoPublicoAlvoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaTipoPublicoAlvoNome"
            caption="Publico alvo"
          ></dxi-column>
          <dxi-column
            dataField="unidadeMedidaUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="unidadeMedidaNome"
            caption="Unidade de medida"
          ></dxi-column>
          <dxi-column
            dataField="medidaInicial"
            caption="Medida inicial"
            alignment="left"
          ></dxi-column>
          <dxi-column
            dataField="notaExplicativa"
            caption="Nota explicativa"
          ></dxi-column>

          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarMovimento(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === movimentoData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerMovimento(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab
        tabTitle="Programas vinculados"
        *ngIf="formulario.get('indicador.uuid').value"
      >
        <div class="label-codigo">
          {{ 'Indicador: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #acaoGrid
          id="programaGrid"
          [dataSource]="programaData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar ação"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="false"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            dataField="codigo"
            caption="Código"
            alignment="left"
          ></dxi-column>
          <dxi-column dataField="nome" caption="Nome"></dxi-column>
        </dx-data-grid>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>
        <button
          *ngIf="
            formulario.get('indicador.uuid').value && nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          *ngIf="
            (formulario.get('indicador.uuid').value &&
              nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
