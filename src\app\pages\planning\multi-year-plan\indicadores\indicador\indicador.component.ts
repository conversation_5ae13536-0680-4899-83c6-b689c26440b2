import { Component, Input, OnInit, ViewChild } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import {
  getMomentDate,
  getParsedDate,
  getParsedDateTela,
} from '@common/helpers/parsers'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { NbDialogRef, NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { DxDataGridComponent } from 'devextreme-angular'
import DataSource from 'devextreme/data/data_source'
import { filter, finalize, first, map, take } from 'rxjs/operators'
import { ToastrService } from './../../../../../@common/services/toastr/toastr.service'
import { IndicadorService } from './../indicador.service'
import { MovimentoIndicadorComponent } from './../movimento-indicador/movimento-indicador.component'
import { EditorDocComponent } from '@common/dialogs/editor-doc/editor-doc.component'

@Component({
  selector: 'eqp-indicador',
  templateUrl: './indicador.component.html',
  styleUrls: ['./indicador.component.scss'],
})
export class IndicadorComponent extends BaseTelasComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Indicador'
  public formulario: FormGroup
  public ppaDescricao: string
  private versaoPpaUuid: string
  private ppaUuid: string
  public codigoEhNome: string
  public programaData: any

  @Input() public modal: boolean = false
  @Input() public ref: NbDialogRef<IndicadorComponent>

  private versao: any

  public movimentoData: any

  @ViewChild('indicadorMovimentoGrid', { static: false })
  indicadorMovimentoGrid: DxDataGridComponent

  constructor(
    private formBuilder: FormBuilder,
    public menuService: MenuService,
    private route: ActivatedRoute,
    public router: Router,
    private service: IndicadorService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService, router)
    this.permissao('/indicador')
  }

  ngOnInit(): void {
    this.versaoPpaUuid = JSON.parse(
      localStorage.getItem('indicadorVersaoPpaUuid'),
    )
    this.ppaUuid = JSON.parse(localStorage.getItem('indicadorPpaUuid'))
    localStorage.removeItem('indicadorVersaoPpaUuid')
    localStorage.removeItem('indicadorPpaUuid')
    this.formulario = this.getNovoFormulario()
    if (this.versaoPpaUuid == null || this.ppaUuid == null) {
      this.cancelar(null)
    } else {
      this.loading = true
      this.service
        .getVersionIndividual(this.versaoPpaUuid)
        .pipe(first())
        .subscribe(data => {
          this.formulario.patchValue({
            planoPlurianual: data.dados.ppaId.descricao,
            versaoLabel:
              data.dados.numeroVersao +
              ' - ' +
              data.dados.situacaoVersaoId.nome +
              ' - ' +
              data.dados.ppaEscopoDocumentoId.nome,
            leiLabel: data.dados?.leiPpa
              ? `${data.dados?.leiPpa?.tipoDocumento.nome} - ${data.dados.leiPpa?.numero} / ${data.dados.leiPpa?.ano}`
              : ' - ',
          })
          this.ppaDescricao = data.dados.ppaId?.descricao
          this.versao = data.dados
          this.carregarTela()
        })
    }
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid && !this.modal) this.buscar(uuid)
      else {
        this.loadMedidaIndicadorNovo()
        this.movimentoData = new DataSource({
          store: [],
          paginate: false,
        })
        this.programaData = new DataSource({
          store: [],
          paginate: false,
        })
        this.loading = false
      }
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        map(data => {
          this.codigoEhNome = data.dados.codigo + ' - ' + data.dados.nome
          const movimentos = []
          data.dados.movimentos.forEach(item => {
            movimentos.push({
              ...item,
              ppaNaturezaIndicadorUuid: item.ppaNaturezaIndicador
                ? item.ppaNaturezaIndicador.uuid
                : null,
              ppaNaturezaIndicadorNome: item.ppaNaturezaIndicador
                ? item.ppaNaturezaIndicador.nome
                : null,
              ppaTipoIndicadorUuid: item.ppaTipoIndicador
                ? item.ppaTipoIndicador.uuid
                : null,
              ppaTipoIndicadorNome: item.ppaTipoIndicador
                ? item.ppaTipoIndicador.nome
                : null,
              ppaTipoPublicoAlvoUuid: item.ppaTipoPublicoAlvo
                ? item.ppaTipoPublicoAlvo.uuid
                : null,
              ppaTipoPublicoAlvoNome: item.ppaTipoPublicoAlvo
                ? item.ppaTipoPublicoAlvo.nome
                : null,
              tipoMovimentoUuid: item.tipoMovimento
                ? item.tipoMovimento.uuid
                : null,
              tipoMovimentoNome: item.tipoMovimento
                ? item.tipoMovimento.nome
                : null,
              unidadeMedidaUuid: item.unidadeMedida
                ? item.unidadeMedida.uuid
                : null,
              unidadeMedidaNome: item.unidadeMedida
                ? item.unidadeMedida.nome
                : null,
              dataMovimento: getMomentDate(item.dataMovimento),
              dataBase: getMomentDate(item.dataBase),
            })
          })

          const programas = []
          if (data.dados.programas) {
            data.dados.programas.forEach(item => {
              programas.push({
                codigo: item.ppaPrograma.codigo,
                nome: item.ppaPrograma.nome,
              })
            })
          }

          data.dados = {
            ...data.dados,
            movimentos: movimentos,
            programas: programas,
          }
          return data
        }),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('indicador').patchValue(data.dados)
        this.movimentoData = new DataSource({
          store: data.dados.movimentos ? data.dados.movimentos : [],
          paginate: false,
        })
        this.programaData = new DataSource({
          store: data.dados.programas ? data.dados.programas : [],
          paginate: false,
        })
        if (
          !data.dados.medidasIndicador ||
          data.dados.medidasIndicador.length === 0
        ) {
          this.loadMedidaIndicadorNovo()
        } else {
          this.formulario
            .get('indicador.ano1')
            .patchValue(data.dados.medidasIndicador[0].anoBase)
          this.formulario
            .get('indicador.esperado1')
            .patchValue(data.dados.medidasIndicador[0].medidaEsperada)
          this.formulario
            .get('indicador.realizado1')
            .patchValue(data.dados.medidasIndicador[0].medidaRealizada)

          this.formulario
            .get('indicador.ano2')
            .patchValue(data.dados.medidasIndicador[1].anoBase)
          this.formulario
            .get('indicador.esperado2')
            .patchValue(data.dados.medidasIndicador[1].medidaEsperada)
          this.formulario
            .get('indicador.realizado2')
            .patchValue(data.dados.medidasIndicador[1].medidaRealizada)

          this.formulario
            .get('indicador.ano3')
            .patchValue(data.dados.medidasIndicador[2].anoBase)
          this.formulario
            .get('indicador.esperado3')
            .patchValue(data.dados.medidasIndicador[2].medidaEsperada)
          this.formulario
            .get('indicador.realizado3')
            .patchValue(data.dados.medidasIndicador[2].medidaRealizada)

          this.formulario
            .get('indicador.ano4')
            .patchValue(data.dados.medidasIndicador[3].anoBase)
          this.formulario
            .get('indicador.esperado4')
            .patchValue(data.dados.medidasIndicador[3].medidaEsperada)
          this.formulario
            .get('indicador.realizado4')
            .patchValue(data.dados.medidasIndicador[3].medidaRealizada)
        }
      })
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      planoPlurianual: [],
      versaoLabel: [],
      leiLabel: [],
      indicador: this.formBuilder.group({
        uuid: [null],
        ppaVersaoUuid: [this.versaoPpaUuid],
        codigo: [null],
        metodologia: [],
        avaliacao: [],

        ano1: [null],
        ano2: [null],
        ano3: [null],
        ano4: [null],

        esperado1: [null],
        esperado2: [null],
        esperado3: [null],
        esperado4: [null],

        realizado1: [null],
        realizado2: [null],
        realizado3: [null],
        realizado4: [null],
      }),
    })
  }

  public abrirEditorTexto() {
    const ref = this.dialogService.open(EditorDocComponent, {
      context: {
        pageTitle: 'Avaliação',
        dados: this.formulario.get('indicador.avaliacao').value
      },
      closeOnBackdropClick: false,
      closeOnEsc: false
    })

    ref.onClose.pipe(take(1), filter((res) => res)).subscribe((res) => {
      this.formulario.get('indicador.avaliacao').patchValue(res)
    })
  }

  public cancelar(retorno: any): void {
    if (!this.modal) {
      this.gravarParametros()
      localStorage.setItem('indicadorPpaUuid', JSON.stringify(this.ppaUuid))
      localStorage.setItem(
        'indicadorVersaoPpaUuid',
        JSON.stringify(this.versaoPpaUuid),
      )
      this.router.navigate([`indicador`])
    } else {
      this.ref.close(retorno)
    }
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this.service
          .delete(this.formulario.get('indicador.uuid').value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Indicador excluído com sucesso.',
              })
              this.cancelar(null)
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      this.loading = true
      if (this.formulario.get('indicador.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getIndicadorDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Indicador criado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getIndicadorDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(retorno => {
        this.toastr.send({
          success: true,
          message: 'Indicador atualizado com sucesso.',
        })
        this.cancelar(retorno.body.dados.uuid)
      })
  }

  private getIndicadorDto(): any {
    const dto = this.formulario.getRawValue()

    const medidaIndicador: any = []
    medidaIndicador.push({
      anoBase: this.formulario.get('indicador.ano1').value,
      medidaEsperada: this.formulario.get('indicador.esperado1').value,
      medidaRealizada: this.formulario.get('indicador.realizado1').value,
    })
    medidaIndicador.push({
      anoBase: this.formulario.get('indicador.ano2').value,
      medidaEsperada: this.formulario.get('indicador.esperado2').value,
      medidaRealizada: this.formulario.get('indicador.realizado2').value,
    })
    medidaIndicador.push({
      anoBase: this.formulario.get('indicador.ano3').value,
      medidaEsperada: this.formulario.get('indicador.esperado3').value,
      medidaRealizada: this.formulario.get('indicador.realizado3').value,
    })
    medidaIndicador.push({
      anoBase: this.formulario.get('indicador.ano4').value,
      medidaEsperada: this.formulario.get('indicador.esperado4').value,
      medidaRealizada: this.formulario.get('indicador.realizado4').value,
    })

    dto.indicador.medidasIndicador = medidaIndicador

    dto.indicador.movimentos = []
    this.movimentoData.items().forEach(item => {
      dto.indicador.movimentos.push({
        id: item.id,
        uuid: item.uuid,
        numero: item.numero,
        tipoMovimentoUuid: item.tipoMovimentoUuid,
        nome: item.nome,
        dataMovimento: item.dataMovimento
          ? getParsedDate(item.dataMovimento)
          : null,
        dataBase: item.dataBase ? getParsedDate(item.dataBase) : null,
        ppaTipoIndicadorUuid: item.ppaTipoIndicadorUuid,
        ppaNaturezaIndicadorUuid: item.ppaNaturezaIndicadorUuid,
        ppaTipoPublicoAlvoUuid: item.ppaTipoPublicoAlvoUuid,
        unidadeMedidaUuid: item.unidadeMedidaUuid,
        medidaInicial: item.medidaInicial,
        notaExplicativa: item.notaExplicativa,
      })
    })

    return dto.indicador
  }

  //--------------------------medida indicador ----------------------------

  private loadMedidaIndicadorNovo(): void {
    this.formulario
      .get('indicador.ano1')
      .patchValue(this.versao.ppaId.exercicioBase + 1)
    this.formulario.get('indicador.esperado1').patchValue('')
    this.formulario.get('indicador.realizado1').patchValue('')

    this.formulario
      .get('indicador.ano2')
      .patchValue(this.versao.ppaId.exercicioBase + 2)
    this.formulario.get('indicador.esperado2').patchValue('')
    this.formulario.get('indicador.realizado2').patchValue('')

    this.formulario
      .get('indicador.ano3')
      .patchValue(this.versao.ppaId.exercicioBase + 3)
    this.formulario.get('indicador.esperado3').patchValue('')
    this.formulario.get('indicador.realizado3').patchValue('')

    this.formulario
      .get('indicador.ano4')
      .patchValue(this.versao.ppaId.exercicioBase + 4)
    this.formulario.get('indicador.esperado4').patchValue('')
    this.formulario.get('indicador.realizado4').patchValue('')
  }

  //-------------------------------movimentos-------------------------------

  public onToolbarPreparingMovimento(event: any): void {
    if (event.toolbarOptions.items.length > 0) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Novo movimento'
          item.options.hint = 'Novo movimento'
          item.options.onClick = () => this.onInitNewRowMovimento()
        }

        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    }
  }

  public onInitNewRowMovimento(): void {
    const lista = []
    this.movimentoData.items().forEach(item => {
      lista.push(item)
    })
    let ultimo
    if (lista.length > 0) {
      ultimo = lista[lista.length - 1]
    }
    const dialogRef = this.dialogService.open(MovimentoIndicadorComponent, {
      context: {
        numeroAtual: lista.length,
        versao: this.versao,
        menorData: ultimo?.dataMovimento ? ultimo.dataMovimento : null,
        podeGravar: this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista.push(retorno)
        this.movimentoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public alterarMovimento(e) {
    const lista = []
    this.movimentoData.items().forEach(item => {
      lista.push(item)
    })

    const movimento: any = {
      id: e.data.id,
      uuid: e.data.uuid,
      ppaIndicadorUuid: e.data.ppaIndicadorUuid,
      numero: e.data.numero,
      tipoMovimentoUuid: e.data.tipoMovimentoUuid,
      tipoMovimentoNome: e.data.tipoMovimentoNome,
      nome: e.data.nome,
      dataMovimento: e.data.dataMovimento,
      dataBase: e.data.dataBase,
      ppaTipoIndicadorUuid: e.data.ppaTipoIndicadorUuid,
      ppaTipoIndicadorNome: e.data.ppaTipoIndicadorNome,
      ppaNaturezaIndicadorUuid: e.data.ppaNaturezaIndicadorUuid,
      ppaNaturezaIndicadorNome: e.data.ppaNaturezaIndicadorNome,
      ppaTipoPublicoAlvoUuid: e.data.ppaTipoPublicoAlvoUuid,
      ppaTipoPublicoAlvoNome: e.data.ppaTipoPublicoAlvoNome,
      unidadeMedidaUuid: e.data.unidadeMedidaUuid,
      unidadeMedidaNome: e.data.unidadeMedidaNome,
      medidaInicial: e.data.medidaInicial,
      notaExplicativa: e.data.notaExplicativa,
    }

    let ultimo
    if (e.rowIndex - 1 >= 0) {
      ultimo = lista[e.rowIndex - 1]
    }

    const dialogRef = this.dialogService.open(MovimentoIndicadorComponent, {
      context: {
        dados: movimento,
        numeroAtual: lista.length,
        versao: this.versao,
        menorData: ultimo?.dataMovimento ? ultimo.dataMovimento : null,
        podeGravar:
          (this.formulario.get('indicador.uuid').value &&
            this.nivelPermissao === 'EDITOR') ||
          this.nivelPermissao === 'FULL',
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista[e.rowIndex] = retorno
        this.movimentoData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public removerMovimento(e) {
    this.indicadorMovimentoGrid.instance.deleteRow(e.rowIndex)
  }

  public dataTela(data): string {
    return getParsedDateTela(data)
  }
}
