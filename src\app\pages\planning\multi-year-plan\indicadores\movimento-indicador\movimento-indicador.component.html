<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row">
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [readonly]="true"
          name="Número"
          label="Número"
          placeholder="Número"
          formControlName="numero"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo de movimento"
          label="Tipo de movimento"
          placeholder="Tipo de movimento"
          [dataSource]="tipoMovimentoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="tipoMovimentoUuid"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data do movimento"
          label="Data do movimento"
          placeholder="Data do movimento"
          formControlName="dataMovimento"
          [minDate]="menorData"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data base"
          label="Data base"
          placeholder="Data base"
          required="true"
          formControlName="dataBase"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'currency'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="medidaInicial"
          [options]="{
            prefix: '',
            decimal: ',',
            precision: 2,
            thousands: '.',
            align: 'left',
            allowNegative: false
          }"
          name="Medida inicial"
          label="Medida inicial"
          placeholder="Medida inicial"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-2 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Unidade de medida"
          label="Unidade de medida"
          placeholder="Unidade de medida"
          [dataSource]="unidadeMedidaData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="unidadeMedidaUuid"
          required="true"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo de indicador"
          label="Tipo de indicador"
          placeholder="Tipo de indicador"
          [dataSource]="ppaTipoIndicadorData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="ppaTipoIndicadorUuid"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Natureza do indicador"
          label="Natureza do indicador"
          placeholder="Natureza do indicador"
          [dataSource]="ppaNaturezaIndicadorData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="ppaNaturezaIndicadorUuid"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Publico alvo"
          label="Publico alvo"
          placeholder="Publico alvo"
          [dataSource]="ppaTipoPublicoAlvoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="ppaTipoPublicoAlvoUuid"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-12 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nome"
          label="Nome"
          placeholder="Nome"
          formControlName="nome"
          required="true"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-12 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nota explicativa"
          label="Nota explicativa"
          placeholder="Nota explicativa"
          formControlName="notaExplicativa"
          [required]="notaObrigatoria"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="podeGravar"
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
