import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { distinctUntilChanged } from 'rxjs/operators'
import { IndicadorService } from './../indicador.service'

@Component({
  selector: 'eqp-movimento-indicador',
  templateUrl: './movimento-indicador.component.html',
  styleUrls: ['./movimento-indicador.component.scss'],
})
export class MovimentoIndicadorComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Movimento do Indicador'
  public formulario: FormGroup
  public tipoMovimentoData: any
  public ppaTipoIndicadorData: any
  public ppaNaturezaIndicadorData: any
  public ppaTipoPublicoAlvoData: any
  public unidadeMedidaData: any
  public notaObrigatoria: boolean = false

  @Input() public dados: any
  @Input() public numeroAtual: number
  @Input() public versao: any
  @Input() public menorData: any
  @Input() public podeGravar: boolean = false

  constructor(
    private formBuilder: FormBuilder,
    private service: IndicadorService,
    public ref: NbDialogRef<MovimentoIndicadorComponent>,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.changeTipoMovimento()
    this.tipoMovimentoData = new DataSource({
      store: [],
      paginate: true,
      pageSize: 10,
    })
    this.formulario.patchValue(this.dados)
    this.loadSelects()
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      id: [null],
      uuid: [null],
      ppaIndicadorUuid: [null],
      numero: [this.numeroAtual + 1],
      tipoMovimentoUuid: [null, Validators.required],
      tipoMovimentoNome: [null],
      nome: [null, Validators.required],
      dataMovimento: [null, Validators.required],
      dataBase: [null, Validators.required],
      ppaTipoIndicadorUuid: [null, Validators.required],
      ppaTipoIndicadorNome: [null],
      ppaNaturezaIndicadorUuid: [null, Validators.required],
      ppaNaturezaIndicadorNome: [null],
      ppaTipoPublicoAlvoUuid: [null, Validators.required],
      ppaTipoPublicoAlvoNome: [null],
      unidadeMedidaUuid: [null, Validators.required],
      unidadeMedidaNome: [null],
      medidaInicial: [null, Validators.required],
      notaExplicativa: [null],
    })
  }

  public cancelar(): void {
    this.ref.close(null)
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      const movimento = this.formulario.getRawValue()
      if (this.tipoMovimentoData.items().length > 0) {
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === movimento.tipoMovimentoUuid) {
            movimento.tipoMovimentoNome = item.nome
          }
        })
      }

      if (this.ppaTipoIndicadorData.items().length > 0) {
        this.ppaTipoIndicadorData.items().forEach(item => {
          if (item.uuid === movimento.ppaTipoIndicadorUuid) {
            movimento.ppaTipoIndicadorNome = item.nome
          }
        })
      }

      if (this.ppaNaturezaIndicadorData.items().length > 0) {
        this.ppaNaturezaIndicadorData.items().forEach(item => {
          if (item.uuid === movimento.ppaNaturezaIndicadorUuid) {
            movimento.ppaNaturezaIndicadorNome = item.nome
          }
        })
      }

      if (this.ppaTipoPublicoAlvoData.items().length > 0) {
        this.ppaTipoPublicoAlvoData.items().forEach(item => {
          if (item.uuid === movimento.ppaTipoPublicoAlvoUuid) {
            movimento.ppaTipoPublicoAlvoNome = item.nome
          }
        })
      }

      if (this.unidadeMedidaData.items().length > 0) {
        this.unidadeMedidaData.items().forEach(item => {
          if (item.uuid === movimento.unidadeMedidaUuid) {
            movimento.unidadeMedidaNome = item.nome
          }
        })
      }

      this.ref.close(movimento)
    }
  }

  private loadSelects(): void {
    this.tipoMovimentoData = new DataSource({
      store:
        this.versao.situacaoVersaoId.nome === 'Aprovado'
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_indicador/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '3',
            )
          : this.numeroAtual === 0 || (this.dados && this.dados.numero === 1)
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_indicador/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '1',
            )
          : this.service.getDataSourceFiltro(
              'uuid',
              'ppa_indicador/tipo_movimento',
              10,
            ),
      paginate: true,
      pageSize: 10,
    })

    this.ppaTipoIndicadorData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_indicador/ppa_tipo_indicador',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })

    this.ppaNaturezaIndicadorData = new DataSource({
      store: this.formulario.get('ppaTipoIndicadorUuid').value
        ? this.service.getDataSourceFiltro(
            'uuid',
            'ppa_indicador/ppa_natureza_indicador',
            10,
            'tipoIndicadorUuid',
            this.formulario.get('ppaTipoIndicadorUuid').value,
          )
        : [],
      paginate: true,
      pageSize: 10,
    })

    this.ppaTipoPublicoAlvoData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_indicador/ppa_tipo_publico_alvo',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })

    this.unidadeMedidaData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'ppa_indicador/unidade_medida',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
    this.changeTipoIndicador()
  }

  private changeTipoIndicador(): void {
    this.formulario
      .get('ppaTipoIndicadorUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.ppaNaturezaIndicadorData = new DataSource({
          store: value
            ? this.service.getDataSourceFiltro(
                'uuid',
                'ppa_indicador/ppa_natureza_indicador',
                10,
                'tipoIndicadorUuid',
                value,
              )
            : [],
          paginate: true,
          pageSize: 10,
        })
        this.formulario.get('ppaNaturezaIndicadorUuid').patchValue('')
      })
  }

  private changeTipoMovimento(): void {
    this.formulario
      .get('tipoMovimentoUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.notaObrigatoria = false
        const nota = this.formulario.get('notaExplicativa').value
        this.formulario.get('notaExplicativa').setValidators([])
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === value) {
            if (item.tipoMovimentoTceId.codigo !== 1) {
              this.formulario
                .get('notaExplicativa')
                .setValidators([Validators.required])
              this.notaObrigatoria = true
            }
          }
        })
        this.formulario.get('notaExplicativa').patchValue('')
        this.formulario.get('notaExplicativa').patchValue(nota)
      })
  }
}
