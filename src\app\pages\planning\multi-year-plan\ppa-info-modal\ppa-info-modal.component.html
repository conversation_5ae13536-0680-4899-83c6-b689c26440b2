<eqp-nebular-dialog
  [dialogTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonId]="'fechar-info-ppa'"
  [bottomLeftButtonTitle]="'Voltar'"
  (bottomLeftButtonEmitter)="close()"
  [dialogSize]="'large'"
>
  <div class="container-fluid" *ngIf="ppaData">
    <div class="row">
      <div class="col-md-4">
        <div class="form-group">
          <label class="label"><strong>Exercício Base:</strong></label>
          <p>{{ ppaData.exercicioBase || '-' }}</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label class="label"><strong>Exercício Inicial:</strong></label>
          <p>{{ ppaData.exercicioInicial || '-' }}</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label class="label"><strong>Exercício Final:</strong></label>
          <p>{{ ppaData.exercicioFinal || '-' }}</p>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4">
        <div class="form-group">
          <label class="label"><strong>Descrição:</strong></label>
          <p>{{ ppaData.descricao || '-' }}</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group d-flex align-items-center">
          <eqp-nebular-checkbox
            class="mt-2 mr-1"
            label="Controle Lei/Ato PPA"
            name="Controle Lei/Ato PPA"
            [formControl]="flagControleLeiAto"
          ></eqp-nebular-checkbox>
          <nb-icon
            nbTooltip="Quando marcada, usa versão de PPA.
            Quando não marcada, não usa versão de PPA.
            Somente permite alteração na primeira versão do PPA."
            class="ml-2"
            class="mt-1"
            icon="question-mark-circle-outline"
            pack="eva"
            status="warning"
            style="font-size: 1.5rem"
          ></nb-icon>
        </div>
      </div>
    </div>
  </div>

  <div class="text-center p-4" *ngIf="!ppaData">
    <nb-icon icon="info-outline" pack="eva" style="font-size: 3rem"></nb-icon>
    <p class="text-muted mt-2">Nenhuma informação disponível para este PPA.</p>
  </div>
</eqp-nebular-dialog>
