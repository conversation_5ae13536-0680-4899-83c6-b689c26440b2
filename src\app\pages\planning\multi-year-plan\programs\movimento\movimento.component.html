<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          [readonly]="true"
          name="Número"
          label="Número"
          placeholder="Número"
          formControlName="numero"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Tipo de movimento"
          label="Tipo de movimento"
          placeholder="Tipo de movimento"
          [dataSource]="tipoMovimentoData"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="tipoMovimentoUuid"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Data do movimento"
          label="Data do movimento"
          placeholder="Data do movimento"
          formControlName="dataMovimento"
          [minDate]="menorData"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-8 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nome"
          label="Nome"
          placeholder="Nome"
          formControlName="nomePrograma"
          required="true"
          maxlength="250"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4 margin-top">
        <eqp-nebular-checkbox
          label="Finalístico"
          name="Finalístico"
          formControlName="finalistico"
        ></eqp-nebular-checkbox>
      </div>
      <div class="col-md-12 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Objetivo"
          label="Objetivo"
          placeholder="Objetivo"
          formControlName="objetivo"
          required="true"
          maxlength="250"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-12 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nota explicativa"
          label="Nota explicativa"
          placeholder="Nota explicativa"
          formControlName="notaexplicativa"
          [required]="notaObrigatoria"
          maxlength="250"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="podeGravar"
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
