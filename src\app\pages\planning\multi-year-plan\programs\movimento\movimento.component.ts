import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { distinctUntilChanged } from 'rxjs/operators'
import { ProgramService } from './../program.service'

@Component({
  selector: 'eqp-movimento',
  templateUrl: './movimento.component.html',
  styleUrls: ['./movimento.component.scss'],
})
export class MovimentoComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Movimento'
  public formulario: FormGroup
  public tipoMovimentoData: any
  public notaObrigatoria: boolean = false
  @Input() public numeroAtual: number

  @Input() public dados: any
  @Input() public menorData: any
  @Input() public versao: any
  @Input() public podeGravar: boolean = false

  constructor(
    private formBuilder: FormBuilder,
    private service: ProgramService,
    public ref: NbDialogRef<MovimentoComponent>,
    private toastr: ToastrService,
  ) {}

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.changeTipoMovimento()
    this.tipoMovimentoData = new DataSource({
      store: [],
      paginate: true,
      pageSize: 10,
    })
    this.formulario.patchValue(this.dados)
    this.tipoMovimentoData = new DataSource({
      store:
        this.versao.situacaoVersaoId.nome === 'Aprovado'
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_programa/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '3',
            )
          : this.numeroAtual === 0 || (this.dados && this.dados.numero === 1)
          ? this.service.getDataSourceFiltro(
              'uuid',
              'ppa_programa/tipo_movimento',
              10,
              'tipoMovimentoTceCodigo',
              '1',
            )
          : this.service.getDataSourceFiltro(
              'uuid',
              'ppa_programa/tipo_movimento',
              10,
            ),
      paginate: true,
      pageSize: 10,
    })
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      uuid: [null],
      id: [null],
      ppaProgramaUuid: [null],
      numero: [this.numeroAtual + 1],
      tipoMovimentoUuid: [null, Validators.required],
      dataMovimento: [null],
      nomePrograma: [null, Validators.required],
      finalistico: [false],
      objetivo: [null, Validators.required],
      notaexplicativa: [null],
      tipoMovimentoNome: [null],
    })
  }

  public cancelar(): void {
    this.ref.close(null)
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      let movimento = this.formulario.getRawValue()
      if (this.tipoMovimentoData.items().length > 0) {
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === movimento.tipoMovimentoUuid) {
            movimento.tipoMovimentoNome = item.nome
          }
        })
      }
      this.ref.close(movimento)
    }
  }

  private changeTipoMovimento(): void {
    this.formulario
      .get('tipoMovimentoUuid')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        this.notaObrigatoria = false
        const nota = this.formulario.get('notaExplicativa').value
        this.formulario.get('notaexplicativa').setValidators([])
        this.tipoMovimentoData.items().forEach(item => {
          if (item.uuid === value) {
            if (item.tipoMovimentoTceId.codigo !== 1) {
              this.formulario
                .get('notaexplicativa')
                .setValidators([Validators.required])
              this.notaObrigatoria = true
            }
          }
        })
        this.formulario.get('notaexplicativa').patchValue('')
        this.formulario.get('notaExplicativa').patchValue(nota)
      })
  }
}
