<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row align">
      <div class="col-md-4">
        <h5>{{ pageTitle }}</h5>
      </div>
      <div class="col-md-8 d-flex" [formGroup]="formulario">
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Plano Plurianual"
            placeholder=""
            readonly="true"
            formControlName="planoPlurianual"
          >
          </eqp-nebular-input>
        </div>
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Versão/Situação"
            placeholder=""
            readonly="true"
            formControlName="versaoLabel"
          >
          </eqp-nebular-input>
        </div>
        <div class="col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            label="Lei"
            placeholder=""
            readonly="true"
            formControlName="leiLabel"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <nb-tabset formGroupName="programa">
      <nb-tab tabTitle="Programa">
        <div class="row">
          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              [readonly]="formulario.get('programa.uuid').value"
              name="Código"
              label="Código"
              placeholder="Código"
              formControlName="codigo"
              required="true"
              primaryMask="0999"
            >
            </eqp-nebular-input>
          </div>

          <div [class]="'col-md-6 col-sm-12 mb-4'">
            <eqp-nebular-select
              *ngIf="planoPlurianual"
              [size]="'small'"
              [shape]="'rectangle'"
              name="Plano PMS/ECA"
              label="Plano PMS/ECA"
              placeholder=""
              valueExpr="uuid"
              displayExpr="numeroVersao"
              [dataSource]="ppaVersionData"
              formControlName="versaoPpaUuid"
              [searchExprOption]="[
                'situacaoVersaoId.nome',
                'ppaEscopoDocumentoId.nome'
              ]"
            ></eqp-nebular-select>
          </div>

          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <eqp-nebular-select
              [size]="'small'"
              [shape]="'rectangle'"
              name="Eixo"
              label="Eixo"
              placeholder="Eixo"
              [dataSource]="ppaEixoData"
              valueExpr="uuid"
              displayExpr="nome"
              formControlName="ppaEixoUuid"
            ></eqp-nebular-select>
          </div>

          <div [class]="(modal ? 'col-md-12' : 'col-md-4') + ' col-sm-12 mb-4'">
            <button
              type="button"
              class="btn btn-success mt-4 float-md-left"
              (click)="abrirEditorDocumentoDiagnostico()"
            >
              Diagnóstico
            </button>
            <button
              type="button"
              class="btn btn-success ml-3 mt-4 float-md-left"
              (click)="abrirEditorDocumentoAvaliacao()"
            >
              Avaliação
            </button>
          </div>
        </div>
      </nb-tab>
      <nb-tab tabTitle="Movimento">
        <div class="label-codigo">
          {{ 'Programa: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #programMovimentoGrid
          id="programMovimentoGrid"
          [dataSource]="movimentoData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingMovimento($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="none"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar movimento"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            dataField="uuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="numero"
            caption="Número"
            alignment="left"
            sortOrder="asc"
          ></dxi-column>
          <dxi-column
            dataField="tipoMovimentoUuid"
            caption="Tipo de movimento"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="tipoMovimentoNome"
            caption="Tipo de movimento"
          ></dxi-column>
          <dxi-column
            dataField="dataMovimento"
            caption="Data do movimento"
            dataType="date"
          ></dxi-column>
          <dxi-column dataField="nomePrograma" caption="Nome"></dxi-column>
          <dxi-column
            dataField="finalistico"
            caption="Finalistico"
          ></dxi-column>
          <dxi-column dataField="objetivo" caption="Objetivo"></dxi-column>
          <dxi-column
            dataField="notaexplicativa"
            caption="Nota explicativa"
          ></dxi-column>

          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarMovimento(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === movimentoData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerMovimento(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Objetivo">
        <div class="label-codigo">
          {{ 'Programa: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #objetivoGrid
          id="objetivoGrid"
          [dataSource]="objetivoData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingObjetivo($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar objetivo"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            dataField="uuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="ppaObjetivoUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaObjetivoNome"
            caption="Objetivo"
          ></dxi-column>
          <dxi-column
            dataField="baseNome"
            caption="Instrumento Base"
          ></dxi-column>
          <dxi-column dataField="itemNome" caption="Item"></dxi-column>
          <dxi-column
            dataField="politicaPublicaUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="politicaPublicaNome"
            caption="Políticas Públicas"
          ></dxi-column>

          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarObjetivo(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === objetivoData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerObjetivo(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
          <div *dxTemplate="let data of 'dataTela'">
            {{ dataTela(data.value) }}
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab tabTitle="Vincular indicador">
        <div class="label-codigo">
          {{ 'Programa: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #indicadorGrid
          id="indicadorGrid"
          [dataSource]="indicadorData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparingIndicador($event)"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar indicador"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="nivelPermissao === 'FULL'"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column dataField="id" caption="" [visible]="false"></dxi-column>
          <dxi-column
            dataField="uuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaIndicadorUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="ppaIndicadorNome"
            caption="Indicador"
          ></dxi-column>
          <dxi-column
            dataField="dataInclusaoTce"
            caption="Inclusão no TCE"
            dataType="date"
          ></dxi-column>
          <dxi-column
            dataField="dataCancelamento"
            caption="Data do cancelamento"
            dataType="date"
          ></dxi-column>
          <dxi-column
            dataField="notaExplicativa"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="leiUuid"
            caption=""
            [visible]="false"
          ></dxi-column>
          <dxi-column
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              title="Alterar"
              (click)="alterarIndicador(data)"
              class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="
                data.rowIndex === indicadorData.items().length - 1 &&
                nivelPermissao === 'FULL'
              "
              title="Remover"
              (click)="removerIndicador(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
      <nb-tab
        tabTitle="Ações vinculadas"
        *ngIf="formulario.get('programa.uuid').value"
      >
        <div class="label-codigo">
          {{ 'Programa: ' + codigoEhNome }}
        </div>
        <dx-data-grid
          #acaoGrid
          id="acaoGrid"
          [dataSource]="acaoData"
          [allowColumnResizing]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
        >
          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar ação"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="false"
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            dataField="codigo"
            caption="Código"
            alignment="left"
          ></dxi-column>
          <dxi-column dataField="nome" caption="Nome"></dxi-column>
        </dx-data-grid>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>
        <button
          *ngIf="
            formulario.get('programa.uuid').value && nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          *ngIf="
            (formulario.get('programa.uuid').value &&
              nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
