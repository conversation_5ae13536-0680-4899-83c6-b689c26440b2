import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { NbFormFieldComponent, NbFormFieldModule, NbInputModule, NbSelectModule } from '@nebular/theme'
import { MultiYearPlanComponent } from '@pages/planning/multi-year-plan/multi-year-plan.component'
import { DxCheckBoxModule, DxNumberBoxModule } from 'devextreme-angular'
import { DialogProjectActivityComponent } from './annual-budget-law/expense/project-activity/dialog-project-activity-form/dialog-project-activity-form.component'
import { ProjectActivityFormComponent } from './annual-budget-law/expense/project-activity/project-activity-form/project-activity-form.component'
import { ProjectActivityListComponent } from './annual-budget-law/expense/project-activity/project-activity-list/project-activity-list.component'
import { FinancialTransferBudgetForecastFormComponent } from './annual-budget-law/financial-transfer-budget-forecast/financial-transfer-budget-forecast-form/financial-transfer-budget-forecast-form.component'
import { FinancialTransferBudgetForecastListComponent } from './annual-budget-law/financial-transfer-budget-forecast/financial-transfer-budget-forecast-list/financial-transfer-budget-forecast-list.component'
import { LdoTaxRiskFormComponent } from './budget-guidelines-law/ldo-tax-risk/ldo-tax-risk-form/ldo-tax-risk-form.component'
import { DialogTaxRiskComponent } from './budget-guidelines-law/tax-risk/dialog-tax-risk-form/dialog-tax-risk-form.component'
import { TaxRiskFormComponent } from './budget-guidelines-law/tax-risk/tax-risk-form/tax-risk-form.component'
import { TaxRiskListComponent } from './budget-guidelines-law/tax-risk/tax-risk-list/tax-risk-list.component'
import { ExpenseAccountGroupFormComponent } from './expense-group/expense-account-group/expense-account-group-form/expense-account-group-form.component'
import { ExpenseAccountGroupListComponent } from './expense-group/expense-account-group/expense-account-group-list/expense-account-group-list.component'
import { ExpenseGroupFormComponent } from './expense-group/expense-group-form/expense-group-form.component'
import { ExpenseGroupListComponent } from './expense-group/expense-group-list/expense-group-list.component'
import { ManagePercentageFormComponent } from './expense-group/manage-percentage/manage-percentage-form/manage-percentage-form.component'
import { MonthlyLiberationFormComponent } from './expense-group/monthly-liberation/monthly-liberation-form/monthly-liberation-form.component'
import { MonthlyLiberationListComponent } from './expense-group/monthly-liberation/monthly-liberation-list/monthly-liberation-list.component'
import { AccountingPlanFormComponent } from './multi-year-plan/components/plano-de-contas/accounting-plan/accounting-plan-form/accounting-plan-form.component'
import { AccountingPlanListComponent } from './multi-year-plan/components/plano-de-contas/accounting-plan/accounting-plan-list/accounting-plan-list.component'
import { LoadStandardPlanComponent } from './multi-year-plan/components/plano-de-contas/accounting-plan/accounting-plan-list/load-standard-plan/load-standard-plan.component'
import { ExpensePlanFormComponent } from './multi-year-plan/components/plano-de-contas/expense-plan/expense-plan-form/expense-plan-form.component'
import { ExpensePlanListComponent } from './multi-year-plan/components/plano-de-contas/expense-plan/expense-plan-list/expense-plan-list.component'
import { LoadStandardExpensePlanComponent } from './multi-year-plan/components/plano-de-contas/expense-plan/expense-plan-list/load-standard-expense-plan/load-standard-expense-plan.component'
import { RevenuePlanFormComponent } from './multi-year-plan/components/plano-de-contas/revenue-plan/revenue-plan-form/revenue-plan-form.component'
import { LoadStandardRevenuePlanComponent } from './multi-year-plan/components/plano-de-contas/revenue-plan/revenue-plan-list/load-standard-revenue-plan/load-standard-revenue-plan.component'
import { RevenuePlanListComponent } from './multi-year-plan/components/plano-de-contas/revenue-plan/revenue-plan-list/revenue-plan-list.component'
import { RevenuePlanSourceListComponent } from './multi-year-plan/components/plano-de-contas/revenue-plan/revenue-plan-source-list/revenue-plan-source-list.component'
import { VersionFormComponent } from './multi-year-plan/components/version/version-form/version-form.component'
import { ActionsComponent } from './multi-year-plan/components/version/version-list/actions/actions.component'
import { AnnualShareApplicationComponent } from './multi-year-plan/components/version/version-list/actions/annual-share-application/annual-share-application.component'
import { IndicatorsComponent } from './multi-year-plan/components/version/version-list/indicators/indicators.component'
import { VersionListComponent } from './multi-year-plan/components/version/version-list/version-list.component'
import { MovimentoComponent } from './multi-year-plan/programs/movimento/movimento.component'
import { ObjetivoComponent } from './multi-year-plan/programs/objetivo/objetivo.component'
import { ProgramsComponent } from './multi-year-plan/programs/program-list/programs.component'
import { ProgramComponent } from './multi-year-plan/programs/program/program.component'
import { ProgramaIndicadorComponent } from './multi-year-plan/programs/programa-indicador/programa-indicador.component'
import { OrgansFormComponent } from './organs-unities/organs-form/organs-form.component'
import { OrgansListComponent } from './organs-unities/organs-list/organs-list.component'
import { UnitiesComponent } from './organs-unities/unities/unities.component'
import { UnityFormComponent } from './organs-unities/unity-form/unity-form.component'
import { PlaceFormComponent } from './place/place-form/place-form.component'
import { PlaceListComponent } from './place/place-list/place-list.component'
import { PlanningRoutingModule } from './planning-routing.module'
import { PlanningComponent } from './planning.component'
import { ResourceSourceSearchComponent } from './shared/searchs/resource-source-search/resource-source-search.component';
import { ExpenseGroupReleaseListComponent } from './expense-group/expense-group-release/expense-group-release-list/expense-group-release-list.component';
import { ExpenseGroupReleaseFormComponent } from './expense-group/expense-group-release/expense-group-release-form/expense-group-release-form.component';
import { AnnualGoalsFormComponent } from './budget-guidelines-law/annual-goals/annual-goals-form/annual-goals-form.component'
import { AnnualGoals2020FormComponent } from './budget-guidelines-law/annual-goals-2020/annual-goals-2020-form/annual-goals-2020-form.component';
import { PlaceExerciseListComponent } from './place/place-exercise/place-exercise-list/place-exercise-list.component';
import { PlaceExerciseFormComponent } from './place/place-exercise/place-exercise-form/place-exercise-form.component';
import { GenericRevenuePlanSearchComponent } from './shared/searchs/generic-revenue-plan-search/generic-revenue-plan-search.component'
import { PublicationNoteFieldModule } from '@pages/shared/components/publication-note-field/publication-note-field.module'
import { PlanOptionsModule } from '@pages/shared/components/plan-options/plan-options.module';
import { AccountingPlanFromToListComponent } from './multi-year-plan/components/plano-de-contas/accounting-plan/accounting-plan-from-to/accounting-plan-from-to-list/accounting-plan-from-to-list.component';
import { AccountingPlanFromToFormComponent } from './multi-year-plan/components/plano-de-contas/accounting-plan/accounting-plan-from-to/accounting-plan-from-to-form/accounting-plan-from-to-form.component'
import { DefaultSearchDialogModule } from './shared/searchs/default-search-dialog/default-search-dialog.module';
import { DialogComponent } from './shared/helpers/dialog/dialog.component'
import { UserLogModule } from './shared/user-log/user-log.module'


@NgModule({
  declarations: [
    PlanningComponent,
    OrgansListComponent,
    MultiYearPlanComponent,
    VersionListComponent,
    ProgramsComponent,
    UnitiesComponent,
    IndicatorsComponent,
    AnnualShareApplicationComponent,
    ActionsComponent,
    OrgansFormComponent,
    UnityFormComponent,
    PlaceListComponent,
    PlaceFormComponent,
    AccountingPlanListComponent,
    AccountingPlanFormComponent,
    RevenuePlanListComponent,
    RevenuePlanFormComponent,
    ExpensePlanListComponent,
    ExpensePlanFormComponent,
    LoadStandardPlanComponent,
    LoadStandardExpensePlanComponent,
    RevenuePlanSourceListComponent,
    LoadStandardRevenuePlanComponent,
    ResourceSourceSearchComponent,
    TaxRiskFormComponent,
    TaxRiskListComponent,
    DialogTaxRiskComponent,
    LdoTaxRiskFormComponent,
    ProgramComponent,
    MovimentoComponent,
    VersionFormComponent,
    ExpenseGroupListComponent,
    ExpenseGroupFormComponent,
    ProjectActivityListComponent,
    ProjectActivityFormComponent,
    DialogProjectActivityComponent,
    MonthlyLiberationListComponent,
    MonthlyLiberationFormComponent,
    ObjetivoComponent,
    ExpenseAccountGroupListComponent,
    ExpenseAccountGroupFormComponent,
    ProgramaIndicadorComponent,
    FinancialTransferBudgetForecastListComponent,
    FinancialTransferBudgetForecastFormComponent,
    ManagePercentageFormComponent,
    ExpenseGroupReleaseListComponent,
    ExpenseGroupReleaseFormComponent,
    AnnualGoalsFormComponent,
    AnnualGoals2020FormComponent,
    PlaceExerciseListComponent,
    PlaceExerciseFormComponent,
    GenericRevenuePlanSearchComponent,
    AccountingPlanFromToListComponent,
    AccountingPlanFromToFormComponent,
    DialogComponent,
  ],
  imports: [
    CommonModule,
    PlanningRoutingModule,
    CommonToolsModule,
    DxCheckBoxModule,
    DxNumberBoxModule,
    NbInputModule,
    NbSelectModule,
    NbFormFieldModule,
    PublicationNoteFieldModule,
    PlanOptionsModule,
    DefaultSearchDialogModule,
    UserLogModule
  ],
})
export class PlanningModule {}
