<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col col-12 col-md-3">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="codigo"
          name="Código"
          label="Código*"
          placeholder="Código"
          type="number"
          [readonly]="uuid"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-9">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="fontePadraoTce"
          label="Fonte padrão TCE*"
          placeholder="Fonte padrão TCE"
          formControlName="fontePadraoTce"
          [dataSource]="fontePadraoTceData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="origem"
          label="Origem*"
          placeholder="Origem"
          formControlName="origemRecursoTce"
          [dataSource]="origemRecursoTceData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="aplicacao"
          label="Aplicação*"
          placeholder="Aplicação"
          formControlName="aplicacaoRecursoTce"
          [dataSource]="aplicacaoRecursoTceData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="desdobramento"
          label="Desdobramento*"
          placeholder="Desdobramento"
          formControlName="desdobramentoFonteTce"
          [dataSource]="desdobramentoFonteTceData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="detalhamento"
          label="Detalhamento*"
          placeholder="Detalhamento"
          formControlName="detalhamentoFonteTce"
          [dataSource]="detalhamentoFonteTceData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <div>
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="nome"
            name="Nome"
            label="Nome*"
            placeholder="Nome"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="fontePadraoStn"
          label="Fonte padrão STN"
          placeholder="Fonte padrão STN"
          formControlName="fontePadraoStn"
          [dataSource]="fontePadraoStnData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
      <div class="col">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="marcadorStn"
          label="Marcador STN"
          placeholder="Marcador STN"
          formControlName="marcadorStn"
          [dataSource]="marcadorStnData"
          [displayExpr]="codeNameDisplay"
          valueExpr="uuid"
        ></eqp-nebular-select>
      </div>
    </div>
    <div class="row">
      <div class="col col-12 col-md-3">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="dataInicial"
          name="dataInicial"
          label="Data inicial*"
          placeholder="Data inicial"
          [errorMessage]="'É obrigatório preencher a data inicial'"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-3">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="dataFinal"
          name="dataFinal"
          label="Data final"
          placeholder="Data final"
          [errorMessage]="'É obrigatório preencher a data final'"
        >
        </eqp-nebular-input>
      </div>
      <div class="col col-12 col-md-6">
        <eqp-nebular-toggle
          formControlName="flagRetencao"
          label="Retenção"
          title="Retenção"
        ></eqp-nebular-toggle>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar(null)">
          Voltar
        </button>
        <button
          *ngIf="model.get('uuid').value && nivelPermissao === 'FULL'"
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          *ngIf="
            (model.get('uuid').value && nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL' 
          "
          type="button"
          [disabled]="model.invalid"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Confirmar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
