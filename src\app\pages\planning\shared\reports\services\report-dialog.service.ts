import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NbDialogService } from '@nebular/theme';
import { finalize, take } from 'rxjs/operators';
import { ReportService } from './report.service';

@Injectable({
  providedIn: 'root',
})
export class ReportDialogService extends ReportService {
  constructor(protected http: HttpClient, protected dialog: NbDialogService) {
    super(http, dialog);
  }

  getLoading() {
    return this.loading.asObservable();
  }

  getReportBase64(formData: any, pageTitle: string, uri: string, habilitarPost?: boolean) {
    const viewReport = formData.visualizar ?? true;
    const isPDF = formData.tipo === 'PDF';

    delete formData.visualizar;
    if (isPDF && viewReport) {
      this.abrirRelatorioNovaAba(formData, uri, habilitarPost);
    } else {
      formData.tipo = viewReport ? formData.tipo : 'PDF';
      this.loading.next(true);
      this.generateReport(formData, uri, '', habilitarPost)
        .pipe(
          take(1),
          finalize(() => this.loading.next(false)),
        )
        .subscribe((res) => {
          this.viewReport(pageTitle, res.dados, viewReport);
        });
    }
  }
}
