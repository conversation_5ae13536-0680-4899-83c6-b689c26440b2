import { DefaultColumnSearchInterface } from '../../interfaces/default-search'

interface SearchColumnsInterface {
  [campo: string]: DefaultColumnSearchInterface[]
}
const BOOLEAN_DATA: { value: string; key: string }[] = [
  {
    value: 'Sim',
    key: 'S',
  },
  {
    value: 'Não',
    key: 'N',
  },
]

export const SEARCH_COLUMNS: SearchColumnsInterface = {
  initialExpenseForecastSourceColumns: [
    {
      dataField: 'codigo',
      caption: 'Conta despesa',
      dataType: 'number',
    },
    {
      dataField: 'previsaoInicialDespesa.orgao.codigo',
      caption: 'Orgão',
      dataType: 'number',
      format: {
        type: 'decimal',
        precision: 2,
      },
    },
    {
      dataField: 'previsaoInicialDespesa.unidade.codigo',
      caption: 'Unidade',
      dataType: 'number',
      format: {
        type: 'decimal',
        precision: 3,
      },
    },
    {
      dataField: 'previsaoInicialDespesa.funcao.codigo',
      caption: 'Função',
      dataType: 'number',
      format: {
        type: 'decimal',
        precision: 2,
      },
    },
    {
      dataField: 'previsaoInicialDespesa.subfuncao.codigo',
      caption: 'Subfunção',
      dataType: 'number',
      format: {
        type: 'decimal',
        precision: 3,
      },
    },
    {
      dataField: 'previsaoInicialDespesa.programa.codigo',
      caption: 'Programa',
      dataType: 'number',
      format: {
        type: 'decimal',
        precision: 4,
      },
    },
    {
      dataField: 'previsaoInicialDespesa.projetoAtividade.tipo',
      caption: 'Tipo',
      dataType: 'number',
    },
    {
      dataField: 'previsaoInicialDespesa.projetoAtividade.ordem',
      caption: 'Ordem',
      dataType: 'number',
      format: {
        type: 'decimal',
        precision: 3,
      },
    },
    {
      dataField: 'previsaoInicialDespesa.projetoAtividade.nome',
      caption: 'Projeto atividade',
      dataType: 'string',
    },
    {
      dataField: 'previsaoInicialDespesa.planoDespesa.codigo',
      caption: 'Natureza',
    },
    {
      dataField: 'fonteRecurso.codigo',
      caption: 'Fonte recurso',
      dataType: 'number',
    },
  ],
  codeNameColumns: [
    {
      caption: 'Código',
      dataField: 'codigo',
    },
    {
      caption: 'Nome',
      dataField: 'nome',
    },
  ],
  actionYearColumns: [
    {
      caption: 'Código',
      dataField: 'ppaAcao.codigo',
    },
    {
      caption: 'Nome',
      dataField: 'ppaAcao.nome',
    },
  ],
  defaultColumns: [
    {
      caption: 'Código',
      dataField: 'codigo',
    },
    {
      caption: 'Nome',
      dataField: 'nome',
    },
  ],
  accountingPlanColumns: [
    {
      caption: 'Cód. Reduzido',
      dataField: 'codigoReduzido',
    },
    {
      caption: 'Código',
      dataField: 'codigo',
    },
    {
      caption: 'Nome',
      dataField: 'nome',
    },
    {
      caption: 'Analítica',
      dataField: 'analitica',
      lookup: {
        dataSource: BOOLEAN_DATA,
        displayExpr: 'value',
        valueExpr: 'key',
      },
    },
  ],
  eventColumns: [
    {
      caption: 'Número',
      dataField: 'numero',
    },
    {
      caption: 'Nome',
      dataField: 'nome',
    },
  ],
  accountingEventColumns: [
    {
      caption: 'Número',
      dataField: 'eventoContabil.numero',
    },
    {
      caption: 'Número configuração',
      dataField: 'numero',
    },
    {
      caption: 'Nome',
      dataField: 'eventoContabil.nome',
    },
    {
      caption: 'Débito',
      dataField: 'planoContabilDebito.codigo',
    },
    {
      caption: 'Nome',
      dataField: 'planoContabilDebito.nome',
    },
    {
      caption: 'Crédito',
      dataField: 'planoContabilCredito.codigo',
    },
    {
      caption: 'Nome',
      dataField: 'planoContabilCredito.nome',
    },
  ],
  neighborhoodColumns: [
    {
      caption: 'Bairro',
      dataField: 'nome',
    },
    {
      caption: 'Cidade',
      dataField: 'cidade.nome',
    },
    {
      caption: 'CEP',
      dataField: 'cidade.cep',
    },
    {
      caption: 'Estado',
      dataField: 'cidade.estado.nome',
    },
    {
      caption: 'UF',
      dataField: 'cidade.estado.sigla',
    },
    {
      caption: 'País',
      dataField: 'cidade.estado.pais.nome',
    },
  ],
  cepColumns: [
    { 
      dataField: 'cep',
      caption: 'CEP'
    },
    { 
      dataField: 'endereco', 
      caption: 'Logradouro' 
    },
    { dataField: 'bairro.nome', 
      caption: 'Bairro' 
    },
    { 
      dataField: 'bairro.cidade.nome', 
      caption: 'Cidade' 
    },
    { 
      dataField: 'bairro.cidade.estado.nome', 
      caption: 'Estado' 
    },
  ],
  serverColumns: [
    {
      caption: 'Código',
      dataField: 'pessoaCodigo',
      dataType: 'number',
    },
    {
      caption: 'Matrícula',
      dataField: 'matricula',
      dataType: 'number',
    },
    {
      caption: 'Nome',
      dataField: 'nomePessoa',
    }
  ],
  revenuePlanColumns: [
    {
      dataField: 'codigo',
      caption: 'Código',
    },
    {
      dataField: 'nome',
      caption: 'Nome',
    },
    {
      dataField: 'analitica',
      caption: 'Analítica',
      lookup: {
        dataSource: BOOLEAN_DATA,
        displayExpr: 'value',
        valueExpr: 'key'
      }
    },
    {
      dataField: 'flagTemRateio',
      caption: 'Tem rateio',
      lookup: {
        dataSource: BOOLEAN_DATA,
        displayExpr: 'value',
        valueExpr: 'key'
      }
    },
    {
      dataField: 'tipoDiarioArrecadacao.nome',
      caption: 'Tipo de arrecadação',
    },
  ],
}
