import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { CrudService } from '@common/services/crud.service';
import { NbDialogRef } from '@nebular/theme';
import { MenuService } from '@pages/menu.service';
import { ProgramInterface } from '@pages/planning/multi-year-plan/interfaces/program';
import DataSource from 'devextreme/data/data_source';

@Component({
  selector: 'eqp-program-search',
  templateUrl: './program-search.component.html',
  styleUrls: ['./program-search.component.scss']
})
export class ProgramSearchComponent
extends BaseTelasComponent implements OnInit {
  pageTitle: string = 'Selecionar programa'
  loading: boolean

  selectedRowKeys: string[] = []

  dataSource: DataSource

  @Input() uri: string = 'ppa_programa/paginado'
  @Input() isMultiple: boolean = false
  @Input() paginated: boolean = true

  constructor(
    public router: Router,
    private service: CrudService,
    public menuService: MenuService,
    private dialogRef: NbDialogRef<ProgramSearchComponent>,
  ) {
    super(menuService, router)
    this.permissao(this.uri)
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  async fetchGrid() {
    if(this.paginated) {
      this.dataSource = new DataSource({
        store: this.service.getDataSourceFiltro('uuid', this.uri, 10),
        paginate: true,
        pageSize: 10,
      })
    } else {
      const programData = await this.service
      .getSingleData<ProgramInterface[]>(this.uri)
      .toPromise()

      this.dataSource = new DataSource({
        store: {
          data: programData?.dados || [],
          type: 'array',
          key: 'uuid',
        },
      })
    }
  }

  async confirm() {
    if (this.isMultiple) {
      const items = []
      const dataSource = await this.dataSource.store().load()
      this.selectedRowKeys.forEach(key => {
        const item = dataSource.find(item => item.uuid === key)
        items.push(item)
      })
      this.dialogRef.close(items)
    } else {
      const item = await this.dataSource.store().byKey(this.selectedRowKeys[0])
      this.dialogRef.close(item)
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  onSelectionChanged(event: any) {
    this.selectedRowKeys = event.selectedRowKeys
  }

}
