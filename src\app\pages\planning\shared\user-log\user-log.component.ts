import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { LogInterface } from '../interfaces/log';

@Component({
  selector: 'eqp-user-log',
  templateUrl: './user-log.component.html',
  styleUrls: ['./user-log.component.scss']
})
export class UserLogComponent implements OnInit {

  @Input() logData: LogInterface

  model: FormGroup

  constructor(private builder: FormBuilder) { }

  ngOnInit(): void {
    this.model = this.getNewModel()
    this.model.patchValue(this.logData)
  }

  private getNewModel(){
    return this.builder.group({
      logCreate: [null],
      logUpdate: [null],
    })
  }

}
