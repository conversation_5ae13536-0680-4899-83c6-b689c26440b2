import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuResponseInterface } from '@core/interfaces/menu';
import { MenuStorageService } from '@core/services/reports/menu-storage.service';
import { Subject } from 'rxjs';
import { filter, take } from 'rxjs/operators';

@Component({
  selector: 'eqp-reports',
  template: `
  <eqp-standard-page [mainTitle]="currentMenu?.nome" [nbSpinner]="loading">
    <ng-container *ngIf="currentMenu; else notFound">
      <router-outlet></router-outlet>
    </ng-container>

    <ng-template #notFound>
      <div *ngIf="!checkReportInProgress" class="text-center">
        <h1>Relatório não encontrado</h1>
        <p>Nenhum relatório foi encontrado para a URL informada</p>
      </div>
    </ng-template>
  </eqp-standard-page>
  `,
})
export class ReportsComponent implements OnInit, OnDestroy {

  public currentMenu?: MenuResponseInterface;
  public loading = true;
  public checkReportInProgress = true;
  private subscribe$ = new Subject();

  constructor(
    private _menuStorageService: MenuStorageService,
    private _router: Router,
  ) { }

  ngOnInit(): void {
    this.getCurrentMenu();
  }

  ngOnDestroy(): void {
      this.subscribe$.next();
      this.subscribe$.complete();
  }

  private getCurrentMenu(): void {
    this._menuStorageService.get()
      .pipe(
        filter(el => el.allMenu.length > 0),
        take(1)
      ).subscribe(res => {
        this.currentMenu = this.getCurrentReport(res.allMenu);
        this.loading = false;

        if(!this.currentMenu) {
          this.checkReportInProgress = false;
        }
      })
  }

  private getCurrentReport(reports: Array<MenuResponseInterface>): MenuResponseInterface | undefined {
    const currentURLReport = this._router.url.split('?')[0].match(/\/relatorio\/(.*)/);
    const currentURl = currentURLReport ? currentURLReport[1] : '';
    const reportWithLink = reports.filter(el => el.link && el.nivel === 3 && currentURl.includes(this.removeTrailingSlash(el.link)));
    const reportResponse = reportWithLink.length === 1 ? reportWithLink[0] : reportWithLink.find(el => this.checkUrlEquals(currentURl.split('/'), el.link.split('/')));
    return reportResponse;
  }

  private removeTrailingSlash(url: string): string {
    if (url.endsWith('/')) {
      return url.slice(0, -1);
    }
    return url;
  }

  private checkUrlEquals(currentUrl: Array<string>, reportUrl: Array<string>){
    return reportUrl.every((value, index) => value == currentUrl[index]);
  }

}
