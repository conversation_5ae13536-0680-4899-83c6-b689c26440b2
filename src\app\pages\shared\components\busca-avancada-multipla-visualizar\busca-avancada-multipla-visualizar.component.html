<eqp-nebular-dialog
  [dialogTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightDenyButtonVisible]="true"
  [rightDenyButtonText]="'Remover'"
  [rightDenyButtonDisabled]="linhasSelecionadas.length === 0"
  (rightDenyButtonEmitter)="modalConfirmar()"
>
  <ng-container>
    <dx-data-grid
      [dataSource]="dataSource"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="{ enabled: true, text: 'Carregando...' }"
      [columnHidingEnabled]="false"
      [remoteOperations]="false"
      keyExpr="uuid"
      [(selectedRowKeys)]="linhasSelecionadas"
    >
      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [infoText]="'Página {0} de {1} ({2} itens)'"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="true"
        [allowedPageSizes]="[10, 25, 50, 100]"
      >
      </dxo-pager>

      <dxo-selection
        selectAllMode="allPages"
        showCheckBoxesMode="always"
        [mode]="'multiple'"
      ></dxo-selection>

      <dxo-header-filter
        [visible]="true"
        [allowSearch]="true"
      ></dxo-header-filter>
      <dxo-filter-row [visible]="true" [applyFilter]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser
        [enabled]="true"
        [mode]="'dragAndDrop'"
      ></dxo-column-chooser>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        *ngFor="let column of columns"
        [caption]="column.caption"
        [dataField]="column.dataField"
        [width]="column.width"
        [alignment]="column.alignment"
        [calculateFilterExpression]="
          customFilterExpression ||
          column?.calculateFilterExpression ||
          column?.defaultCalculateFilterExpression
        "
      ></dxi-column>

      <dxo-toolbar>
        <dxi-item location="after">
          <div *dxTemplate>
            <dx-button
              stylingMode="outlined"
              type="normal"
              nbTooltip="Remover todos"
              icon="trash"
              (onClick)="removerTodosRegistros()"
            >
            </dx-button>
          </div>
        </dxi-item>
      </dxo-toolbar>
    </dx-data-grid>
  </ng-container>
</eqp-nebular-dialog>
