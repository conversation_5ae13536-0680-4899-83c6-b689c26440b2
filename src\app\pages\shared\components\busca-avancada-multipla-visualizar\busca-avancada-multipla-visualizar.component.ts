import { Component, Input, OnInit } from '@angular/core';
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import DataSource from 'devextreme/data/data_source';
import { from } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'eqp-busca-avancada-multipla-visualizar',
  templateUrl: './busca-avancada-multipla-visualizar.component.html',
  styleUrls: ['./busca-avancada-multipla-visualizar.component.scss']
})
export class BuscaAvancadaMultiplaVisualizarComponent implements OnInit {
  public pageTitle = 'Registros selecionados';
  public loading = false;
  public linhasSelecionadas: string[] = [];
  @Input() dataSource: DataSource;
  @Input() columns: any;
  @Input() customFilterExpression: Function

  constructor(
    private dialogRef: NbDialogRef<BuscaAvancadaMultiplaVisualizarComponent>,
    private dialogService: NbDialogService,
  ) {}


  ngOnInit(): void {
  }

  onSelectionChanged(e) {
    this.linhasSelecionadas = e.selectedRowKeys;
  }

  cancel() {
    this.dialogRef.close();
  }

  public modalConfirmar() {
    const ref = this.dialogService.open(ConfirmationComponent, {
      context: {
        confirmationContent: {
          body: 'Confirma a remoção do(s) registro(s) selecionado(s)?',
        },
        dialogSize: 'small',
        exiberIcones: false
      },
      closeOnBackdropClick: false,
      closeOnEsc: false,
    });

    ref.onClose.pipe(filter((res) => res)).subscribe(() => {
      this.remover();
    });
  }

  public removerTodosRegistros() {
    from(this.dataSource.store().load())
      .pipe(filter((res) => res.length > 0))
      .subscribe((dados) => {
        for (const item of dados) {
          this.dataSource.store().remove(item.uuid);
        }
        this.dataSource.reload();
      });
  }

  private remover() {
    for (const chave of this.linhasSelecionadas) {
      this.dataSource.store().remove(chave);
    }
    this.dataSource.reload();
    this.linhasSelecionadas = [];
  }

}
