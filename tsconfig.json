{"compileOnSave": false, "compilerOptions": {"baseUrl": "./src", "importHelpers": true, "module": "es2020", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2015", "typeRoots": ["node_modules/@types"], "lib": ["es2017", "dom"], "plugins": [{"name": "tslint-language-service"}], "paths": {"*": ["types/*"], "@angular/*": ["../node_modules/@angular/*"], "@nebular/*": ["../node_modules/@nebular/*"], "@theme/*": ["app/@theme/*"], "@core/*": ["app/@core/*"], "@common/*": ["app/@common/*"], "@dialogs/*": ["app/@common/dialogs/*"], "@interfaces/*": ["app/@common/interfaces/*"], "@pipes/*": ["app/@common/pipes/*"], "@services/*": ["app/@common/services/*"], "@misc/*": ["app/@common/misc/*"], "@systems-id/*": ["app/@common/systems-id/*"], "@design-tools/*": ["app/@design-tools/*"], "@favorites/*": ["app/@common/modules/favorites/*"], "@guards/*": ["app/guards/*"], "@notifications/*": ["app/@common/modules/notifications/*"], "@system-selector/*": ["app/@common/modules/system-selector/*"], "@pages/*": ["app/pages/*"], "@environments/*": ["environments/*"], "jszip": ["node_modules/jszip/dist/jszip.min.js"]}}}